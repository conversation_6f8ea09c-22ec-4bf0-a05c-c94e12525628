image: atlassian/default-image:3

pipelines:
  branches:
    development:
      - step:
          name: Associate commits to Sentry release
          script:
            - pipe: sentryio/sentry-new-release:0.3.0
              variables:
                SENTRY_AUTH_TOKEN: $SENTRY_AUTH_TOKEN
                SENTRY_ORG: '365werknl'
                SENTRY_PROJECT: 'jet-service'
                ENVIRONMENT: 'development'
    master:
      - step:
          name: Associate commits to Sentry release
          script:
            - pipe: sentryio/sentry-new-release:0.3.0
              variables:
                SENTRY_AUTH_TOKEN: $SENTRY_AUTH_TOKEN
                SENTRY_ORG: '365werknl'
                SENTRY_PROJECT: 'jet-service'
                ENVIRONMENT: 'production'
