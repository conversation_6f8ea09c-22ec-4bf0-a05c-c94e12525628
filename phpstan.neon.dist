includes:
#    - vendor/spaze/phpstan-disallowed-calls/disallowed-dangerous-calls.neon
#    - vendor/spaze/phpstan-disallowed-calls/disallowed-execution-calls.neon
#    - vendor/spaze/phpstan-disallowed-calls/disallowed-insecure-calls.neon
parameters:
    tmpDir: .cache/phpstan
    paths:
        - app
        - tests
        - database/factories
        - routes

#    type_coverage:
#        declare: 7
#        return: 94
#        param: 94
#        property: 99
#        constant: 7

    # Level 9 is the highest level
    level: 5
    strictRules:
        allRules: false
    noEnvCallsOutsideOfConfig: true
    ignoreErrors:
        - identifier: property.phpDocType
    treatPhpDocTypesAsCertain: false
