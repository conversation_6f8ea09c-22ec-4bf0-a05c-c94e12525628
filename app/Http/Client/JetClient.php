<?php

namespace App\Http\Client;

use App\DTO\JetOauthDTO;
use App\DTO\JetOauthTokenDTO;
use App\Enums\JetEntity;
use Illuminate\Http\Client\Factory;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Throwable;

/**
 * @mixin Factory
 */
class JetClient extends Factory
{
    private PendingRequest $httpClient;

    public function client(): JetClient
    {
        $client = fn () => Http::baseUrl(config('services.jet.api_url'))
            ->withHeaders([
                'Accept' => 'application/json',
                'Content-Type' => 'application/json',
            ])
            ->withToken($this->fetchAccessToken())
            ->throw();

        try {
            $this->httpClient = $client();
        } catch (Throwable $exception) {
            Log::debug('error setting up jet client, retrying', [
                'message' => $exception->getMessage(),
            ]);
            $this->httpClient = $client();
        }

        return $this;
    }

    private function fetchAccessToken(): string
    {
        return Cache::remember('jet-oauth-token', 50, function () {
            $oAuth = JetOauthDTO::from(config('services.jet.oauth'));

            $response = Http::baseUrl(config('services.jet.api_url'))
                ->withHeader('Accept', 'application/json')
                ->throw()
                ->asForm()
                ->post(config('services.jet.oauth_url'), $oAuth->all());

            return JetOauthTokenDTO::from($response->json())->access_token;
        });
    }

    public function describe(JetEntity $entity): Response
    {
        return $this->httpClient->get($entity->apiUrl() . '/describe');
    }

    public function getContentDocumentIdByContentVersion(string $id): Response
    {
        return $this->httpClient->get(sprintf(
            '/services/data/%s/query/?q=select contentdocumentid from contentversion where id = \'%s\'',
            JetEntity::API_VERSION,
            $id
        ));
    }

    public function createDocumentData(string $title, string $path, string $location, string $data, bool $isMutable = false)
    {
        return $this->httpClient->post(JetEntity::CONTENT_VERSION->apiUrl(), [
            'Title' => $title,
            'PathOnClient' => $path,
            'ContentLocation' => $location,
            'VersionData' => $data,
            'IsMajorVersion' => ! $isMutable,
        ]);
    }

    public function __call($method, $parameters)
    {
        return $this->httpClient->{$method}(...$parameters);
    }
}
