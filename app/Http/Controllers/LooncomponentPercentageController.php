<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreLooncomponentPercentageRequest;
use App\Http\Requests\UpdateLooncomponentPercentageRequest;
use App\Models\LooncomponentPercentage;

class LooncomponentPercentageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreLooncomponentPercentageRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(LooncomponentPercentage $looncomponentPercentage)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(LooncomponentPercentage $looncomponentPercentage)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateLooncomponentPercentageRequest $request, LooncomponentPercentage $looncomponentPercentage)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(LooncomponentPercentage $looncomponentPercentage)
    {
        //
    }
}
