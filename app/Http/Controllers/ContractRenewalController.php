<?php

namespace App\Http\Controllers;

use App\Models\ContractRenewal;
use App\Models\JetApplicantRenewalStatus;
use Carbon\Carbon;
use Illuminate\Http\Request;

class ContractRenewalController extends Controller
{

    public function resetRenewal(?JetApplicantRenewalStatus $renewalStatus)
    {
        if (! $renewalStatus) return false;

        $renewalStatus->step_in_process = 0;
        $renewalStatus->notes = 'Verlenging teruggedraaid op ' . Carbon::today()->format('d-m-Y');
        return $renewalStatus->save();
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(ContractRenewal $contractRenewal)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ContractRenewal $contractRenewal)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ContractRenewal $contractRenewal)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ContractRenewal $contractRenewal)
    {
        //
    }
}
