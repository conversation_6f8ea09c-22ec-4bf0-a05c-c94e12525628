<?php

namespace App\Http\Controllers;

use App\Actions\JetActions;
use App\DTO\JetApplicantDTO;
use App\Models\ContractRenewal;
use App\Models\EasyflexContracts;
use App\Models\JetApplicant;
use App\Models\JetApplicantRenewalStatus;
use App\Services\CommunicationService;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use Laravel\Octane\Exceptions\DdException;

class EasyflexContractsController extends Controller
{
    private EmployeeOffboardingController $employeeOffboardingController;
    private ContractController $contractController;
    private CommunicationService $communicationService;

    public function __construct()
    {
        $this->employeeOffboardingController = new EmployeeOffboardingController();
        $this->contractController = new ContractController();
        $this->communicationService = new CommunicationService();
    }

    /**
     * @param $record
     * @return Notification|true
     * @throws DdException
     * @throws \Exception
     */
    public function renewContract($record, $isApplicant = false)
    {
        if ($isApplicant) {
            $applicant = $record;
            $record = EasyflexContracts::where('easyflex_registratienummer', $record->easyflex_id)
                ->orderBy('fw_arbeidscontract_contracteinde', 'desc')
                ->first();
        } else {
            $applicant = JetApplicant::where('easyflex_id', $record->easyflex_registratienummer)->first();
        }

        $contractStartDate = Carbon::createFromFormat('Y-m-d', $record->fw_arbeidscontract_contracteinde)->addDay()->format('Y-m-d');
        $onbepaaldeTijd = false;
        $english = "0";
        $contractEndDate = Carbon::parse($contractStartDate)->addMonths(12)->subDay()->toDateString();
        $flexappData = $applicant->flexappData;

        //check startdate for wage
        $dob = $flexappData->personalData->date_of_birth;
        $uurloon = JetActions::getUurloon($contractStartDate, 'Courier', $dob);

        ContractRenewal::create([
            'applicant_id' => $applicant->applicant_id,
            'wage_per_hour' => $uurloon,
            'contract_hours' => $record->fw_arbeidscontract_contracturen,
            'is_wage_with_vacation_allowance' => $flexappData->financialDetails?->is_wage_with_vacation_allowance,
            'is_wage_with_vacation_days' => $flexappData->financialDetails?->is_wage_with_vacation_days,
            'is_werkstudent' => $flexappData->info?->twv_is_student,
            'delivery_area' => $applicant->delivery_area,
            'contract_start_date' => $contractStartDate,
            'contract_end_date' => $contractEndDate,
        ]);

        if ($flexappData->identification?->document_type === 'Verblijfsvergunning') {
            // check if valid
            $validUntil = $flexappData->identification->identification_valid_until;

            if (!$validUntil || !Carbon::hasFormat($validUntil, 'd-m-Y')) {
                return Notification::make()
                    ->title('No expire date found for residence permit')
                    ->danger()
                    ->persistent()
                    ->send();
            }

            $validUntilCarbon = Carbon::parse($validUntil);
            if ($validUntilCarbon->lt($contractStartDate)) {
                // Enrollment step: verify ID and Renewal step: residence permit required
                JetActions::timeApplicantRenewalStep($applicant->applicant_id, 1);

                if ($flexappData->info?->twv_required === true) {
                    // check if twv is valid
                    $twvValid = $this->checkTwvValidity($flexappData, $contractStartDate);
                    if ($twvValid) {
                        $this->resetTwv($flexappData, true);
                    }
                }

                $emailData = [
                    'email' => $flexappData->personalData->email,
                    'preferred_language' => $flexappData->applicationProfile->preferred_language,
                    'recruiter_name' => auth()->user()->name
                ];

                $this->communicationService->sendContractRenewalMessage($emailData);

                return Notification::make()
                    ->title('Residence permit has expired')
                    ->danger()
                    ->persistent()
                    ->send();
            }
        }

        // check twv
        if ($flexappData->info?->twv_required) {
            // check if twv is invalid or doesn't have an expiration date
            $twvValid = $this->checkTwvValidity($flexappData, $contractStartDate);
            if (!$twvValid) {
                JetActions::timeApplicantRenewalStep($applicant->applicant_id, 3);
                return Notification::make()
                    ->title('TWV flow started')
                    ->danger()
                    ->persistent()
                    ->send();
            }
        }

        // check aantal maanden werkhistorie
        if ($applicant->number_of_previous_contracts === 3) {
            $contractEndDate = '';
            $onbepaaldeTijd = true;
        }

        if ($flexappData->applicationProfile->language === 'Engels') {
            $english = "1";
        }

        $data = [
            "contract_type" => "renew_contract",
            "function" => "Courier",
            "contract_hours" => $record->fw_arbeidscontract_contracturen,
            "is_wage_with_vacation_allowance" => $flexappData->financialDetails?->is_wage_with_vacation_allowance,
            "is_wage_with_vacation_days" => $flexappData->financialDetails?->is_wage_with_vacation_days,
            "delivery_area" => $applicant->delivery_area,
            "english" => $english,
            "contract_start_date" => $contractStartDate,
            "probation_period" => "0",
            "onbepaalde_tijd" => $onbepaaldeTijd,
            "contract_end_date" => $contractEndDate,
            "wage_per_hour" => $uurloon,
            "total_forms" => [
                0 => "contract",
            ],
        ];

        // make contract based on ef data
        $contract = $this->contractController->generateContract($applicant, $data);

        JetActions::timeApplicantRenewalStep($applicant->applicant_id, 6, true);
        // TODO create contract in easyflex (waiting for ef support response)
        return $contract;
    }

    public function resetTwv($flexappData, $resetIdentification = false)
    {
        $flexappData->info->twv_required = true;
        $flexappData->info->twv_requested = null;
        $flexappData->info->twv_requested_date = null;
        $flexappData->info->twv_approved = null;
        $flexappData->info->twv_approved_date = null;
        $flexappData->info->twv_start_date = null;
        $flexappData->info->twv_number = null;
        $flexappData->info->twv_expiration_date = null;
        if ($resetIdentification === true) {
            $flexappData->info->identification_approved = null;
        }
        $flexappData->info->save();
    }

    public function checkTwvValidity($flexappData, $contractStartDate)
    {
        $twvExpirationDate = $flexappData->info?->twv_expiration_date;

        if (! $twvExpirationDate) {
            return false;
        }

        return Carbon::parse($twvExpirationDate)->gte($contractStartDate);
    }

    public function terminateContract($record, $skipEmail = false): void
    {
        $applicant = JetApplicant::where('easyflex_id', $record->easyflex_registratienummer)->first();

        // start offboarding process
        JetActions::timeApplicantRenewalStep($applicant->applicant_id, 10, true);

        $data = [
            'contract_number' => $record->fw_arbeidscontract_contract,
            'reason' => 'terminate_contract',
            'end_contract_per_date' => $record->fw_arbeidscontract_contracteinde,
            'skip_email' => $skipEmail,
        ];

        $this->employeeOffboardingController->offboardUser($data, $applicant);
    }
}
