<?php

namespace App\Http\Controllers;

use App\Actions\JetActions;
use App\Actions\JetApplicantEnrollmentActions;
use App\Models\Plaatsing;
use App\Models\Urenbestand;
use App\Services\FusionauthService;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PlaatsingController extends Controller
{
    public function storePlaatsing($import_id): bool
    {
        $urenbestanden = Urenbestand::select('driver_id', 'city')
            ->where('import_id', '=', $import_id)
            ->groupBy('driver_id', 'city')
            ->get();

        foreach($urenbestanden as $urenbestand){
            if (!isset($urenbestand->applicant) || !isset($urenbestand->applicant->applicant_id) || !isset($urenbestand->applicant->easyflex_id)) {
                continue;
            }

            $plaatsing = Plaatsing::where('applicant_id', $urenbestand->applicant->applicant_id)
                ->where('kpcode', ucfirst($urenbestand->city))
                ->exists();

            if (!$plaatsing) {
                $applicant_id = $urenbestand->applicant->applicant_id;
                $easyflex_id = $urenbestand->applicant->easyflex_id;

                $payload = [
                    'registratienummer' => $easyflex_id,
                    'WKM_id' => 7,
                    'queryEasyflex' => true
                ];

                $response = Http::withHeaders([
                    'Accept' => 'application/vnd.api+json',
                    'Content-Type' => 'application/vnd.api+json'
                ])
                    ->withToken(FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.easyflex_service')))
                    ->baseUrl(config('services.easyflex_service.url'))
                    ->post('/get_plaatsingen', $payload);

                if (!$response->successful()) {
                    Log::error('easyflex get_plaatsingen call failed', [
                        'responseData' => $response->json(),
                        'params' => $payload,
                        'httpCode' => $response->status(),
                    ]);

                    continue;
                }

                $data = json_decode($response->body());
                foreach($data->data as $plaatsing)
                {
                    $existingPlaatsing = Plaatsing::where('plaatsing_id', $plaatsing->rl_plaatsingen_plaatsingnummer)->first();
                    if(!$existingPlaatsing){
                        Plaatsing::create([
                            'plaatsing_id' => $plaatsing->rl_plaatsingen_plaatsingnummer,
                            'applicant_id' => $applicant_id,
                            'kpcode' => $plaatsing->rl_plaatsingen_kostenplaatscode,
                        ]);
                        $step10FinishedAt = JetActions::timeApplicantEnrollmentStep($applicant_id, 10, true);
                        JetApplicantEnrollmentActions::finishStep($applicant_id, 16, false, $step10FinishedAt);
                    }
                }
            }
        }
        return true;
    }
}
