<?php

namespace App\Http\Controllers;

use App\Models\Export;
use App\Models\JetApplicant;
use App\Models\LoonComponentExceptions;
use App\Models\LooncomponentPercentage;
use App\Models\Plaatsing;
use App\Models\Urenbestand;
use App\Services\EasyflexService;
use Carbon\Carbon;
use Filament\Actions\Imports\Models\Import;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Spatie\SimpleExcel\SimpleExcelWriter;

class ExportController extends Controller
{
    public string $loonschema = 'BEZORGER';
    public string $tarief = 'JUST EAT';
    public string $cao_schaal = '1';
    public string $functie = '';
    public int $chunkSize = 500;
    public mixed $relatienummer;
    public string $environment;
    public $easyflex_service;

    public function __construct(EasyflexService $easyflex_service)
    {
        $this->relatienummer = config('services.easyflex.relatienummer');
        $this->easyflex_service = $easyflex_service;
        $this->environment = config('app.env');
    }

    public function createExport($import_id, $looncomponenttype_id, $filename, $user_id)
    {
        $filelocation = storage_path('app/export_temp/'.$filename);
        $directoryPath = dirname($filelocation);
        if (!File::exists($directoryPath)) {
            File::makeDirectory($directoryPath, 0755, true);
        }

        $csv_writer = SimpleExcelWriter::create($filelocation, '', null, ';');
        $this->getExportData($looncomponenttype_id, $import_id, $csv_writer);
        $csv_writer->close();

        $file_contents = File::get($filelocation);
        Storage::disk('jet')->put($filename, $file_contents);

        $createPlaatsingen = false;
        if($looncomponenttype_id === 99){
            $createPlaatsingen = true;
        }

        $easyflexExport = $this->exportToEasyflex($filename, $user_id, $import_id, $createPlaatsingen);
        File::delete($filelocation);

        return $easyflexExport;
    }

    public function downloadData($record)
    {
        if($record === null){
            $drivers = Urenbestand::pluck('driver_id')->unique();
        }else{
            $import_id = $record->id;
            $drivers = Urenbestand::where('import_id', $import_id)->pluck('driver_id')->unique();
        }

        $filename = 'export_bestand_'.Carbon::now()->timestamp.'.xlsx';
        $filelocation = storage_path('app/export_temp/'.$filename);

        $csv_writer = SimpleExcelWriter::create($filelocation, '', null, ';');
        $csv_writer->addHeader(['scoober_id', 'easyflex_id', 'geboortedatum', 'functie', 'datum eerste uren']);

        $i = 0;
        foreach($drivers as $driver){
            $applicant = JetApplicant::where('scoober_id', $driver)->first();
            $first_hours = Urenbestand::where('driver_id', $driver)
                ->where('loon_component_id', 1)
                ->orderBy('date', 'asc')
                ->first();

            $first_hours_date = $first_hours ? Carbon::parse($first_hours->date)->format('d-m-Y') : '-';
            $scoober_id = $driver;
            $ef_id = $applicant->easyflex_id ?? 'Geen EF registratienummer gevonden';
            $dob = $applicant->flexappData->personal->date_of_birth ?? $applicant->date_of_birth ?? null;
            $function = $applicant->function ?? 'Courier';
            $csv_writer->addRow([$scoober_id, $ef_id, Carbon::parse($dob)->format('d-m-Y'), $function, $first_hours_date]);
        }
        $csv_writer->close();

        return $filelocation;
    }

    public function getExportData($looncomponenttype_id, $import_id, $csv_writer)
    {
        switch ($looncomponenttype_id) {
            case 3:
                $this->getDeclaratieData($import_id, $csv_writer);
                break;
            case 2:
            case 5:
                $this->getUrenData($import_id, $csv_writer);
                break;
            case 1:
                $this->getAbsenceData($import_id, $csv_writer);
                break;
            case 7:
                $this->getOnbetaaldVerlof($import_id, $csv_writer);
                break;
            case 123: // bulk combine
                $this->getUrenData($import_id, $csv_writer);
                $this->getOnbetaaldVerlof($import_id, $csv_writer);
                $this->getAbsenceData($import_id, $csv_writer);
                $this->getDeclaratieData($import_id, $csv_writer, false);
                break;
            case 4:
                $this->getUrenData($import_id, $csv_writer);
                break;
            case 99:
                $this->createCSVPlaatsingen($import_id, $csv_writer);
                break;
            default:
                return false;
        }
        return false;
    }

    public function createCSVHeaders($looncomponent_id)
    {
        return match ($looncomponent_id) {
            1, 2, 4, 5, 7, 11 => ['reg', 'pltsnr', 'looncomponent', 'perc', 'datum', 'uren', 'relatienr', 'kpcode', 'functie', 'cao schaal', 'tarief', 'loonschema'], // Uren & absentie
            3, 123 => ['reg', 'pltsnr', 'looncomponent', 'perc', 'datum', 'uren', 'relatienr', 'kpcode', 'functiecode', 'cao schaal', 'tariefschema', 'loonschema', 'tarief', 'uurloon', 'afwijkende functienaam', 'btw'], // Declaraties & captains
            99 => ['reg', 'pltsnr', 'looncomponent', 'perc', 'datum', 'uren', 'relatienr', 'kpcode', 'functie', 'cao schaal', 'tarief', 'loonschema'], // Plaatsingen
            default => [],
        };
    }

    public function urenImportDefinitie($looncomponent_type_id, $createPlaatsingen)
    {
        return $createPlaatsingen ? 6195 : match ($looncomponent_type_id) {
            1, 2, 4, 5 => 6195, // Uren & Absentie
            3 => 6224,          // Declaraties
            default => null,
        };
    }

    public function checkPlaatsingen($import_id): bool
    {
        $urenbestanden = Urenbestand::select('driver_id', 'city', 'loon_component_id', DB::raw('SUM(hours) as total_hours'))
            ->where('import_id', '=', $import_id)
            ->groupBy('driver_id', 'city', 'loon_component_id')
            ->get();

        foreach ($urenbestanden as $urenbestand) {
            $applicant = $urenbestand->applicant;
            if (!$applicant || !$applicant->easyflex_id) {
                return false;
            }

            $plaatsing = Plaatsing::where('applicant_id', $applicant->applicant_id)
                ->where('kpcode', ucfirst($urenbestand->city))
                ->exists();
            if(!$plaatsing){
                return false;
            }
        }
        return true;
    }

    public function countApplicantsWithoutPlaatsing($import_id)
    {
        $urenbestanden = Urenbestand::select('driver_id', 'city')
            ->where('import_id', '=', $import_id)
            ->groupBy('driver_id', 'city')
            ->get();

        $applicantsWithoutPlaatsingCount = 0;
        $users = [];
        $missingPlaatsingUsers = [];
        foreach ($urenbestanden as $urenbestand) {
            $applicant = $urenbestand->applicant;
            if($applicant == null){
                return ['error' => 'An applicant not found', 'info' => [$urenbestand->driver_id]]; //rood
            }
            if (!$applicant->easyflex_id) {
                $users[] = $applicant->applicant_id;
                continue;
            }

            $plaatsing = Plaatsing::where('applicant_id', $applicant->applicant_id)
                ->where('kpcode', ucfirst($urenbestand->city))
                ->exists();

            if (!$plaatsing) {
                $missingPlaatsingUsers[] = $applicant->scoober_id;
                $applicantsWithoutPlaatsingCount++;
            }
        }
        if(count($users) != 0){
            return ['error' => 'Missing easyflex_ids', 'info' => $users]; //rood
        }
        if($applicantsWithoutPlaatsingCount === 0){
            return ['error' => 'All applicants have a plaatsing', 'info' => null]; //groen
        }
        return ['error' => 'Missing plaatsingen: '.$applicantsWithoutPlaatsingCount, 'info' => $missingPlaatsingUsers]; //oranje
    }

    public function getUrenData($import_id, $csv_writer)
    {
        $firstChunk = true;
        $urenbestandDataQuery = Urenbestand::select('driver_id', 'city', 'date', 'loon_component_id', DB::raw('SUM(hours) as total_hours'))
            ->groupBy('driver_id', 'city', 'date', 'loon_component_id')
            ->where(function ($query) {
                $query->where('absence', 'false')
                    ->orWhereNull('absence');
            });

        if (is_array($import_id)) {
            $urenbestandDataQuery->whereIn('import_id', $import_id);
        } else {
            $urenbestandDataQuery->where('import_id', $import_id);
        }

        $urenbestandDataQuery
        ->orderBy('driver_id')
            ->orderBy('city')
            ->orderBy('date')
            ->orderBy('loon_component_id');

        $urenbestandDataQuery->chunk($this->chunkSize,
        function ($urenbestandDataChunk) use ($csv_writer, &$firstChunk) {
            if ($firstChunk) {
                $csv_writer->addHeader($this->createCSVHeaders($urenbestandDataChunk[0]->loonComponent->looncomponenttype->id));
                $firstChunk = false;
            }
            foreach ($urenbestandDataChunk as $uren) {
                $reg = $uren->applicant->easyflex_id;
                if ($reg) {
                    $plaatsing = Plaatsing::where('applicant_id', $uren->applicant->applicant_id)->where('kpcode', ucfirst($uren->city))->first();
                    if ($plaatsing) {
                        $plaatsingnr = $plaatsing->plaatsing_id;
                        $original_date = $uren->date;
                        $date = date('d-m-Y', strtotime($original_date));
                        $kpcode = ucfirst($uren->city);
                        $hours = number_format($uren->total_hours, 2, ',', '.');
                        $percentage = $this->checkPercentage($original_date);
//                        $function_information = $this->getFunctionInformation($uren->applicant->pre_contract_function);
                        if ($percentage == null) {
                            return Notification::make()
                                ->title('Export failed. Reason: No percentage found for applicant ' . $uren->applicant->applicant_id)
                                ->persistent()
                                ->danger()
                                ->send();
                        }
                        $looncomponent = ($percentage == '100') ? $uren->loonComponent->name : 'Onregelmatige uren';
                        $loonComponent = $urenbestandDataChunk[0]->loonComponent;

                        switch ($loonComponent->looncomponenttype->id) {
                            case 5:
                                $looncomponent = $uren->loonComponent->name;
                                break;
                            case 4: // late own
                                $looncomponent = $loonComponent->looncomponenttype->name;
                                $percentage = $loonComponent->value;
                                break;
                        }

//                        $function = ($loonComponent->id === 10) ? 'Captain' : 'Courier';

//                        $uurloon = $this->calculateUurloon($date, $uren->applicant->flexappData->personalData->date_of_birth, $function);
//                        $csv_writer->addRow([$reg, $plaatsingnr, $looncomponent, $percentage, $date, $hours, $this->relatienummer, $kpcode, $function_information['functiecode'], $this->cao_schaal, $this->tarief, $function_information['loonschema'], $uurloon, '', $function_information['afwijkende_functie'], '']);
                        $csv_writer->addRow([$reg, $plaatsingnr, $looncomponent, $percentage, $date, $hours, $this->relatienummer, $kpcode, $this->functie, $this->cao_schaal, $this->tarief, $this->loonschema]);
                    }
                }
            }
        });
    }

    public function getOnbetaaldVerlof($import_id, $csv_writer)
    {
        $firstChunk = true;
        $urenbestandDataQuery = Urenbestand::select('driver_id', 'city', 'date', 'loon_component_id', DB::raw('SUM(hours) as total_hours'))
            ->groupBy('driver_id', 'city', 'date', 'loon_component_id')
            ->where('absence', 'true')
            ->where('sub_reason', 'LIKE', '%unpaid%');

        if (is_array($import_id)) {
            $urenbestandDataQuery->whereIn('import_id', $import_id);
        } else {
            $urenbestandDataQuery->where('import_id', $import_id);
        }

        $urenbestandDataQuery->orderBy('driver_id')
            ->orderBy('city')
            ->orderBy('date')
            ->orderBy('loon_component_id');

        $urenbestandDataQuery->chunk($this->chunkSize, function ($urenbestandDataChunk) use ($csv_writer, &$firstChunk) {
            if ($firstChunk) {
                $csv_writer->addHeader($this->createCSVHeaders($urenbestandDataChunk[0]->loonComponent->looncomponenttype->id));
                $firstChunk = false;
            }
            foreach ($urenbestandDataChunk as $uren) {
                $reg = $uren->applicant->easyflex_id;
                if ($reg) {
                    $plaatsing = Plaatsing::where('applicant_id', $uren->applicant->applicant_id)->where('kpcode', ucfirst($uren->city))->first();
                    if ($plaatsing) {
                        $plaatsingnr = $plaatsing->plaatsing_id;
//                        $function_information = $this->getFunctionInformation($uren->applicant->pre_contract_function);
                        $original_date = $uren->date;
                        $date = date('d-m-Y', strtotime($original_date));
//                        $uurloon = $this->calculateUurloon($date, $uren->applicant->flexappData->personalData->date_of_birth);
                        $kpcode = ucfirst($uren->city);
                        $hours = number_format($uren->total_hours, 2, ',', '.');
                        $csv_writer->addRow([$reg, $plaatsingnr, 'Onbetaald verlof', '0', $date, $hours, $this->relatienummer, $kpcode, $this->functie, $this->cao_schaal, $this->tarief, $this->loonschema]);
//                        $csv_writer->addRow([$reg, $plaatsingnr, 'Onbetaald verlof', '0',  $date, $hours, $this->relatienummer, $kpcode, $function_information['functiecode'], $this->cao_schaal, $this->tarief, $function_information['loonschema'], $uurloon, '', $function_information['afwijkende_functie'], '']);
                    }
                }
            }
        });
    }

    // Not unpaid and not sickness
    public function getAbsenceData($import_id, $csv_writer)
    {
        $drivers = Urenbestand::where('import_id', $import_id)
            ->where('absence', 'true')
            ->pluck('driver_id')
            ->unique();

        $csv_writer->addHeader($this->createCSVHeaders(1));

        foreach($drivers as $driver){
            $totalVacationHours = Urenbestand::select('driver_id',
                DB::raw('SUM(hours) as total_hours'),
                DB::raw('COUNT(*) as total_count'))
                ->where('driver_id', $driver)
                ->where('import_id', $import_id)
                ->where('absence', 'true')
                ->where('sub_reason', 'NOT LIKE', '%unpaid%')
                ->where('sub_reason', 'NOT LIKE', '%sick%')
                ->where('sub_reason', 'NOT LIKE', '%injury%')
                ->groupBy('driver_id')
                ->get();

            if ($totalVacationHours->isEmpty()) {
                continue;
            }

            $reg = $totalVacationHours[0]->applicant->easyflex_id;
            $vacationInfo = $this->calculateVacationHours($reg, $totalVacationHours[0]->total_hours);
            $totalVacationHoursUsed = 0;

            $urenbestandDataQuery = Urenbestand::select('driver_id', 'city', 'date', 'loon_component_id', 'sub_reason', DB::raw('SUM(hours) as total_hours'))
                ->where('driver_id', $driver)
                ->where('import_id', $import_id)
                ->where('absence', 'true')
                ->where('sub_reason', 'NOT LIKE', '%unpaid%')
                ->where('sub_reason', 'NOT LIKE', '%sick%')
                ->where('sub_reason', 'NOT LIKE', '%injury%')
                ->groupBy('city', 'date', 'driver_id', 'loon_component_id', 'sub_reason')
                ->orderBy('date', 'asc')
                ->distinct();

            if (is_array($import_id)) {
                $urenbestandDataQuery->whereIn('import_id', $import_id);
            } else {
                $urenbestandDataQuery->where('import_id', $import_id);
            }

            $urenbestandData = $urenbestandDataQuery->get();
            foreach ($urenbestandData as $uren) {
                $plaatsing = Plaatsing::where('applicant_id', $uren->applicant->applicant_id)->where('kpcode', ucfirst($uren->city))->first();
                if($plaatsing)
                {
                    $plaatsingnr = $plaatsing->plaatsing_id;
                    $original_date = $uren->date;
                    $date = date('d-m-Y', strtotime($original_date));
                    $kpcode = ucfirst($uren->city);
                    $looncomponent = $this->getLooncomponent($uren->sub_reason);
//                    $function_information = $this->getFunctionInformation($uren->applicant->pre_contract_function);
//                    $uurloon = $this->calculateUurloon($date, $uren->applicant->flexappData->personalData->date_of_birth);
                    $hours = $uren->total_hours;
                    if ($looncomponent === 'Vakantiedagen') {
                        $remainingVacationHours = $vacationInfo['vacation'] - $totalVacationHoursUsed;

                        $vacationHoursToUse = min($hours, $remainingVacationHours);
                        $totalVacationHoursUsed += $vacationHoursToUse;

                        if ($totalVacationHoursUsed >= $vacationInfo['vacation']) {
                            $unpaidHours = max(0, $hours - $vacationHoursToUse);
                            $hours -= $unpaidHours;
                            if ($unpaidHours > 0) {
                                $unpaidHours = number_format($unpaidHours, 2, ',', '.');
                                $csv_writer->addRow([$reg, $plaatsingnr, 'Onbetaald verlof 1', '0', $date, $unpaidHours, $this->relatienummer, $kpcode, $this->functie, $this->cao_schaal, $this->tarief, $this->loonschema]);
//                                $csv_writer->addRow([$reg, $plaatsingnr, 'Onbetaald verlof 1', '0',  $date, $unpaidHours, $this->relatienummer, $kpcode, $function_information['functiecode'], $this->cao_schaal, $this->tarief, $function_information['loonschema'], $uurloon, '', $function_information['afwijkende_functie'],'']);
                            }
                        }
                    }
                    $percentage = $this->checkPercentage($date, $uren->driver_id, $looncomponent);
                    if ($hours > 0) {
                        $hours = number_format($hours, 2, ',', '.');
                        $csv_writer->addRow([$reg, $plaatsingnr, $looncomponent, $percentage, $date, $hours, $this->relatienummer, $kpcode, $this->functie, $this->cao_schaal, $this->tarief, $this->loonschema]);
//                        $csv_writer->addRow([$reg, $plaatsingnr, $looncomponent, $percentage,  $date, $hours, $this->relatienummer, $kpcode, $function_information['functiecode'], $this->cao_schaal, $this->tarief, $function_information['loonschema'], $uurloon, '', $function_information['afwijkende_functie'], '']);
                    }
                }else{
                    Log::class('Export error: plaatsing not found for applicant '.$uren->applicant->applicant_id);
                }
            }
        }
    }

    public function getAbsencePercentage($driver_id, $date)
    {
        $applicant = JetApplicant::where('scoober_id', $driver_id)->select('date_of_birth', 'pre_contract_function')->first();

        if (!$applicant) {
            return null;
        }

        $dob = $applicant->date_of_birth;
        $function = $applicant->pre_contract_function;

        if (!$dob) {
            return null;
        }

        $date = Carbon::parse($date);
        $requestedAge = $date->diffInYears($dob);
        $age = ($requestedAge > 21) ? 21 : $requestedAge;

        $percentage = LooncomponentPercentage::where('age', intval(floor($age)))
            ->where('start_date', '<=', $date)
            ->where('function', $function)
            ->orderBy('start_date', 'desc')
            ->value('percentage');

        return $percentage;
    }

    public function getLooncomponent($sub_reason = null)
    {
        $sub_reason_lower = strtolower($sub_reason);

        return match (true) {
            str_contains($sub_reason_lower, 'vacation') => 'Vakantiedagen',
            str_contains($sub_reason_lower, 'sick') || str_contains($sub_reason_lower, 'sickness') || str_contains($sub_reason_lower, 'injury') => 'Ziekte',
            str_contains($sub_reason_lower, 'garden') => 'Garden leave',
            str_contains($sub_reason_lower, 'emergency') => 'Emergency leave',
            $sub_reason_lower === 'short-term-care-leave-hourly' => 'Kortdurend zorgverlof',
            $sub_reason_lower === 'bereavement-leave-other-daily' => 'Rouw verlof',
            default => $sub_reason,
        };
    }

    public function getDeclaratieData($import_id, $csv_writer)
    {
        $urenbestandDataQuery = Urenbestand::select('driver_id', 'city', 'loon_component_id', 'weeknr', DB::raw('MIN(date) as date'),  DB::raw('SUM(amount) as total_amount'))
            ->groupBy('driver_id', 'city', 'loon_component_id', 'weeknr')
            ->distinct();

        if (is_array($import_id)) {
            $urenbestandDataQuery->whereIn('import_id', $import_id);
        } else {
            $urenbestandDataQuery->where('import_id', $import_id);
        }

        $urenbestandData = $urenbestandDataQuery->get();
        $csv_writer->addHeader($this->createCSVHeaders($urenbestandData[0]->loonComponent->looncomponenttype->id));

        foreach ($urenbestandData as $uren) {
            $reg = $uren->applicant->easyflex_id;
            if($reg){
                $plaatsing = Plaatsing::where('applicant_id', $uren->applicant->applicant_id)->where('kpcode', ucfirst($uren->city))->first();
                if($plaatsing)
                {
                    $plaatsingnr = $plaatsing->plaatsing_id;
                    $original_date = $uren->date;
                    $kpcode = ucfirst($uren->city);
                    $date = date('d-m-Y', strtotime($original_date));
                    // use for tips/km
                    $carbonDate = Carbon::createFromFormat('Y-m-d', $original_date);
                    $lastDayOfMonth = $carbonDate->endOfMonth()->format('d-m-Y');
                    $function_information = $this->getFunctionInformation($uren->applicant->pre_contract_function);
                    //todo:  zoek correcte einddatum + dag ervoor inboeken
                    $amount = number_format($uren->total_amount, 2, ',', '.');
                    $tariff = $amount;
                    $looncomponent = $uren->loonComponent->name;
                    if($uren->loon_component_id == 8){
                        $tariff = $this->calculateAmount($uren->total_amount, $uren->applicant->pre_contract_is_indefinite);
                        $yearweek = $uren->weeknr;
                        $year = (int) substr($yearweek, 0, 4);
                        $weeknr = (int) substr($yearweek, 4, 2);
                        $monday = Carbon::now()
                            ->setISODate($year, $weeknr)
                            ->startOfWeek();
                        $date = $monday->format('d-m-Y');
                    }
                    $btw = ($uren->loonComponent->id == 3) ? 0 : '';
                    $csv_writer->addRow([$reg, $plaatsingnr, $looncomponent, '', $date, '', $this->relatienummer, $kpcode, $function_information['functiecode'], $this->cao_schaal, $this->tarief, $function_information['loonschema'], $amount, $tariff, $function_information['afwijkende_functie'], $btw]);
                }
            }
        }
    }

    public function createCSVPlaatsingen($import_id, $csv_writer)
    {
        $urenbestandDataQuery = Urenbestand::select('driver_id', 'city', DB::raw('MIN(date) as date'))
            ->groupBy('driver_id', 'city')
            ->distinct();

        if (is_array($import_id)) {
            $urenbestandDataQuery->whereIn('import_id', $import_id);
        } else {
            $urenbestandDataQuery->where('import_id', $import_id);
        }

        $urenbestandData = $urenbestandDataQuery->get();

        $csv_writer->addHeader($this->createCSVHeaders(99));

        $driverIds = $urenbestandData ->pluck('driver_id')->unique();
        $jetApplicants = JetApplicant::whereIn('scoober_id', $driverIds)->get();
        $totalRows = 0;
        foreach ($urenbestandData  as $uren) {
            foreach ($jetApplicants as $jetApplicant) {
                if ($jetApplicant->scoober_id == $uren->driver_id) {
                    $plaatsing = Plaatsing::where('applicant_id', $jetApplicant->applicant_id)
                        ->where('kpcode', ucfirst($uren->city))
                        ->exists();

                    if (!$plaatsing) {
                        $reg = $jetApplicant->easyflex_id;
                        $date = date('d-m-Y', strtotime($uren->date));
                        $kpcode = ucfirst($uren->city);
                        $csv_writer->addRow([$reg, '', 'Loon normale uren', '100', $date, '0', $this->relatienummer, $kpcode, $this->functie, $this->cao_schaal, $this->tarief, $this->loonschema]);
                        $totalRows++;
                    }
                    break;
                }
            }
        }

        if($totalRows > 0){
            return false;
        }
        return true;
    }

    protected function checkPercentage($date, $driver_id=null, $looncomponent=null)
    {
        if (!$driver_id && !$looncomponent) {
            $dateException = LoonComponentExceptions::where('exception_date', $date)->first();
            return $dateException ? $dateException->value : '100';
        }

        if ($looncomponent === 'Onbetaald verlof') {
            return '0';
        }

        if ($looncomponent === 'Vakantiedagen') {
            return null;
        }

        if ($looncomponent === 'Ziekte') {
            return $this->getAbsencePercentage($driver_id, $date);
        }

        return '100';
    }

    protected function getFunctionInformation($pre_contract_function)
    {
        $information['loonschema'] = $this->loonschema;
        $information['functiecode'] = 'Courier';
        $information['afwijkende_functie'] = 'Courier';

        if($pre_contract_function === 'Courier Captain')
        {
            $information['loonschema'] = 'CAPTAIN';
            $information['functiecode'] = 'Captain';
            $information['afwijkende_functie'] = 'Courier Captain';
        }

        return $information;
    }

    public function calculateAmount($amount, $contract_type)
    {
        // true is indefinite contract
        if($contract_type){
            $totalAmount = $amount * 1.1676;
        }else{
            $totalAmount = $amount * 1.2176;
        }
        return number_format($totalAmount, 2, ',', '.');
    }

    public function calculateUurloon($date, $dob, $function = 'Courier')
    {
        $dob = Carbon::parse($dob);
        $requestedAge = (int) $dob->diffInYears($date);
        $age = ($requestedAge > 21) ? 21 : $requestedAge;
        $format_startdate = Carbon::parse($date)->format('Y-m-d');

        $loonInfo = LooncomponentPercentage::where('age', $age)
            ->where('function', $function)
            ->whereDate('start_date', '<=', $format_startdate)
            ->orderBy('start_date', 'desc')->first();

        return number_format($loonInfo->wage_per_hour, 2, ',', '.');
    }

    public function exportToEasyflex($filename, $user_id, $import_id = null, $createPlaatsingen = false)
    {
        $filePath = storage_path('app/export_temp/'.$filename);
        if(File::exists($filePath)) {
            $csvData = File::get($filePath);
        }

        $looncomponent_type_id = Urenbestand::where('import_id', $import_id)->first()->loonComponent->looncomponenttype->id;
        $urenimport_definitie = $this->urenImportDefinitie($looncomponent_type_id, $createPlaatsingen);
        $fields = array('wm_urenimport_filename' => $filename, 'wm_urenimport_filedata' => $csvData, 'wm_urenimport_definitie' => $urenimport_definitie, 'wm_urenimport_verwerken' => false);
        $result = $this->easyflex_service->sendToHoursEasyflex($fields);
        if($result['success']){
            $fields = $result['fields'];
            Export::create([
                'import_id' => $import_id,
                'file_name' => $filename,
                'file_nr' => $fields->wm_urenimport_files->item->wm_urenimport_files_filenr,
                'status' => $fields->wm_urenimport_success,
                'loon_component_type_id' => $looncomponent_type_id,
                'user_id' => $user_id,
            ]);
            return true;
        }
        return $result['error'];
    }

    public function calculateVacationHours($registratienummer, $hoursToTake)
    {
        $availableVacationHours = $this->easyflex_service->getVacationHours($registratienummer);
        $remainingVacationHours = $availableVacationHours - $hoursToTake;
        if ($remainingVacationHours < 0) {
            $result['unpaid'] =  abs($remainingVacationHours);
            $result['vacation'] = (float) $availableVacationHours;
        }else{
            $result['vacation'] = (float) $hoursToTake;
        }
        return $result;
    }
}
