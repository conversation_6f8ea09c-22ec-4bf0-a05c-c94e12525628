<?php

namespace App\Http\Middleware;

use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Sentry\State\Scope;
use Throwable;
use function Sentry\configureScope as configureSentryScope;

class SentryContext
{
    public function handle(Request $request, Closure $next)
    {
        if (! auth()->check()) {
            return $next($request);
        }

        configureSentryScope(function (Scope $scope): void {
            $uuid = auth()->id();

            try {
                $user = User::where('uuid', $uuid)
                    ->select(['id', 'roles', 'uuid', 'claims'])
                    ->firstOrFail();
            } catch (Throwable) {
                $scope->setUser(['id' => $uuid]);
                $scope->setTag('flexapp_id', $uuid);

                return;
            }

            $claims = json_decode($user->claims, associative: true);

            $commonData = [
                'uuid' => $uuid,
                'id' => $user->id,
                'email' => data_get($claims, 'email'),
                'username' => data_get($claims, 'preferred_username'),
                'UKT_id' => data_get($user->wenote, 'UKT_id'),
            ];

            $scope->setUser([
                ...$commonData,
                'roles' => data_get($claims, 'roles'),
            ]);

            $scope->setTags([
                ...$commonData,
                'flexapp_id' => $uuid,
            ]);
        });

        return $next($request);
    }
}
