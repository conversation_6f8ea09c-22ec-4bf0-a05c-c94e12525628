<?php

namespace App\Enums;

enum CourierDocumentType: string
{
    case ACCIDENT_REPORT = "Accident report";
    case BACKGROUND_POLICE_STATEMENT = "Background / Police statement";
    case BANK_ACCOUNT = "Bank account";
    case CONTRACT = "Contract";
    case CONTRACT_AMENDMENT = "Contract Amendment";
    case DISABILITY = "Disability";
    case DRIVER_LICENSE = "Driver's license";
    case ENROLLMENT_CERTIFICATE = "Enrollment certificate";
    case EQUIPMENT = "Equipment";
    case HEALTH_SAFETY_TRAINING = "Health & Safety training";
    case HEALTH_INSURANCE = "Health insurance";
    case ID_PASSPORT = "ID / Passport";
    case LEGAL = "Legal";
    case MILITARY_DOCUMENT = "Military document";
    case OTHER_HIRING = "Other (hiring)";
    case OTHER_OPERATIONAL = "Other (operational)";
    case OTHER_SENSITIVE = "Other (sensitive)";
    case REFERENCE_LETTER = "Reference Letter";
    case REGISTRATION_CERTIFICATE = "Registration certificate";
    case SICK_NOTE = "Sick note";
    case SOCIAL_SECURIY_DOCUMENT = "Social Securiy document";
    case TAX_DOCUMENT = "Tax document";
    case TERMINATION = "Termination";
    case VEHICLE_CHECK = "Vehicle check";
    case VEHICLE_RELATED_DOCUMENT = "Vehicle related document";
    case WARNING_LETTER = "Warning letter";
    case WORK_PERMIT = "Work permit";
}
