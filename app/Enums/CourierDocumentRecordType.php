<?php

namespace App\Enums;

use Illuminate\Support\Facades\App;

enum CourierDocumentRecordType: string
{
    case OPERATIONAL = 'operational';
    case SENSITIVE_HR = 'sensitive_hr';
    case SENSITIVE_HIRING_DATA = 'sensitive_hiring_data';
    case MASTER = 'master';

    public function getId()
    {
        if (App::environment('production')) {
            return match ($this) {
                self::OPERATIONAL => '01207000000HHf9AAG',
                self::SENSITIVE_HR => '01207000000HHfAAAW',
                self::SENSITIVE_HIRING_DATA => '01207000000HHfBAAW',
                self::MASTER => '012000000000000AAA',
            };
        }

        return match ($this) {
            self::OPERATIONAL => '0121x000004p1wUAAQ',
            self::SENSITIVE_HR => '0121x000004p1wVAAQ',
            self::SENSITIVE_HIRING_DATA => '0121x000004p1wWAAQ',
            self::MASTER => '012000000000000AAA',
        };
    }
}
