<?php

namespace App\Enums;

enum JetEntity: string
{
    public const API_VERSION = 'v60.0';

    public const
        SALES_FORCE_CONTENT_LOCATION = 'S',
        EXTERNAL_CONTENT_LOCATION = 'E',
        SOCIAL_CUSTOMER_SERVICE_CONTENT_LOCATION = 'L';

    case COURIER_DOCUMENT = 'Courier_Document__c';
    case CONTENT_VERSION = 'ContentVersion';
    case QUERY = 'Query';
    case CONTENT_DOCUMENT_LINK = 'ContentDocumentLink';

    public function apiUrl(?string $id = null): string
    {
        $format = match ($this) {
            self::COURIER_DOCUMENT,
            self::CONTENT_VERSION,
            self::CONTENT_DOCUMENT_LINK => '/services/data/%s/sobjects/' . $this->value,
            self::QUERY => '/services/data/%s/query',
        };
        $url = sprintf($format, self::API_VERSION);

        if ($id === null) {
            return $url;
        }

        return sprintf('%s/%s', $url, $id);
    }
}
