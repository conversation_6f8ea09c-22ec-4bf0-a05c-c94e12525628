<?php

namespace App\Livewire;

use App\Models\Urenbestand;
use Carbon\Carbon;
use Livewire\Component;
use Spatie\SimpleExcel\SimpleExcelWriter;

class CreateExportButton extends Component
{
    protected $listeners = ['createExport' => 'createExport'];

    public $record;
    public $weeknr;
    public $workedHours;


    public function mount($record, $weeknr, $workedHours)
    {
        $this->record = $record;
        $this->weeknr = $weeknr;
        $this->workedHours = $workedHours;
    }
    public function createExport()
    {
        $workedHours = json_decode($this->workedHours, true);
        $arbeidstijdenwet = new \App\Filament\Resources\ImportResource\Pages\Arbeidstijdenwet();
        $dataSets = [
            'regular' => [
                'title' => 'Regular Applicants',
                'data' => $arbeidstijdenwet->getRegularApplicantsInformation($workedHours['regular'], $this->weeknr),
            ],
            'underage' => [
                'title' => 'Underage Applicants',
                'data' => $arbeidstijdenwet->getUnderageApplicantsData($workedHours['underage'], $this->weeknr),
            ],
            'student' => [
                'title' => 'Student Applicants',
                'data' => $arbeidstijdenwet->getStudentData($workedHours['student']),
            ],
        ];

        $filename = 'export_arbeidstijdenwet'.Carbon::now()->timestamp.'.csv';
        $filelocation = storage_path('app/export_temp/'.$filename);
        $csv_writer = SimpleExcelWriter::create($filelocation, '', null, ';');

        $headers = [
            'daily' => ['scoober_id', 'easyflex_id', 'uren', 'datum/weeknr'],
            'weekly' => ['scoober_id', 'easyflex_id', 'uren', 'datum', 'import_id', 'totaal_uren'],
        ];

        $periodTitles = [
            'daily' => 'Daily Hours',
            'weekly' => 'Weekly Hours',
        ];

        foreach ($dataSets as $type => $dataset)
        {
            if (!empty($dataset['data']['daily']) || !empty($dataset['data']['weekly']))
            {
                $csv_writer->addHeader([$dataset['title']]);

                foreach (['daily', 'weekly'] as $period)
                {
                    if (!empty($dataset['data'][$period]))
                    {
                        $csv_writer->addRow([$periodTitles[$period]]);
                        $csv_writer->addHeader($headers[$period]);

                        foreach ($dataset['data'][$period] as $entry)
                        {
                            if ($period === 'daily')
                            {
                                $csv_writer->addRow([
                                    $entry['driver_id'],
                                    $entry['easyflex_id'],
                                    $entry['total_hours'],
                                    $entry['date']
                                ]);
                            } elseif ($period === 'weekly')
                            {
                                $datas = $this->getHours($entry['driver_id'], $entry['weeknr']);
                                foreach ($datas as $dataRow)
                                {
                                    $csv_writer->addRow([
                                        $dataRow['driver_id'],
                                        $entry['easyflex_id'],
                                        $dataRow['hours'],
                                        $dataRow['date'],
                                        $dataRow['import_id'],
                                        $entry['total_hours']
                                    ]);
                                }
                            }
                        }
                        $csv_writer->addRow([]);
                    }
                }

                if (!empty($dataset['data']))
                {
                    $csv_writer->addRow([]);
                }
            }
        }

        $csv_writer->close();
        return response()->download($filelocation);
    }

    public function getHours($scoober_id, $date)
    {
        $query = Urenbestand::where('driver_id', $scoober_id)
            ->where('absence', false);

        if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $date))
        {
            // If it's a date (YYYY-MM-DD), filter by date
            $query->where('date', $date);
        } elseif (preg_match('/^\d{6}$/', $date))
        {
            // If it's a week number (YYYYWW), filter by weeknr
            $query->where('weeknr', $date);
        }

        return $query->get();
    }

    public function render()
    {
        return view('livewire.create-export-button');
    }
}
