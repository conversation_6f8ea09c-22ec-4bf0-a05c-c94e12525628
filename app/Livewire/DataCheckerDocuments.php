<?php

namespace App\Livewire;

use App\Models\JetApplicant;
use App\Services\DataCheckerService;
use App\Services\UserService;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Livewire\Component;

class DataCheckerDocuments extends Component implements HasForms, HasInfolists
{
    use InteractsWithForms, InteractsWithInfolists;

    public $uuid = null;

    public function mount($record, String|null $uuid): void
    {
        $this->uuid = $uuid;
        $this->infolist->record($record);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Tabs::make('Tabs')
                    ->tabs([
                        Tabs\Tab::make('Identificatie')
                            ->schema([
                                ...$this->getDocument()
                            ]),
                        Tabs\Tab::make('Rijbewijs')
                            ->schema([
                                ...$this->getDocument('driverLicense')
                            ]),
                        Tabs\Tab::make('Alle afbeeldingen ID scan')
                            ->schema([
                                $this->getMostRecentTransactionImages()
                            ])
                    ])
            ]);
    }

    public function getDocument($type = 'identity'): array
    {
        $return = [
            Infolists\Components\TextEntry::make('')
                ->getStateUsing('Geen document gevonden')
                ->hiddenLabel()
        ];

        if ($this->uuid) {
            $images = $this->getImages();
            $data = $this->getData($type);

            if(is_null($data)){
                return [
                    Infolists\Components\TextEntry::make('')
                        ->getStateUsing('Geen document gevonden')
                        ->hiddenLabel()
                ];
            }

            $documentType = null;

            if(isset($data['info']['document_type'])) {
                $documentType = $data['info']['document_type'];
            } else if($type === 'driverLicense') {
                $documentType = 'DRIVING_LICENSE';
            }

            if (array_key_exists($documentType, $images)) {
                $documentImages = $images[$documentType];
            }

            $status = $data['info']['result'] ?? '';

            if (!($data['compare_documents']['valid'] ?? true) || $status === 'RESULT_AVAILABLE') {
                $status = 'REJECTED';
            }

            #region RTW warning
            # Show a warning if a scan did not have an RTW check
            # When the data has been checked, stop showing the warning.
            $isApprovedIDScanWithoutRTWCheck = $data['info']['result'] === 'APPROVED'
                && $documentType !== 'DRIVING_LICENSE'
                && !in_array($data['product'] ?? '', ['RTW_PREMIUM', 'RTW_STANDARD'], true);

            $isCheckedByRecruiter = JetApplicant::firstWhere('flexapp_id', $this->uuid)?->flexappData?->info?->data_checked_by_recruiter ?? false;
            #endregion

            $return = [
                Infolists\Components\TextEntry::make('')
                    ->columnSpanFull()
                    ->getStateUsing(function () use ($status, $data) {
                        $html = "<p class='font-bold'>{$status}</p>";

                        $reasons = $data['message']['messages'] ?? [];

                        if (!($data['compare_documents']['valid'] ?? true)) {
                            array_unshift($reasons, 'EXACT_TYPE');
                        }

                        if (count($reasons) > 0) {
                            $html .= '<ul class="list-disc list-inside"><li>';
                            $html .= implode('</li><li>', array_map(fn ($reason) => __('datachecker.reasons.'.$reason), $reasons));
                            $html .= '</li></ul>';
                        }

                        return $html;
                    })
                    ->extraAttributes(function () use ($status) {
                        $classes = 'mb-4 px-4 py-4 border-l-4';

                        if($status === 'APPROVED') {
                            $classes .= ' bg-success-100 border-success-500';
                        } else if($status === 'REJECTED') {
                            $classes .= ' bg-danger-100 border-danger-500';
                        } else if($status === 'UPLOADED') {
                            $classes .= ' bg-blue-100 border-info-500';
                        }

                        return ['class' => $classes];
                    })
                    ->html()
                    ->size(Infolists\Components\TextEntry\TextEntrySize::Medium),

                ...($isApprovedIDScanWithoutRTWCheck && !$isCheckedByRecruiter ? [
                    Infolists\Components\TextEntry::make('')
                        ->columnSpanFull()
                        ->getStateUsing(function () {
                            $html = "<p class='font-bold'>Let op:</p>";
                            $html .= "<p>Bij deze scan is geen 'Right To Work' check uitgevoerd.</p>";
                            $html .= '<p>Let op of de applicant mag werken, of scan opnieuw met RTW check.</p>';

                            return $html;
                        })
                        ->extraAttributes(function () {
                            return ['class' => 'mb-4 px-4 py-4 border-l-4 bg-danger-100 border-danger-500'];
                        })
                        ->html()
                        ->size(Infolists\Components\TextEntry\TextEntrySize::Medium)
                ] : []),
            ];

            if (isset($documentImages) && $status !== 'UPLOADED') {
                $return = [
                    ...$return,

                    ...($documentImages['FRONT']['attributes']['preview_url'] ?? null ? [
                        Infolists\Components\ImageEntry::make('front')
                            ->getStateUsing($documentImages['FRONT']['attributes']['preview_url'] ?? null)
                            ->extraImgAttributes(['loading' => 'lazy'])
                            ->label(('Legitimatie') . ' voorkant')
                    ] : [
                        Infolists\Components\TextEntry::make(''),
                    ]),

                    ...($documentImages['BACK']['attributes']['preview_url'] ?? null ? [
                        Infolists\Components\ImageEntry::make('back')
                            ->getStateUsing($documentImages['BACK']['attributes']['preview_url'] ?? null)
                            ->extraImgAttributes(['loading' => 'lazy'])
                            ->label(('Legitimatie') . ' achterkant')
                    ] : [
                        Infolists\Components\TextEntry::make(''),
                    ]),

                    ...($documentImages['RESIDENCE_PERMIT_STICKERS']['attributes']['preview_url'] ?? null ? [
                        Infolists\Components\ImageEntry::make('sticker')
                            ->getStateUsing($documentImages['RESIDENCE_PERMIT_STICKERS']['attributes']['preview_url'] ?? null)
                            ->extraImgAttributes(['loading' => 'lazy'])
                            ->label(('Legitimatie') . ' sticker')
                    ] : [
                        Infolists\Components\TextEntry::make(''),
                    ]),

                    Infolists\Components\TextEntry::make(''),

                    Infolists\Components\ViewEntry::make('created_at')->view('filament.components.view-field')
                        ->label('Gekeurd door DataChecker op')
                        ->getStateUsing(fn () => $data['info']['created_at'] ?? null ? Carbon::parse($data['info']['created_at'])->format('H:i:s d-m-Y') : ''),

                    Infolists\Components\ViewEntry::make('document_number')->view('filament.components.view-field')
                        ->label('Documentnummer')
                        ->getStateUsing(fn () => $data['info']['document_number'] ?? ''),

                    Infolists\Components\ViewEntry::make('rtw_check')->view('filament.components.view-field')
                        ->label('RTW check')
                        ->hidden(fn () => $type !== 'identity')
                        ->getStateUsing(fn () => $data['message']['rtw_type'] ?? 'Onbekend'),

                    Infolists\Components\ViewEntry::make('date_of_birth')->view('filament.components.view-field')
                        ->label('Geboortedatum')
                        ->getStateUsing(fn () => $data['info']['date_of_birth'] ?? null ? Carbon::parse($data['info']['date_of_birth'])->format('d-m-Y') : ''),

                    Infolists\Components\ViewEntry::make('date_of_issuance')->view('filament.components.view-field')
                        ->label('Datum van afgifte')
                        ->getStateUsing(fn () => $data['info']['date_of_issuance'] ?? null ? Carbon::parse($data['info']['date_of_issuance'])->format('d-m-Y') : ''),
                ];

                if ($type === 'identity') {
                    $return = [
                        ...$return,

                        Infolists\Components\ViewEntry::make('personal_government_id')->view('filament.components.view-field')
                            ->label('BSN')
                            ->getStateUsing(fn () => $data['info']['personal_government_id'] ?? 'Geen BSN bekend'),

                        Infolists\Components\ViewEntry::make('date_of_expiry')->view('filament.components.view-field')
                            ->label('Geldig tot')
                            ->getStateUsing(fn () => $data['info']['date_of_expiry'] ?? null ? Carbon::parse($data['info']['date_of_expiry'])->format('d-m-Y') : ''),

                        Infolists\Components\ViewEntry::make('residence_permit_type')->view('filament.components.view-field')
                            ->label('Verblijfsvergunning type')
                            ->hidden(fn () => $data['compare_documents']['residence_permit_type'] === null)
                            ->getStateUsing(fn () => $data['compare_documents']['residence_permit_type']),

                        Infolists\Components\ViewEntry::make('issuing_state_or_organization')->view('filament.components.view-field')
                            ->label('Land van uitgifte/ organisatie')
                            ->getStateUsing(fn () => $data['info']['issuing_state_or_organization'] ?? ''),
                    ];
                } else if ($type === 'driverLicense' && $data != null) {
                    $return = [
                        ...$return,

                        Infolists\Components\ViewEntry::make('personal_government_id')->view('filament.components.view-field')
                            ->label('BSN')
                            ->getStateUsing(fn () => $data['info']['personal_government_id'] ?? ''),

                        Infolists\Components\ViewEntry::make('date_of_expiration')->view('filament.components.view-field')
                            ->label('Geldig tot')
                            ->getStateUsing(fn () => $data['info']['date_of_expiration'] ?? null ? Carbon::parse($data['info']['date_of_expiration'])->format('d-m-Y') : ''),

                        Infolists\Components\ViewEntry::make('DRIVING_LICENSE')->view('filament.components.view-field')
                            ->label('Categorieën')
                            ->getStateUsing(fn () => $this->getDrivingLicenses($data['info']['classes'] ?? [])),
                    ];
                }
            }
        }

        return [
            Infolists\Components\Fieldset::make('')
                ->schema([
                    ...$return
                ]),
        ];
    }

    public function getImages($uuid = null): array
    {
        $userService = new UserService();
        if(!$uuid){
            $uuid = $this->uuid;
        }

        $images = $userService->getIdentificationImages($uuid);

        $filteredImages = array_filter($images['data'], function($image) {
            if (isset($image['attributes']['type'])) {
                $imageDocumentType = $image['attributes']['type'];
                return $imageDocumentType === 'IDENTITY_CARD' || $imageDocumentType === 'DRIVING_LICENSE' ||  $imageDocumentType === 'PASSPORT' || $imageDocumentType === 'RESIDENCE_PERMIT';
            }
            return false;
        });


        return array_reduce($filteredImages, function($carry, $image) {
            $imageDocumentType = $image['attributes']['type'];
            if (!isset($carry[$imageDocumentType])) {
                $carry[$imageDocumentType] = [];
            }

            $carry[$imageDocumentType][$image['attributes']['page_type']] = $image;
            return $carry;
        }, []);
    }

    private function getData($type = null): array|null
    {
        $dataCheckerService = New DataCheckerService();
        $data = $dataCheckerService->getMostRecentDocuments($this->uuid);

        if (empty($data)) {
            return null;
        }

        if ($type === 'identity' && isset($data['residencePermit'])) {
            if (isset($data['identity'])) {
                $identityDate = Carbon::parse($data['identity']['info']['created_at']);
                $residencePermitDate = Carbon::parse($data['residencePermit']['info']['created_at']);

                if ($residencePermitDate->isAfter($identityDate)) {
                    $type = 'residencePermit';
                }
            } else {
                $type = 'residencePermit';
            }
        }

        return $data[$type] ?? null;
    }

    public function getDrivingLicenses($licenses)
    {
        $formattedString = '';

        foreach ($licenses as $license) {
            $formattedString .= $license['name'] . ': ' . Carbon::parse($license['dateOfExpiry'])->format('d-m-Y') . "\n";
        }

        return $formattedString;
    }

    public function getMostRecentTransactionImages()
    {
        if (!$this->uuid) {
            return Infolists\Components\TextEntry::make('')
                ->getStateUsing('Geen account gevonden')
                ->hiddenLabel();
        }

        $datacheckerService = new DataCheckerService();
        $images = $datacheckerService->getMostRecentTransactionImages($this->uuid);
        if (!$images) {
            return Infolists\Components\TextEntry::make('')
                ->getStateUsing('Geen afbeeldingen gevonden')
                ->hiddenLabel();
        }

        return Infolists\Components\Fieldset::make('')
            ->schema(
                collect($images->json('images'))
                    ->map(function ($image) {
                        return Infolists\Components\ImageEntry::make('')
                            ->getStateUsing($image)
                            ->extraImgAttributes(['loading' => 'lazy'])
                            ->url($image)
                            ->openUrlInNewTab();
                    }
                )->toArray()
            );
    }

    public function placeholder(): string
    {
        return <<<HTML
        <div class="flex items-center gap-2">
            <x-filament::loading-indicator class="w-10 h-10 text-navy-500" />
            <span class="text-navy-500">Documenten ophalen...</span>
        </div>
        HTML;
    }

    public function render(): \Illuminate\Contracts\View\View
    {
        return view('livewire.data-checker-documents');
    }
}
