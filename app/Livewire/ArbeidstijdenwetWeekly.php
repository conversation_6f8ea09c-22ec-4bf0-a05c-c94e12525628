<?php

namespace App\Livewire;

use App\Models\JetApplicant;
use App\Filament\Resources\JetApplicantResource;
use Carbon\Carbon;
use Livewire\Component;

class ArbeidstijdenwetWeekly extends Component
{
    public $applicant_id;
    public $scoober_id;
    public $applicant_name;
    public $applicant_url;
    public $weeknr;
    public $total_hours;
    public function mount($data)
    {
        if(!is_array($data))
        {
            return;
        }

        $this->applicant_id = $data['applicant_id'];
        $this->weeknr = $data['weeknr'];
        $this->scoober_id = $data['driver_id'];
        $this->total_hours = $data['total_hours'];

        $applicant = JetApplicant::where('applicant_id', $this->applicant_id)->first();
        if ($applicant) {
            $this->applicant_name = $applicant->first_name . ' ' . $applicant->last_name;
        }

        $this->applicant_url = JetApplicantResource\Pages\ViewCheckJetApplicants::getUrl(['record' => $this->applicant_id]);

    }
    public function openModal()
    {
        $this->dispatch('openModalWithData', 123);
    }
    public function render()
    {
        return view('livewire.arbeidstijdenwet-weekly');
    }
}


