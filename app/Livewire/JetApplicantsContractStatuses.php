<?php

namespace App\Livewire;

use App\Models\JetApplicant;
use App\Services\SignhostService;
use Carbon\Carbon;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Http;
use Livewire\Component;

class JetApplicantsContractStatuses extends Component implements HasForms, HasInfolists, HasActions
{
    use InteractsWithForms, InteractsWithInfolists, InteractsWithActions;

    public $record = null;

    private $statuses = [
        5 => 'Waiting for document',
        10 => 'Waiting for signer',
        20 => 'In progress',
        30 => 'Signed',
        40 => 'Rejected',
        50 => 'Expired',
        60 => 'Cancelled',
        70 => 'Failed'
    ];

    public function mount(JetApplicant $record): void
    {
        $this->record = $record;
        $this->infolist->record($record);
    }

    public function infolist(Infolist $infolist): Infolist
    {
        $signhostService = new SignhostService();
        $contracts = $signhostService->getContracts($this->record->flexapp_id);
        $pendingContracts = array_filter($contracts, fn ($contract) => $contract['attributes']['status_code'] < 30);
        $pendingContractsItems = [];

        if(count($pendingContracts) > 0) {
            foreach($pendingContracts as $contract) {
                $pendingContractsItems = [
                    ...$pendingContractsItems,

                    Infolists\Components\Fieldset::make('')
                        ->extraAttributes(['class' => 'fi-fieldset--border-b'])
                        ->schema([
                            Infolists\Components\TextEntry::make('')
                                ->getStateUsing(fn () => Carbon::parse($contract['attributes']['date'])->format('H:i d-m-Y'))
                                ->extraAttributes(['class' => 'text-navy-500 font-bold'])
                                ->hiddenLabel(),

                            Infolists\Components\TextEntry::make('')
                                ->getStateUsing(fn () => $this->statuses[$contract['attributes']['status_code']] ?? 'Onbekend')
                                ->badge()
                                ->color('warning')
                                ->alignEnd(),

                            Infolists\Components\TextEntry::make('')
                                ->getStateUsing(fn () => count($contract['attributes']['files']) . ' documenten')
                                ->extraAttributes(['class' => 'text-navy-500'])
                                ->hiddenLabel(),

                            Infolists\Components\Actions::make([
                                Infolists\Components\Actions\Action::make('Annuleer contract')
                                    ->color('danger')
                                    ->link()
                                    ->requiresConfirmation()
                                    ->action(function () use ($contract, $signhostService) {
                                        $signhostService->cancelContract($contract['attributes']['transaction_id']);
                                    })
                            ])->columnSpan(2)
                        ]),
                ];
            }
        }

        return $infolist
            ->schema([
                ...(count($contracts) > 0 ? [
                    ...$pendingContractsItems,

                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('viewContracts')
                            ->label('Bekijk contracten (' . count($contracts) . ')')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->modalHeading('Contracten')
                            ->modalDescription()
                            ->modalCancelAction(false)
                            ->modalSubmitAction(false)
                            ->modalWidth(MaxWidth::TwoExtraLarge)
                            ->modalContent(view('livewire.jet-applicants-contract-statuses-modal', [
                                'contracts' => $contracts,
                                'statuses' => $this->statuses,
                            ]))
                    ])
                ] : [
                    Infolists\Components\TextEntry::make('')
                        ->getStateUsing(fn($record) => 'Er zijn nog geen contracten verstuurd')
                        ->extraAttributes(['class' => 'mb-4 px-4 py-4 bg-info-100 border-l-4 border-blue-500'])
                        ->size(Infolists\Components\TextEntry\TextEntrySize::Medium)
                ])
            ]);
    }

    public function placeholder() {
        return <<<HTML
        <div class="flex items-center gap-2">
            <x-filament::loading-indicator class="w-10 h-10 text-navy-500" />
            <span class="text-navy-500">Contracten ophalen...</span>
        </div>
        HTML;
    }

    public function render()
    {
        return view('livewire.jet-applicants-contract-statuses');
    }
}
