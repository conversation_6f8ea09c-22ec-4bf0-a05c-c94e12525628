<?php

namespace App\Livewire;

use App\Models\WenoteUser;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Concerns\InteractsWithInfolists;
use Filament\Infolists\Contracts\HasInfolists;
use Filament\Infolists\Infolist;
use Livewire\Component;

class WenoteUserNote extends Component implements HasForms, HasInfolists
{
    use InteractsWithForms, InteractsWithInfolists;

    public ?array $data = [];

    public function mount(WenoteUser $record): void
    {
        $this->form->fill($record->toArray());
        $this->infolist->record($record);
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Textarea::make('notitie')
                    ->hiddenLabel()
                    ->rows(4)
                    ->autosize(),
                Actions::make([
                    Actions\Action::make('Notitie toevoegen')
                        ->action(function($record) {
                            $newstate = $this->form->getState();

                            $record->note = $record->note . '||' . $newstate;
                            $record->save();
                        })
                        ->color('info')->outlined()
                ])
            ])
            ->statePath('data');
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                TextEntry::make('Lijst met huidige notities')
            ]);
    }

    public function render() {
        return view('livewire.wenote-user-note');
    }
}
