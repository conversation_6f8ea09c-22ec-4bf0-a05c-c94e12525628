<?php

namespace App\Livewire;

use App\Models\JetApplicant;
use App\Models\Urenbestand;
use LivewireUI\Modal\ModalComponent;

class ArbeidstijdenWetModal extends ModalComponent
{
    public JetApplicant $applicant;
    public $date;
    public string $title = 'Arbeidstijdenwet details';
    public string $component = '';
    public $total_hours;

    public function mount()
    {
        $this->total_hours = $this->getHours();
        return [$this->applicant, $this->total_hours, $this->date];
    }

    public function getHours()
    {
        $query = Urenbestand::where('driver_id', $this->applicant->scoober_id)
            ->where('absence', false);

            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $this->date)) {
                // If it's a date (YYYY-MM-DD), filter by date
                $query->where('date', $this->date);
            } elseif (preg_match('/^\d{6}$/', $this->date)) {
                // If it's a week number (YYYYWW), filter by weeknr
                $query->where('weeknr', $this->date);
            }

        return $query->get();
    }
    public function render()
    {
        return view('livewire.arbeidstijden-wet-modal');
    }
}
