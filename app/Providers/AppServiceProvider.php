<?php

namespace App\Providers;

use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;
use function Sentry\continueTrace as continueSentryTrace;
use function Sentry\getBaggage as getSentryBaggage;
use function Sentry\getTraceparent as getSentryTraceparent;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Http::globalRequestMiddleware(static function (Request $request) {
            continueSentryTrace(
                sentryTrace: $request->getHeaderLine('sentry-trace'),
                baggage: $request->getHeaderLine('baggage')
            );

            return $request;
        });

        Http::globalResponseMiddleware(static fn (Response $response) => $response
            ->withHeader('baggage', getSentryBaggage())
            ->withHeader('sentry-trace', getSentryTraceparent())
        );
    }
}
