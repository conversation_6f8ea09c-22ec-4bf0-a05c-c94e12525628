<?php
namespace App\Actions;

use App\Actions\JetActions;
use App\Services\FusionauthService;
use App\Services\JetService;
use App\Models\JetApplicant;
use App\Models\JetEnrollmentStep;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;

class JetApplicantEnrollmentActions {
    private static function getDataCheckerScanTimestamps (int $applicantId, bool $isFromDriverLicenseScans = false): array {
        $jetApplicant = JetApplicant::findOrFail($applicantId);
        $documentTypes = $isFromDriverLicenseScans ? ['driverLicense'] : ['residencePermit', 'identity'];

        $userTransactions = DB::connection('datachecker')
            ->table('transactions')
            ->where('user_id', $jetApplicant->flexapp_id)
            ->whereIn('document_type', $documentTypes)
            ->select('id as transaction_id')
            ->get()
        ;

        $userTransactions = $userTransactions->map(function ($userTransaction) {
            $userTransactionLog = DB::connection('datachecker')
                ->table('transaction_logs')
                ->where('transaction_id', $userTransaction->transaction_id)
                ->selectRaw('"type", MIN(created_at) as created_at')
                ->groupBy('transaction_id', 'type')
                ->get()
                ->flatMap(function ($userTransactionLog) {
                    $type = Str::after($userTransactionLog->type, 'manually_');
                    return ["{$type}_at" => $userTransactionLog->created_at];
                })
            ;
            return [
                'transaction_id' => $userTransaction->transaction_id,
                'created_at' => $userTransactionLog['created_at'] ?? null,
                'uploaded_at' => $userTransactionLog['uploaded_at'] ?? null,
                'completed_at' => $userTransactionLog['completed_at'] ?? null
            ];
        })->sortBy('created_at');

        $userScanTimestamps = $userTransactions->reduce(function (array $carry, array $item) {
            if (!$item['uploaded_at'] && $item['completed_at']) {
                $carry['manually_completed_at'] = $item['completed_at'];
                return $carry;
            }

            $carry['created_at'] ??= $item['created_at'];
            if (!$carry['uploaded_at']) {
                $carry['uploaded_at'] = $item['uploaded_at'];
                $carry['completed_at'] = $item['completed_at'];
            }
            return $carry;
        }, [
            'created_at' => null,
            'uploaded_at' => null,
            'completed_at' => null,
            'manually_completed_at' => null
        ]);
        return $userScanTimestamps;
    }

    #region Applicant Enrollment Actions
    public static function startStep (int $applicantId, int $stepNumber, bool $skipStepInProcess = false, ?string $startedAt = null) {
        if (JetEnrollmentStep::where('step_id', $stepNumber)->doesntExist()) {
            throw new \Exception('The stepNumber is not valid.');
        }

        $enrollmentStatusNew = JetApplicant::findOrFail($applicantId)->enrollmentStatusNew;
        if (!$skipStepInProcess) { $enrollmentStatusNew->step_in_process = $stepNumber; }

        $startedAt ??= Carbon::now()->toDateTimeString();
        $enrollmentStatusNew->{'step_' . $stepNumber . '_started_at'} = $startedAt;
        $enrollmentStatusNew->{'step_' . $stepNumber . '_finished_at'} = null;

        $enrollmentStatusNew->save();
    }

    public static function finishStep (int $applicantId, int $stepNumber, bool $skipStepInProcess = false, ?string $finishedAt = null) {
        if (JetEnrollmentStep::where('step_id', $stepNumber)->doesntExist()) {
            throw new \Exception('The stepNumber is not valid.');
        }

        $enrollmentStatusNew = JetApplicant::findOrFail($applicantId)->enrollmentStatusNew;
        if (!$skipStepInProcess) { $enrollmentStatusNew->step_in_process = $stepNumber; }

        $finishedAt ??= Carbon::now()->toDateTimeString();
        $enrollmentStatusNew->{'step_' . $stepNumber . '_started_at'} ??= $finishedAt;
        $enrollmentStatusNew->{'step_' . $stepNumber . '_finished_at'} = $finishedAt;

        $enrollmentStatusNew->save();
    }

    public static function resetStep (int $applicantId, int $stepNumber) {
        if (JetEnrollmentStep::where('step_id', $stepNumber)->doesntExist()) {
            throw new \Exception('The stepNumber is not valid.');
        }

        $enrollmentStatusNew = JetApplicant::findOrFail($applicantId)->enrollmentStatusNew;
        $enrollmentStatusNew->{'step_' . $stepNumber . '_started_at'} = null;
        $enrollmentStatusNew->{'step_' . $stepNumber . '_finished_at'} = null;

        $enrollmentStatusNew->save();
    }
    #endregion

    #region Sync Steps In Progress
    // Step 4
    public static function syncActivateAccountStep () {
        $applicantsToSync = JetApplicant::whereHas('enrollmentStatusNew', function (Builder $query) {
            $query
                ->whereRaw('on_hold IS NOT true')
                ->where('step_in_process', 4)
                ->whereNotNull('step_4_started_at')
                ->whereNull('step_4_finished_at')
            ;
        })->get();

        foreach ($applicantsToSync as $applicant) {
            try {
                // Use nullsafe operator to safely access flexappData properties
                if ($applicant->flexappData?->updated_at != $applicant->flexappData?->created_at) {
                    $accountActivatedAt = $applicant->flexappData->updated_at;
                    self::finishStep($applicant->applicant_id, 4, false, $accountActivatedAt);
                    self::startStep($applicant->applicant_id, 5, false, $accountActivatedAt);
                }
            } catch (\Throwable $exception) {
                report($exception);
                continue;
            }
        }
    }

    // Step 5
    public static function syncFinalizeProfileStep () {
        $applicantsToSync = JetApplicant::whereHas('enrollmentStatusNew', function (Builder $query) {
            $query
                ->whereRaw('on_hold IS NOT true')
                ->where('step_in_process', 5)
                ->whereNotNull('step_5_started_at')
                ->whereNull('step_5_finished_at')
            ;
        })->get();

        foreach ($applicantsToSync as $applicant) {
            try {
                if ($applicant->flexappData->info->has_finalize_profile) {
                    $profileFinalizedAt = $applicant->flexappData->info->has_finalize_profile_at;
                    self::finishStep($applicant->applicant_id, 5, false, $profileFinalizedAt);
                    self::startStep($applicant->applicant_id, 6, false, $profileFinalizedAt);
                }
            } catch (\Throwable $exception) {
                report($exception);
                continue;
            }
        }
    }

    // Step 6 through 9
    public static function syncDataCheckerSteps () {
        $applicantsToSync = JetApplicant::whereHas('enrollmentStatusNew', function (Builder $query) {
            $query
                ->whereRaw('on_hold IS NOT true')
                ->whereBetween('step_in_process', [6, 9])
            ;
        })->get();

        foreach ($applicantsToSync as $applicant) {
            try {
                $idScanTimestamps = self::getDataCheckerScanTimestamps($applicant->applicant_id);
                $verifyIdDataCheckerFinishedAt = null;

                // Scan ID:
                // if ($idScanTimestamps['created_at']) {
                //     self::startStep($applicant->applicant_id, 6, false, $idScanTimestamps['created_at']);
                // }

                if ($idScanTimestamps['uploaded_at']) {
                    self::finishStep($applicant->applicant_id, 6, false, $idScanTimestamps['uploaded_at']);
                    self::startStep($applicant->applicant_id, 7, false, $idScanTimestamps['uploaded_at']);
                }

                // Verify ID (DataChecker):
                if ($idScanTimestamps['manually_completed_at'] > $idScanTimestamps['completed_at']) {
                    $verifyIdDataCheckerFinishedAt = $idScanTimestamps['manually_completed_at'];
                    self::resetStep($applicant->applicant_id, 6);
                    self::finishStep($applicant->applicant_id, 7, false, $verifyIdDataCheckerFinishedAt);
                } else if ($idScanTimestamps['completed_at']) {
                    $verifyIdDataCheckerFinishedAt = $idScanTimestamps['completed_at'];
                    self::finishStep($applicant->applicant_id, 7, false, $verifyIdDataCheckerFinishedAt);
                }


                $verifyDriverLicenseDataCheckerFinishedAt = null;
                if ($applicant->flexappData->info->driving_license_required) {
                    $driverLicenseScanTimestamps = self::getDataCheckerScanTimestamps($applicant->applicant_id, true);

                    // Scan Driver License:
                    if (!$driverLicenseScanTimestamps['created_at'] && $verifyIdDataCheckerFinishedAt) {
                        self::startStep($applicant->applicant_id, 8, false, $verifyIdDataCheckerFinishedAt);
                    } else if ($driverLicenseScanTimestamps['created_at']) {
                        self::startStep($applicant->applicant_id, 8, false, $driverLicenseScanTimestamps['created_at']);
                    }

                    if ($driverLicenseScanTimestamps['uploaded_at']) {
                        self::finishStep($applicant->applicant_id, 8, false, $driverLicenseScanTimestamps['uploaded_at']);
                        self::startStep($applicant->applicant_id, 9, false, $driverLicenseScanTimestamps['uploaded_at']);
                    }

                    // Verify Driver License (DataChecker):
                    if ($driverLicenseScanTimestamps['manually_completed_at'] > $driverLicenseScanTimestamps['completed_at']) {
                        $verifyDriverLicenseDataCheckerFinishedAt = $driverLicenseScanTimestamps['manually_completed_at'];
                        self::resetStep($applicant->applicant_id, 8);
                        self::finishStep($applicant->applicant_id, 9, false, $verifyDriverLicenseDataCheckerFinishedAt);
                    } else if ($driverLicenseScanTimestamps['completed_at']) {
                        $verifyDriverLicenseDataCheckerFinishedAt = $driverLicenseScanTimestamps['completed_at'];
                        self::finishStep($applicant->applicant_id, 9, false, $verifyDriverLicenseDataCheckerFinishedAt);
                    }
                }

                // Verify Scans (start):
                if ($applicant->flexappData->info->driving_license_required && $verifyIdDataCheckerFinishedAt && $verifyDriverLicenseDataCheckerFinishedAt) {
                    if ($verifyIdDataCheckerFinishedAt > $verifyDriverLicenseDataCheckerFinishedAt) {
                        self::startStep($applicant->applicant_id, 10, false, $verifyIdDataCheckerFinishedAt);
                    } else {
                        self::startStep($applicant->applicant_id, 10, false, $verifyDriverLicenseDataCheckerFinishedAt);
                    }
                } else if (!$applicant->flexappData->info->driving_license_required && $verifyIdDataCheckerFinishedAt) {
                    self::startStep($applicant->applicant_id, 10, false, $verifyIdDataCheckerFinishedAt);
                }
            } catch (\Throwable $exception) {
                report($exception);
                continue;
            }
        }
    }

    // Step 12
    public static function syncSignContractStep () {
        $applicantsToSync = JetApplicant::whereHas('enrollmentStatusNew', function (Builder $query) {
            $query
                ->whereRaw('on_hold IS NOT true')
                ->where('agency_status', '!=', 'Rejected')
                ->where('step_in_process', 12)
                ->whereNotNull('step_12_started_at')
                ->whereNull('step_12_finished_at')
            ;
        })->get();
        foreach ($applicantsToSync as $applicant) {
            try {
                $response = Http::withHeaders([
                    'Accept' => 'application/vnd.api+json',
                    'Content-Type' => 'application/vnd.api+json',
                ])
                    ->withToken(FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.signhost_service')))
                    ->baseUrl(config('services.signhost_service.url'))
                    ->throw()
                    ->get('/get_jet_contract_status/' . $applicant->flexapp_id)
                ;

                $responseJson = $response->json();
                if($responseJson['contracts_signed'] === true){
                    self::finishStep($applicant->applicant_id, 12);
                    self::startStep($applicant->applicant_id, 13);
                }

            } catch (\Throwable $exception) {
                report($exception->getMessage());
                continue;
            }
        }
    }

    // Step 13
    public static function syncSendRegistrationToJetStep () {
        $applicantsToSync = JetApplicant::whereHas('enrollmentStatusNew', function (Builder $query) {
            $query
                ->whereRaw('on_hold IS NOT true')
                ->where('step_in_process', 13)
                ->whereNotNull('step_13_started_at')
                ->whereNull('step_13_finished_at')
            ;
        })->get();
        foreach ($applicantsToSync as $applicant) {
            try {
                JetActions::approveApplicant($applicant->applicant_id);
                self::finishStep($applicant->applicant_id, 13);
                self::startStep($applicant->applicant_id, 14);
                if (!$applicant->scoober_id) {
                    self::startStep($applicant->applicant_id, 15, true);
                }
            } catch (\Throwable $exception) {
                report($exception);
                continue;
            }
        }
    }

    // Step 15
    public static function syncFetchScooberIdStep () {
        $jetService = new JetService;
        $applicantsToSync = JetApplicant::whereNull('scoober_id')
            ->whereRaw('is_import IS NOT true')
            ->whereHas('enrollmentStatusNew', function (Builder $query) {
                $query
                    ->whereRaw('on_hold IS NOT true')
                    ->whereNotNull('step_15_started_at')
                    ->whereNull('step_15_finished_at')
                ;}
            )
            ->get()
        ;
        foreach ($applicantsToSync as $applicant) {
            try {
                $applicantDTO = $jetService->fetchApplicant($applicant->lead_id);
                if ($applicantDTO->contact_id && $applicantDTO->scoober_id) {
                    $applicant->contact_id = $applicantDTO->contact_id;
                    $applicant->scoober_id = $applicantDTO->scoober_id;
                    $applicant->save();

                    self::finishStep($applicant->applicant_id, 15, true);
                }
            } catch (\Throwable $exception) {
                report($exception);
                continue;
            }
        }
    }
    #endregion

    #region Extra syncs
    public static function syncApplicantsDrivingLicense () {
        $applicantsToSync = JetApplicant::whereHas('enrollmentStatusNew', function (Builder $query) {
                $query
                    ->whereRaw('on_hold IS NOT true')
                    ->whereNotNull('step_10_started_at')
                    ->whereNotNull('step_10_finished_at')
                ;
            })
            ->whereIn('active_transportation_type', ['Motorcycle / Scooter', 'E-Roller', 'Car / Kombi', 'Buffer Vehicle'])
            ->whereNull('driving_license_number')
            ->whereNull('driving_license_expiration_date')
            ->get()
        ;

        foreach ($applicantsToSync as $applicant) {
            try {
                if (
                    $applicant->flexappData->drivingLicense &&
                    $applicant->flexappData->drivingLicense->date_of_expiry &&
                    $applicant->flexappData->drivingLicense->document_number
                ) {
                    if (!$applicant->is_import) {
                        $jetService = new JetService;
                        $jetService->updateApplicantDrivingLicense($applicant->lead_id, $applicant->flexappData->drivingLicense->document_number, $applicant->flexappData->drivingLicense->date_of_expiry);
                    }

                    $applicant->driving_license_expiration_date = $applicant->flexappData->drivingLicense->date_of_expiry;
                    $applicant->driving_license_number = $applicant->flexappData->drivingLicense->document_number;
                    $applicant->save();
                }
            } catch (\Throwable $exception) {
                report($exception);
                continue;
            }
        }
    }
    #endregion
}
