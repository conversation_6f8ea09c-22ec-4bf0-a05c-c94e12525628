<?php

namespace App\Actions;

use App\Models\Flexapp\UserInfo;
use App\Models\JetApplicantEnrollmentStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Log;

class ReminderActions
{
    public static function sendRegistrationReminders(): bool
    {
        #region Find applicants that need the reminder
        $startOfToday = Carbon::today();
        $daysAgo3 = Carbon::now()
            ->subDays(3)
            ->setTime(11, 0);

        $applicants = JetApplicantEnrollmentStatus::where('step_in_process', 3)
            ->where('agency_status', 'New')
            ->whereBetween('step_3_started_at', [$daysAgo3, $startOfToday])
            ->where(
                fn (Builder $query) => $query
                    ->where('on_hold', false)
                    ->orWhereNull('on_hold')
            )
            ->with('applicant:applicant_id,flexapp_id')
            ->get()
            ->pluck('applicant.flexapp_id');

        $incompleteRegistrationUuids = UserInfo::whereIn('uuid', $applicants)
            ->where(
                fn (Builder $query) => $query
                    ->where('final_information_check', false)
                    ->orWhereNull('final_information_check')
            )
            ->pluck('uuid');
        #endregion

        $reminderCount = $incompleteRegistrationUuids->count();
        Log::info(
            'sendRegistrationReminders: Count of applicants to remind: '
            . $reminderCount
        );

        if (0 === $reminderCount) {
            return false;
        }

        #region Send the reminders
        // Can send in bulk, but eventually it needs to be individual calls to FusionAuth anyway.
        // In a different usecase, this is what FusionAuth itself recommends, with some sleep():
        // https://fusionauth.io/docs/lifecycle/migrate-users/provider-specific/cognito#reset-passwords
        foreach ($incompleteRegistrationUuids as $uuid) {
            try {
                UserActions::sendJETRegistrationReminder($uuid);
                sleep(1);
            } catch (\Throwable $th) {
                Log::error("sendRegistrationReminders: Failed sending reminder to user $uuid");
                Log::error($th);
            }
        }
        #endregion

        return true;
    }
}
