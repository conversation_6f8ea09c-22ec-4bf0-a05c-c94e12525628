<?php
namespace App\Actions;

use App\Http\Controllers\ExportController;
use App\Http\Controllers\PlaatsingController;
use App\Jobs\ExportJob;
use App\Models\LoonComponent;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class CsvActions{

    private $export;
    private $plaatsing;

    public function __construct(ExportController $export, PlaatsingController $plaatsing)
    {
        $this->export = $export;
        $this->plaatsing = $plaatsing;
    }
    public function exportAction($data)
    {
        $looncomponent = LoonComponent::find($data->loon_component_id);
        $looncomponenttype_id = $looncomponent->looncomponenttype->id;
        $filetype = strtolower($looncomponent->looncomponenttype->name);

        $check = $this->export->checkPlaatsingen($data->id);
        if(!$check){
            return Notification::make()
                ->title('Niet alle plaatsingen zijn ingevuld, export nog niet mogelijk.')
                ->persistent()
                ->danger()
                ->send();
        }
        $filename = $filetype.'/'.str_replace(' ', '_', $filetype).'_export_bestand_'.Carbon::now()->timestamp.'.csv';
        ExportJob::dispatch($data->id, $looncomponenttype_id, $filename, auth()->user()->id);
        return Notification::make()
            ->title('Export wordt op de achtergrond uitgevoerd.')
            ->persistent()
            ->success()
            ->send();
//        return $this->export->createExport($data->id, $looncomponenttype_id, $filename, auth()->user()->id);
    }

    public function exportBulkAction($records, $looncomponenttype_override = null, $filetype = null)
    {
        $checkComponentType = null;
        $looncomponenttype_id = $looncomponenttype_override;
        foreach($records as $record){
            if(!$looncomponenttype_override){
                $looncomponent = LoonComponent::find($record->loon_component_id);
                if(!$looncomponent){
                    return Notification::make()
                        ->title('Geen looncomponent gevonden')
                        ->persistent()
                        ->failed()
                        ->send();
                }
                $looncomponenttype_id = $looncomponent->looncomponenttype->id;
                if ($checkComponentType === null) {
                    $checkComponentType = $looncomponenttype_id;
                    $filetype = $looncomponent->looncomponenttype->name;
                    $filetype = strtolower($filetype);
                } else {
                    if ($looncomponenttype_id !== $checkComponentType) {
                        $looncomponenttype_id = 123;
                    }
                }
            }
            $import_ids[] = $record->id;
        }

        $filetype = $filetype ?? 'bulk';
        if($looncomponenttype_id === 2){
            $filetype = 'Uren';
        }
        if($looncomponenttype_id === 3){
            $filetype = 'declaraties';
        }
        if($looncomponenttype_id === 1){
            $filetype = 'Absentie';
        }
        if($looncomponenttype_id === 7){
            $filetype = 'Onbetaald Verlof';
        }
        $filename = $filetype.'/'.str_replace(' ', '_', $filetype).'_export_bestand_'.Carbon::now()->timestamp.'.csv';
//        return $this->export->createExport($import_ids, $looncomponenttype_id, $filename, auth()->user()->id);
        ExportJob::dispatch($import_ids, $looncomponenttype_id, $filename, auth()->user()->id);

        return Notification::make()
            ->title('Export wordt op de achtergrond uitgevoerd.')
            ->persistent()
            ->success()
            ->send();
    }
}
