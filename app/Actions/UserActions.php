<?php
namespace App\Actions;

use App\Services\UserService;

class UserActions {
    public static function sendJETRegistrationReminder (string $flexapp_id) {
        $userService = new UserService;
        $userService->sendJETRegistrationReminder($flexapp_id);
    }

    public static function updateUserEmailAddress (string $flexapp_id, string $newEmailAddress) {
        $userService = new UserService;
        $userService->updateUserEmailAddress($flexapp_id, $newEmailAddress);
    }
}
