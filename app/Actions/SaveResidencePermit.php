<?php

namespace App\Actions;

use App\DTO\ResidencePermit;
use App\Enums\CourierDocumentRecordType;
use App\Enums\CourierDocumentType;
use App\Enums\JetEntity;
use App\Http\Client\JetClient;
use App\Models\JetApplicant;
use App\Models\JetCourierDocumentRequestLog;
use App\Services\DataCheckerService;
use App\Services\JetService;
use Closure;
use GuzzleHttp\Utils;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Client\RequestException;
use Illuminate\Http\Client\Response as ClientResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Pipeline;
use Sentry;
use Sentry\Breadcrumb;
use Sentry\Severity;
use Sentry\State\Scope;
use Throwable;

use function Sentry\captureMessage as captureSentryMessage;
use function Sentry\configureScope as configureSentryScope;

class SaveResidencePermit
{
    private DataCheckerService $client;

    private function __construct()
    {
        $this->client = app(DataCheckerService::class);
    }

    public static function forApplicant(JetApplicant $applicant): ?ResidencePermit
    {
        $instance = new self();
        return $instance->process($applicant);
    }

    public function process(JetApplicant $applicant): ?ResidencePermit
    {
        try {
            $response = $this->client->getResidentPermitForApplicant($applicant);
        }
        /** @var RequestException $exception */
        catch (Throwable $exception) {
            $breadcrumb = new Breadcrumb(
                level: Breadcrumb::LEVEL_ERROR,
                type: Breadcrumb::TYPE_ERROR,
                category: class_basename(self::class),
                message: 'No datachecker results found for this applicant.',
                metadata: [
                    'lead_id' => $applicant->lead_id,
                    'flexapp_id' => $applicant->flexapp_id,
                    'document_type' => 'residence_permit',
                ],
            );

            configureSentryScope(fn (Scope $scope) => $scope
                ->addBreadcrumb($breadcrumb)
                ->setContext('fetch_residence_permit_information', [
                    'applicant' => $applicant->flexapp_id,
                ])
            );

            captureSentryMessage($exception->response->json('message'), Severity::error());

            return null;
        }

        $permit = ResidencePermit::from([
            'transactionId' => $response->json('transaction_id'),
            'Name' => 'Residence permit',
            'Lead__c' => $applicant->lead_id,
            'flexappId' => $applicant->flexapp_id,
            'Type__c' => CourierDocumentType::WORK_PERMIT->value,
            'filePath' => 'residence-permit.pdf',
            'fileData' => $response->json('file_data'),
            'documentNumber' => $response->json('document_number'),
            'documentExpiresAt' => $response->json('document_expires_at'),
            'residencePermitType' => $response->json('residence_permit_type')
        ]);

        $applicant->jetCourierDocumentRequestLogs()
            ->whereIn(JetCourierDocumentRequestLog::HTTP_STATUS_CODE, [Response::HTTP_OK, Response::HTTP_CREATED])
            ->select(JetCourierDocumentRequestLog::ENTITY, JetCourierDocumentRequestLog::ENTITY_ID)
            ->get()
            ->each(fn (JetCourierDocumentRequestLog $log): ResidencePermit => $permit->setIdFor($log->entity, $log->entity_id));

        return Pipeline::send($permit)
            ->through([
                // create document entity
                function (ResidencePermit $permit, Closure $next) {
                    if (SaveResidencePermit::hasPreviouslySucceededProcessing(JetEntity::COURIER_DOCUMENT, $permit)) {
                        return $next($permit);
                    }

                    try {
                        $response = (new JetClient())->client()->post(JetEntity::COURIER_DOCUMENT->apiUrl(), [
                            'Name' => $permit->Name,
                            'Lead__c' => $permit->Lead__c,
                            'Type__c' => $permit->Type__c,
                            'RecordTypeId' => CourierDocumentRecordType::SENSITIVE_HIRING_DATA->getId(),
                        ]);
                    } /** @var RequestException $exception */
                    catch (Throwable $exception) {
                        SaveResidencePermit::logException($exception, $permit, JetEntity::COURIER_DOCUMENT);

                        throw $exception;
                    }

                    $permit->courierDocumentId = $response->json('id');

                    SaveResidencePermit::logRecord($response, $permit, JetEntity::COURIER_DOCUMENT);

                    return $next($permit);
                },
                // create document content entity with content
                function (ResidencePermit $permit, Closure $next) {
                    if (SaveResidencePermit::hasPreviouslySucceededProcessing(JetEntity::CONTENT_VERSION, $permit)) {
                        return $next($permit);
                    }

                    try {
                        $response = (new JetClient())->client()
                            ->createDocumentData(
                                title: $permit->Name,
                                path: $permit->filePath,
                                location: JetEntity::SALES_FORCE_CONTENT_LOCATION,
                                data: $permit->fileData,
                            );
                    }
                        /** @var RequestException $exception */
                    catch (Throwable $exception) {
                        SaveResidencePermit::logException($exception, $permit, JetEntity::CONTENT_VERSION);

                        throw $exception;
                    }

                    $permit->contentVersionId = $response->json('id');

                    SaveResidencePermit::logRecord($response, $permit, JetEntity::CONTENT_VERSION);

                    return $next($permit);
                },
                // retrieve the id for the document content object
                function (ResidencePermit $permit, Closure $next) {
                    if (SaveResidencePermit::hasPreviouslySucceededProcessing(JetEntity::QUERY, $permit)) {
                        return $next($permit);
                    }

                    try {
                        $response = (new JetClient())->client()->getContentDocumentIdByContentVersion(id: $permit->contentVersionId);

                        abort_if(
                            $response->json('totalSize') !== 1,
                            Response::HTTP_UNPROCESSABLE_ENTITY,
                            'expected one query result, got ' . $response->json('totalSize')
                        );

                        $permit->contentDocumentId = $response->json('records.0.ContentDocumentId');

                        SaveResidencePermit::logRecord($response, $permit, JetEntity::QUERY, Request::METHOD_GET);
                    }
                        /** @var RequestException $exception */
                    catch (Throwable $exception) {
                        SaveResidencePermit::logException($exception, $permit, JetEntity::CONTENT_VERSION, Request::METHOD_GET);

                        throw $exception;
                    }

                    return $next($permit);
                },
                // link the document object and the document content object
                function (ResidencePermit $permit, Closure $next) {
                    if (SaveResidencePermit::hasPreviouslySucceededProcessing(JetEntity::CONTENT_DOCUMENT_LINK, $permit)) {
                        return $next($permit);
                    }

                    try {
                        $response = (new JetClient())->client()
                            ->post(JetEntity::CONTENT_DOCUMENT_LINK->apiUrl(), [
                                'ContentDocumentId' => $permit->contentDocumentId,
                                'LinkedEntityId' => $permit->courierDocumentId,
                                'Visibility' => 'AllUsers',
                            ]);
                    }
                        /** @var RequestException $exception */
                    catch (Throwable $exception) {
                        SaveResidencePermit::logException($exception, $permit, JetEntity::CONTENT_DOCUMENT_LINK);

                        throw $exception;
                    }

                    $permit->contentDocumentLinkId = $response->json('id');

                    SaveResidencePermit::logRecord($response, $permit, JetEntity::CONTENT_DOCUMENT_LINK);

                    return $next($permit);
                },
                function (ResidencePermit $permit, Closure $next) use ($applicant) {
                    if (
                        $applicant->residence_permit_number === $permit->documentNumber &&
                        $applicant->residence_permit_expiration_date === $permit->documentExpiresAt &&
                        $applicant->residence_permit_type === $permit->residencePermitType
                    ) {
                        return $next($permit);
                    }

                    $jetService = new JetService;
                    $jetService->updateApplicantWorkPermit(
                        $applicant->lead_id,
                        $permit->documentNumber,
                        $permit->documentExpiresAt,
                        $permit->residencePermitType
                    );
                    $applicant->update([
                        'residence_permit_number' => $permit->documentNumber,
                        'residence_permit_expiration_date' => $permit->documentExpiresAt,
                        'residence_permit_type'  => $permit->residencePermitType
                    ]);
                    return $next($permit);
                }
            ])
            ->thenReturn();
    }

    public static function hasPreviouslySucceededProcessing(JetEntity $entity, ResidencePermit $permit): bool
    {
        return JetApplicant::query()
            ->where('lead_id', $permit->Lead__c)
            ->whereHas('jetCourierDocumentRequestLogs', fn (Builder $query) => $query
                ->where(JetCourierDocumentRequestLog::TRANSACTION_ID, $permit->transactionId)
                ->where(JetCourierDocumentRequestLog::ENTITY, $entity->value),
                '>', 0
            )
            ->exists();
    }

    public static function logRecord(ClientResponse $response, ResidencePermit $permit, JetEntity $entity, $method = Request::METHOD_POST): JetCourierDocumentRequestLog
    {
        $id = match ($entity) {
            JetEntity::COURIER_DOCUMENT => $permit->courierDocumentId,
            JetEntity::CONTENT_VERSION => $permit->contentVersionId,
            JetEntity::QUERY => $permit->contentDocumentId,
            JetEntity::CONTENT_DOCUMENT_LINK => $permit->contentDocumentLinkId,
        };

        return JetCourierDocumentRequestLog::query()
            ->create([
                JetCourierDocumentRequestLog::TRANSACTION_ID => $permit->transactionId,
                JetCourierDocumentRequestLog::LEAD_ID => $permit->Lead__c,
                JetCourierDocumentRequestLog::ENTITY => $entity,
                JetCourierDocumentRequestLog::ENTITY_ID => $id,
                JetCourierDocumentRequestLog::HTTP_METHOD => $method,
                JetCourierDocumentRequestLog::HTTP_STATUS_CODE => $response->status(),
                JetCourierDocumentRequestLog::PAYLOAD => $permit->except('fileData'),
            ]);
    }

    public static function logException(RequestException $exception, ResidencePermit $permit, JetEntity $entity, string $method = Request::METHOD_POST): JetCourierDocumentRequestLog
    {
        $breadcrumb = new Breadcrumb(
            level: Breadcrumb::LEVEL_ERROR,
            type: Breadcrumb::TYPE_ERROR,
            category: class_basename(self::class),
            message: 'process step: ' . $entity->value,
            metadata: [
                'lead_id' => $permit->Lead__c,
                'flexapp_id' => $permit->flexappId,
                'transaction_id' => $permit->transactionId,
            ],
        );

        configureSentryScope(fn (Scope $scope) => $scope
            ->addBreadcrumb($breadcrumb)
            ->setContext('save_residence_permit', [
                'message' => $exception->response->json('0.message'),
                'error_code' => $exception->response->json('0.errorCode'),
                'fields' => Utils::jsonEncode($exception->response->json('0.fields')),
            ])
        );

        captureSentryMessage('Error during the residence permit process', Severity::error());

        return JetCourierDocumentRequestLog::query()
            ->create([
                JetCourierDocumentRequestLog::TRANSACTION_ID => $permit->transactionId,
                JetCourierDocumentRequestLog::LEAD_ID => $permit->Lead__c,
                JetCourierDocumentRequestLog::ENTITY => $entity->value,
                JetCourierDocumentRequestLog::HTTP_METHOD => $method,
                JetCourierDocumentRequestLog::HTTP_STATUS_CODE => $exception->response->status(),
                JetCourierDocumentRequestLog::PAYLOAD => $permit->except('fileData'),
                JetCourierDocumentRequestLog::ERROR_CODE => $exception->response->json('0.errorCode'),
                JetCourierDocumentRequestLog::ERROR_MESSAGE => $exception->response->json('0.message'),
                JetCourierDocumentRequestLog::ERROR_FIELDS => json_encode($exception->response->json('*.fields')),
            ]);
    }
}
