<?php

namespace App\Jobs;

use App\Http\Controllers\PlaatsingController;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class StorePlaatsingenJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    private int $import_id;

    /**
     * Create a new job instance.
     */
    public function __construct($import_id)
    {
        $this->import_id = $import_id;
        $this->onConnection('database');
        $this->onQueue('default');
    }

    /**
     * Execute the job.
     */
    public function handle(PlaatsingController $plaatsing): void
    {
        $plaatsing->storePlaatsing($this->import_id);
    }
}
