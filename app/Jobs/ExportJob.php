<?php

namespace App\Jobs;

use App\Http\Controllers\ExportController;
use App\Services\EasyflexService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ExportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int|array $import_id;
    public int $looncomponent_type_id;
    public string $filename;
    public int $user_id;
    public int $timeout = 600; // 10 minutes

    /**
     * Create a new job instance.
     */
    public function __construct($import_id, $looncomponent_type_id, $filename, $user_id)
    {
        $this->import_id = $import_id;
        $this->looncomponent_type_id = $looncomponent_type_id;
        $this->filename = $filename;
        $this->user_id = $user_id;
        $this->onConnection('database');
        $this->onQueue('export');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $easyflex = new EasyflexService();
        $export = new ExportController($easyflex);
        $data = $export->createExport($this->import_id, $this->looncomponent_type_id, $this->filename, $this->user_id);
        $ids = $this->import_id;
        if(is_array($this->import_id)){
            $ids = implode(', ', $this->import_id);
        }
        if($data === true){
            Log::info(
                'Export with import id '.$ids.' was successful'
            );
        }else{
            Log::info(
                'Export with import id '.$ids.' failed. Reason: '.$data
            );
        }
    }
}
