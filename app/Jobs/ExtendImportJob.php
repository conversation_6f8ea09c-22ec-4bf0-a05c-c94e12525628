<?php

namespace App\Jobs;

use App\Models\Urenbestand;
use Filament\Actions\Imports\Jobs\ImportCsv;
use Illuminate\Support\Facades\Mail;

class ExtendImportJob extends ImportCsv
{
    /**
     * Execute the job.
     */
    public function handle(): void
    {
        parent::handle();
        $import_id = $this->import->getKey();
        $urenbestand = Urenbestand::where('import_id', $import_id)->first();
        if($urenbestand->looncomponent_id === 1){
            CreatePlaatsingJob::dispatch($import_id);
        }
    }
}
