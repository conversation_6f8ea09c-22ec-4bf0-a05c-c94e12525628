<?php

namespace App\Jobs;

use App\Actions\CsvActions;
use App\Http\Controllers\ExportController;
use App\Http\Controllers\PlaatsingController;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CreatePlaatsingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $import_id;
    /**
     * Create a new job instance.
     */
    public function __construct($import_id)
    {
        $this->import_id = $import_id;
    }

    /**
     * Execute the job.
     */
    public function handle(ExportController $export): void
    {
        $import_id = $this->import_id;
        $filename = 'plaatsingen/plaatsingen_export_bestand_'.Carbon::now()->timestamp.'.csv';
        $export->createExport($import_id, 99, $filename);
    }
}
