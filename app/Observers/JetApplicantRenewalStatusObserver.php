<?php

namespace App\Observers;

use App\Models\ContractRenewal;
use App\Models\JetApplicantRenewalStatus;
use App\Services\CommunicationService;
use Carbon\AbstractTranslator;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class JetApplicantRenewalStatusObserver
{
    private CommunicationService $communicationService;

    public function __construct(CommunicationService $communicationService)
    {
        $this->communicationService = $communicationService;
    }
    /**
     * Handle the JetApplicantRenewalStatus "created" event.
     */
    public function created(JetApplicantRenewalStatus $jetApplicantRenewalStatus): void
    {
        //
    }

    /**
     * Handle the JetApplicantRenewalStatus "updated" event.
     */
    public function updated(JetApplicantRenewalStatus $jetApplicantRenewalStatus): void
    {
        if ($jetApplicantRenewalStatus->isDirty('step_7_finished_at')) {
            $applicantId = $jetApplicantRenewalStatus->applicant->applicant_id;
            $contractRenewalData = ContractRenewal::where('applicant_id', $applicantId)->first();
            if ($contractRenewalData) {
                $contractRenewalData['scoober_id'] = $jetApplicantRenewalStatus->applicant->scoober_id;
                $contractRenewalData['signature_date'] = $jetApplicantRenewalStatus->step_7_finished_at;
                $contractRenewalData['first_name'] = $jetApplicantRenewalStatus->applicant->flexappData->personalData->first_name;
                $contractRenewalData['last_name'] = $jetApplicantRenewalStatus->applicant->flexappData->personalData->last_name;
                $this->communicationService->sendContractRenewalConfirmation($contractRenewalData);
            } else {
                Log::warning("No contract renewal found for applicant ID: $applicantId");
            }
        }
    }

    /**
     * Handle the JetApplicantRenewalStatus "deleted" event.
     */
    public function deleted(JetApplicantRenewalStatus $jetApplicantRenewalStatus): void
    {
        //
    }

    /**
     * Handle the JetApplicantRenewalStatus "restored" event.
     */
    public function restored(JetApplicantRenewalStatus $jetApplicantRenewalStatus): void
    {
        //
    }

    /**
     * Handle the JetApplicantRenewalStatus "force deleted" event.
     */
    public function forceDeleted(JetApplicantRenewalStatus $jetApplicantRenewalStatus): void
    {
        //
    }
}
