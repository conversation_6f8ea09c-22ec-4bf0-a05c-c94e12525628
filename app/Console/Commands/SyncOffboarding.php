<?php

namespace App\Console\Commands;

use App\Models\EmployeeOffboarding;
use App\Models\JetApplicant;
use App\Models\Plaatsing;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class SyncOffboarding extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-offboarding';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $csvFile = Storage::path('passief.csv');
        if (($handle = fopen($csvFile, 'r')) !== false) {
            while (($row = fgetcsv($handle, 1000, ',')) !== false) {
                if($row[1] === 'Plaats'){
                    continue;
                }
//                dd($row);

                $easyflex_id = $row[5];
                if($row[10] == ''){
                    $this->error('Einddatum plaatsing is empty');
                    continue;
                }
                $einddatum_plaatsing = Carbon::createFromFormat('m/d/y', $row[10])->format('Y-m-d');

                $jetApplicant = JetApplicant::where('easyflex_id', $easyflex_id)->first();

                if (!$jetApplicant) {
                    $this->error('JetApplicant not found for Easyflex ID: ' . $easyflex_id);
                    continue;
                }

                $applicant_id = $jetApplicant->applicant_id;

                $plaatsings = Plaatsing::where('applicant_id', $applicant_id)
                    ->where('offboard_date', null)
                    ->get();

                if ($plaatsings->isEmpty()) {
                    $plaatsing_ids = [];
                }else{
                    $plaatsing_ids = $plaatsings->pluck('plaatsing_id')->toArray();
                    foreach($plaatsings as $plaatsing){
                        $plaatsing->offboard_date = $einddatum_plaatsing;
                        $plaatsing->update();
                    }
                }

                $checkOffboarding = EmployeeOffboarding::where('applicant_id', $applicant_id)
                    ->where('offboarding_date', $einddatum_plaatsing)
                    ->where('reason', 'offboarding_sync')
                    ->first();

                // If an offboarding exists, skip this applicant
                if ($checkOffboarding) {
                    $this->info('Offboarding already exists for applicant ID: ' . $applicant_id . ', skipping...');
                    continue;
                }

                $offboarding = new EmployeeOffboarding();
                $offboarding->applicant_id = $applicant_id;
                $offboarding->reason = 'offboarding_sync';
                $offboarding->offboarding_date = $einddatum_plaatsing;
                $offboarding->plaatsing_ids = $plaatsing_ids;
                $offboarding->processed_reserveringen = true;
                $offboarding->processed_in_uwv = true;
                $offboarding->signalering = true;
                $offboarding->is_completed = true;
                $offboarding->contract_id = 0;
                $offboarding->user_id = 1;
                $offboarding->save();
                $this->info('Offboarding created for applicant ID: ' . $applicant_id);
            }
            fclose($handle);
            $this->info('CSV processing complete.');
        }else{
            $this->error('Failed to open CSV file.');
        }
    }
}
