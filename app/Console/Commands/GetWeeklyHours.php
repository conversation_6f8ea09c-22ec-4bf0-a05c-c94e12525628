<?php

namespace App\Console\Commands;

use App\Models\JetApplicant;
use App\Models\WeeklyHours;
use App\Services\EasyflexService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use SoapClient;

class GetWeeklyHours extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:get-weekly-hours {weeknumber?} {year?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Cron for weekly hours from easyflex to our database.';
    private SoapClient $client;

    public function handle(EasyflexService $easyflex)
    {
        $this->client = $easyflex->connectDSEasyflex();

        $weeknr = $this->argument('weeknumber');
        $year = $this->argument('year');

        if ($weeknr === null) {
            $previousWeek = Carbon::now()->subWeek();
            $weeknr = $previousWeek->weekOfYear;
            $year = $previousWeek->year;
        } elseif ($year === null) {
            $year = Carbon::now()->year;
        }

        $this->info("Processing for week: $weeknr, year: $year");
        $this->processApplicantsInChunks($weeknr, $year);

        return 0;
    }
    private function processApplicantsInChunks($weeknr, $year): void
    {
//        $query = JetApplicant::whereHas('plaatsing')
//            ->whereDoesntHave('employeeOffboarding')
//            ->where(function ($q) {
//                $q->whereDoesntHave('employeeOffboarding')
//                    ->orWhereHas('employeeOffboarding', function ($subQuery) {
//                        $subQuery->where('offboarding_date', '>', now());
//                    });
//            })
//            ->whereDoesntHave('weeklyHours', function ($query) use ($weeknr) {
//                $query->where('week_number', $weeknr);
//            });


        $lastProgressTime = Carbon::now();

//        $query = JetApplicant::whereHas('plaatsing')
//            ->whereNotNull('easyflex_id')
//            ->whereNotNull('scoober_id')
//            ->where(function ($q) {
//                $q->whereDoesntHave('employeeOffboarding')
//                    ->orWhereHas('employeeOffboarding', function ($subQuery) {
//                        $subQuery->where('offboarding_date', '>', now());
//                    });
//            })->whereDoesntHave('weeklyHours', function ($query) use ($weeknr) {
//                $query->where('week_number', $weeknr);
//            });

        $offboardingDate = Carbon::now()->subWeek();
        if ($weeknr != null) {
            $offboardingDate = Carbon::now()
                ->setISODate(Carbon::now()->year, $weeknr)
                ->startOfWeek();
        }

        $query = JetApplicant::select('applicant_id', 'easyflex_id')
            ->whereHas('plaatsing')
            ->whereNotNull('easyflex_id')
            ->whereNotNull('scoober_id')
            ->where(function ($q) use ($offboardingDate) {
                $q->whereDoesntHave('employeeOffboarding')
                    ->orWhereHas('employeeOffboarding', function ($subQuery) use ($offboardingDate) {
                        $subQuery->where('offboarding_date', '>=', $offboardingDate);
                    });
            })
            ->whereDoesntHave('weeklyHours', fn($query) =>
            $query->where('week_number', $weeknr)
            );

        $totalApplicants = $query->count();
        $progressBar = $this->output->createProgressBar($totalApplicants);
        $progressBar->start();

        do {
            $processed = false;

            $query->orderBy('applicant_id')
                ->chunkById(500, function ($applicants) use ($weeknr, $year, &$lastProgressTime, &$processed, $progressBar) {
                    if ($applicants->isEmpty()) {
                        return;
                    }

                    $this->processApplicants($applicants, $weeknr, $year, $progressBar);
                    $lastProgressTime = Carbon::now();
                    $processed = true;
                });

            $remainingApplicants = $query->count();

            if ($remainingApplicants === 0) {
                break;
            }

            if (!$processed && Carbon::now()->diffInSeconds($lastProgressTime) > 10) {
                $this->warn("\nNo activity for 1 minute. Stopping command...");
                break;
            }

        } while ($remainingApplicants > 0);

        $progressBar->finish();
        $this->info("\nProcessing complete.");
    }

    private function processApplicants($applicants, $weeknr, $year, $progressBar): void
    {
        foreach ($applicants as $applicant) {
            $this->processApplicant($applicant, $weeknr, $year);
            $progressBar->advance();
        }
    }

    private function processApplicant($applicant, $weeknr, $year): void
    {
        try {
            $hours = $this->getWeeklyHours($applicant->easyflex_id, $year, $weeknr);
            $this->saveWeeklyHours($applicant->applicant_id, $weeknr, $year, $hours);
        } catch (\Exception $e) {
            Log::error("Error processing applicant {$applicant->easyflex_id}", [
                'exception' => $e
            ]);
        }
    }

    public function getWeeklyHours($registratienummer, $year, $weeknumber): array
    {
        $license = config('services.easyflex.license');

        $params = array(
            'license' => $license,
            'parameters' => ["fwregistratienummer" => $registratienummer, "jaar" => $year, "week" => $weeknumber],
            'fields' => []
        );

        $items = $this->client->ds_rl_fw_uren($params)->fields;

        $worked_minutes = 0;
        $ort_minutes = 0;
        $sickness_minutes = 0;
        $vacation_minutes = 0;
        $short_leave_minutes = 0;
        $unpaid_leave_minutes = 0;

        if(isset($items->item)){
            $itemsArray = is_array($items->item) ? $items->item : [$items->item];
            foreach ($itemsArray as $item) {
                switch ($item->rl_fw_uren_looncomponenttype) {
                    case 20810: // Loon normale uren
                    case 20811: // Loon overwerkuren
                    case 20814: // Loon verhoogde uren
                        $worked_minutes += $item->rl_fw_uren_fwminuten;
                        break;
                    case 20812: // ORT
                        $ort_minutes += $item->rl_fw_uren_fwminuten;
                        break;
                    case 20813: // Onbetaald verlof if rl_fw_uren_fwpercentage = 0
                        if ($item->rl_fw_uren_fwpercentage == 0) {
                            $unpaid_leave_minutes += $item->rl_fw_uren_fwminuten;
                        }
                        break;
                    case 20818: // Ziekte
                        $sickness_minutes += $item->rl_fw_uren_fwminuten;
                        break;
                    case 20820: // Kort verzuim
                        $short_leave_minutes += $item->rl_fw_uren_fwminuten;
                        break;
                    case 20822: // Vakantie
                        $vacation_minutes += $item->rl_fw_uren_fwminuten;
                        break;

                }
            }
        }

        return ['worked_minutes' => $worked_minutes, 'ort_minutes' => $ort_minutes, 'sickness_minutes' => $sickness_minutes, 'vacation_minutes' => $vacation_minutes, 'short_leave_minutes' => $short_leave_minutes, 'unpaid_leave_minutes' => $unpaid_leave_minutes];
    }

    private function saveWeeklyHours($applicantId, $weeknr, $year, $hours): void
    {
        WeeklyHours::create([
            'applicant_id' => $applicantId,
            'week_number' => $weeknr,
            'year' => $year,
            ...$hours
        ]);
    }
}
