<?php

namespace App\Console\Commands;

use App\Actions\JetActions;

use Illuminate\Console\Command;

class SyncNewApplicants extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:new-applicants';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Syncs new applicants from Salesforce (Jet).';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        JetActions::syncNewApplicants();
        JetActions::onboardNewApplicants();
    }
}
