<?php

namespace App\Console\Commands;

use App\Models\EmployeeOffboarding;
use App\Models\JetApplicant;
use App\Services\EasyflexService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateFlexwerkerStatusEasyflex extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-flexwerker-status-easyflex';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public function handle(EasyflexService $easyflexService)
    {
        $employeeOffboardings = EmployeeOffboarding::where('offboarding_date', Carbon::today())->get();
        foreach($employeeOffboardings as $employee)
        {
            $applicant = $employee->applicant;
            $params = [];
            $params['WKM_id'] = 7;
            $params['registratienummer'] = $applicant->easyflex_id;
            $params['params']['flexwerkerstatus'] = 21358 /* Uitgeschreven status EF */;
            $easyflexService->updateEasyflexUser($params);
        }
    }
}
