<?php

namespace App\Console\Commands;

use App\Models\ContractRenewal;
use App\Models\JetApplicantRenewalStatus;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CheckRenewalStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:check-renewal-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        JetApplicantRenewalStatus::where('step_in_process', 9)->update(['step_in_process' => 0, 'notes' => 'Verlening teruggedraaid op '. Carbon::today()->format('d-m-Y')]);
    }
}
