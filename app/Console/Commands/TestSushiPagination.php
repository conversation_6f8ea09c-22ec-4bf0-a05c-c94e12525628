<?php

namespace App\Console\Commands;

use App\Models\Filament\SignhostExpiredDocument;
use Illuminate\Console\Command;
use Illuminate\Pagination\Paginator;

class TestSushiPagination extends Command
{
    protected $signature = 'test:sushi-pagination';
    protected $description = 'Test Sushi model pagination functionality';

    public function handle()
    {
        $this->info('Testing Sushi Model Pagination');
        $this->info('==============================');
        $this->newLine();

        // First test if we can get any data at all using getRows()
        $this->info('Testing basic data retrieval:');
        try {
            $model = new SignhostExpiredDocument();
            $rows = $model->getRows();
            $this->info("Raw rows count: " . count($rows));
            if (count($rows) > 0) {
                $this->info("First row keys: " . implode(', ', array_keys($rows[0])));
            }
        } catch (\Exception $e) {
            $this->error("Error getting rows: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
        }

        $this->newLine();

        // Test page 1
        $this->info('Testing Page 1:');
        Paginator::currentPageResolver(function () {
            return 1;
        });

        try {
            $page1Results = SignhostExpiredDocument::paginate(5);
            $this->info("Page 1 - Total items: " . $page1Results->total());
            $this->info("Page 1 - Current page: " . $page1Results->currentPage());
            $this->info("Page 1 - Items count: " . $page1Results->count());
            $this->info("Page 1 - First item ID: " . ($page1Results->first()->id ?? 'N/A'));
        } catch (\Exception $e) {
            $this->error("Error on page 1: " . $e->getMessage());
            $this->error("Stack trace: " . $e->getTraceAsString());
            return 1;
        }

        $this->newLine();

        // Test page 2
        $this->info('Testing Page 2:');
        Paginator::currentPageResolver(function () {
            return 2;
        });

        try {
            $page2Results = SignhostExpiredDocument::paginate(5);
            $this->info("Page 2 - Total items: " . $page2Results->total());
            $this->info("Page 2 - Current page: " . $page2Results->currentPage());
            $this->info("Page 2 - Items count: " . $page2Results->count());
            $this->info("Page 2 - First item ID: " . ($page2Results->first()->id ?? 'N/A'));
        } catch (\Exception $e) {
            $this->error("Error on page 2: " . $e->getMessage());
            return 1;
        }

        $this->newLine();

        // Compare results
        if (isset($page1Results) && isset($page2Results) && $page1Results->first() && $page2Results->first()) {
            $sameData = $page1Results->first()->id === $page2Results->first()->id;
            if ($sameData) {
                $this->error("PROBLEM: Same data returned for both pages!");
            } else {
                $this->info("SUCCESS: Different data returned for each page.");
            }
        } else {
            $this->warn("One or both pages returned no data");
        }

        $this->newLine();
        $this->info('Test completed.');

        return 0;
    }
}
