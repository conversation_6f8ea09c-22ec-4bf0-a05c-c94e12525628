<?php

namespace App\Console\Commands;

use App\Actions\JetActions;
use App\Services\JetService;
use Illuminate\Console\Command;

class UpdatePreContractData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-pre-contract-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
//        $applicants = [2155, 2181, 2225, 278, 2250, 2244, 2238, 2257, 2156, 2266];
        $applicants = [1986, 2154, 2231, 2248];

        foreach($applicants as $applicantId){
            $jetAction = new JetActions();
            $jetAction->approveApplicant($applicantId);
        }
    }
}
