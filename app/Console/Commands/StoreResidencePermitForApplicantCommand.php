<?php

namespace App\Console\Commands;

use App\Actions\SaveResidencePermit;
use App\Models\JetApplicant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Ramsey\Uuid\Uuid;
use Throwable;

use function Laravel\Prompts\spin;

class StoreResidencePermitForApplicantCommand extends Command
{
    protected $signature = 'app:save-applicant-residence-permit {--flexapp_id= : Applicant Flexapp ID}';

    protected $description = 'Save applicant residence permit to SalesForce';

    public function handle(): void
    {
        try {
            $uuid = Uuid::fromString($this->option('flexapp_id'));
        } catch (Throwable) {
            $this->warn('Not a valid Applicant Flexapp ID');

            return;
        }

        if (! $applicant = JetApplicant::query()->firstWhere('flexapp_id', $uuid)) {
            $this->warn('Applicant not found');

            return;
        }

        try {
            $permit = spin(
                fn () => SaveResidencePermit::forApplicant($applicant),
                message: 'Processing applicant ' . $applicant->email . '...'
            );
        } catch (Throwable $e) {
            Log::error('error with processing the residence permit for applicant', [
                'flexapp_id' => $applicant->flexapp_id,
                'message' => $e->getMessage(),
            ]);

            return;
        }

        if (! $permit) {
            $this->error('Failed to save residence permit');

            return;
        }

        $headers = [
            'Name',
            'Lead__c',
            'Type__c',
            'filePath',
            'documentNumber',
            'documentExpiresAt',
            'courierDocumentId',
            'contentVersionId',
            'contentDocumentId',
            'contentDocumentLinkId',
        ];
        $this->table($headers, [$permit->except('fileData')->toArray()]);
    }
}
