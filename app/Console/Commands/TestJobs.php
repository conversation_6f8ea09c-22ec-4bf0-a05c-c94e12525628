<?php

namespace App\Console\Commands;

use AllowDynamicProperties;
use App\Http\Controllers\ExportController;

use App\Http\Controllers\PlaatsingController;
use Illuminate\Console\Command;

#[AllowDynamicProperties] class TestJobs extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-jobs {importId}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create plaatsingen';

    /**
     * Execute the console command.
     */
    public function handle(PlaatsingController $plaatsing)
    {
        $import_id = $this->argument('importId');
        $plaatsing->storePlaatsing($import_id);
    }
}
