<?php

namespace App\Console\Commands;

use App\Livewire\JetApplicantsContractStatuses;
use App\Models\ContractRenewal;
use App\Models\JetApplicantRenewalStatus;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;

class UpdateContractRenewalStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:update-contract-renewal-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update contract renewal status from Signhost';

    public function handle()
    {
        $renewalStatusses = JetApplicantRenewalStatus::where('step_in_process', 6)->get();
        $bar = $this->output->createProgressBar(count($renewalStatusses));
        $bar->start();
        foreach($renewalStatusses as $renewalStatus){
            $applicant = $renewalStatus->applicant;

            $response = Http::withHeaders([
                'Accept' => 'application/vnd.api+json',
                'Content-Type' => 'application/vnd.api+json',
            ])->get(config('services.signhost_service.url') . '/get_local_transaction/' . $applicant->flexapp_id)->json();

            $status = $response['data'][0]['attributes']['status_code'] ?? null;

            $this->newLine();
            $this->info('Processing applicant '. $applicant->applicant_id. ' with status '. $status);

            if($status && $status != 10){
                $contractStatus = match (true) {
                    $status == 30 => 7,          // signed
                    $status == 40 => 8,          // declined
                    $status > 40 => 9,           // not signed
                    default => 6,                // waiting for signer (status 10)
                };

                if($contractStatus != 6){
                    $renewalStatus->step_in_process = $contractStatus;
                    $renewalStatus->{'step_' . $contractStatus . '_started_at'} = now()->toDateTimeString();
                    $renewalStatus->{'step_' . $contractStatus . '_finished_at'} = now()->toDateTimeString();
                    $renewalStatus->save();
                    $this->newLine();
                    $this->info('Updated contract renewal '. $applicant->applicant_id. ' with contract status '. $contractStatus);
                }
            }

            $bar->advance();
        }

        $bar->finish();
    }
}
