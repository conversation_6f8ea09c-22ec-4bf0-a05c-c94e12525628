<?php

namespace App\Console\Commands;

use App\Actions\JetActions;
use App\Models\JetApplicant;
use App\Models\WenoteUser;
use App\Services\UserService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class OnHoldApplicant extends Command
{
    private $jet;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:on-hold-applicant {id : the id of applicant}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $id = $this->argument('id');

        $jetapplicant = JetApplicant::where('applicant_id', '=', $id)->first();
        $wenoteApplicant = WenoteUser::where('emailadres', $jetapplicant->email)->first();

        //update wenote user
        $wenoteApplicant->is_justeat = 1;
        $wenoteApplicant->save();

        $jetapplicant->wenote_id = $wenoteApplicant->UKT_id;
        $jetapplicant->flexapp_id = $wenoteApplicant->flex_id;
        $jetapplicant->save();

        //update userservice info table
        $userService = new UserService;
        $userService->updateUser($wenoteApplicant->flex_id, [
            'info' => [
                'start_date' => $jetapplicant->pre_contract_start_date,
                'minimum_contract_hours' => $jetapplicant->pre_contract_minimum_hours,
                'driving_license_required' => JetActions::isDrivingLicenseRequired($jetapplicant->active_transportation_type),
                'whatsapp_consent' => $jetapplicant->whatsapp_consent,
                'final_information_check' => null,
                'origin' => 'jet',
            ]
        ]);

        JetActions::timeApplicantEnrollmentStep($id, 3);
        JetActions::releaseApplicant($id);
    }
}
