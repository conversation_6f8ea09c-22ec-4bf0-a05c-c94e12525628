<?php

namespace App\Console\Commands;

use App\Models\JetApplicant;
use App\Models\Flexapp\UserInfo;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class sync_scoober_ids extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-scoober-ids {--dry-run : Run without making changes} {--force : Skip confirmation}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync scoober_id from JetApplicants to FlexappData info table for all users';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        $isForced = $this->option('force');

        $this->info('🚀 Starting Scoober ID sync process...');

        if ($isDryRun) {
            $this->warn('🔍 DRY RUN MODE - No changes will be made');
        }

        // Check if scoober_id column exists in info table
        if (!$this->checkScooberIdColumnExists()) {
            $this->error('❌ scoober_id column does not exist in the info table.');
            $this->info('💡 Please run the migration to add the scoober_id column first.');
            return Command::FAILURE;
        }

        // Get all JetApplicants with scoober_id and flexapp_id
        $applicants = $this->getApplicantsWithScooberIds();

        if ($applicants->isEmpty()) {
            $this->info('ℹ️  No applicants found with both scoober_id and flexapp_id');
            return Command::SUCCESS;
        }

        $this->info("📊 Found {$applicants->count()} applicants with scoober_id to sync");

        // Show preview of changes
        $this->showPreview($applicants->take(5));

        if (!$isDryRun && !$isForced) {
            if (!$this->confirm('Do you want to proceed with syncing all scoober_ids?')) {
                $this->info('❌ Operation cancelled');
                return Command::SUCCESS;
            }
        }

        // Perform the sync
        $result = $this->syncScooberIds($applicants, $isDryRun);

        // Display results
        $this->displayResults($result, $isDryRun);

        return Command::SUCCESS;
    }

    /**
     * Check if scoober_id column exists in the info table.
     */
    private function checkScooberIdColumnExists(): bool
    {
        try {
            return DB::connection('userservice')
                ->getSchemaBuilder()
                ->hasColumn('info', 'scoober_id');
        } catch (\Exception $e) {
            Log::error('Error checking scoober_id column existence', [
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Get all JetApplicants that have both scoober_id and flexapp_id.
     */
    private function getApplicantsWithScooberIds()
    {
        return JetApplicant::whereNotNull('scoober_id')
            ->whereNotNull('flexapp_id')
            ->where('scoober_id', '!=', '')
            ->where('flexapp_id', '!=', '')
            ->with(['flexappData.info'])
            ->get();
    }

    /**
     * Show a preview of the changes that will be made.
     */
    private function showPreview($previewApplicants): void
    {
        $this->info("\n📋 Preview of changes (showing first 5):");

        $headers = ['Applicant ID', 'Flexapp ID', 'Scoober ID', 'Current Info Scoober ID', 'Action'];
        $rows = [];

        foreach ($previewApplicants as $applicant) {
            $currentScooberIdInInfo = $applicant->flexappData?->info?->scoober_id ?? 'NULL';
            $action = $currentScooberIdInInfo === $applicant->scoober_id ? 'No change' : 'Update';

            $rows[] = [
                $applicant->applicant_id,
                $applicant->flexapp_id,
                $applicant->scoober_id,
                $currentScooberIdInInfo,
                $action
            ];
        }

        $this->table($headers, $rows);
    }

    /**
     * Sync scoober_ids to the info table.
     */
    private function syncScooberIds($applicants, bool $isDryRun): array
    {
        $stats = [
            'total' => $applicants->count(),
            'updated' => 0,
            'skipped' => 0,
            'errors' => 0,
            'created_info_records' => 0
        ];

        $progressBar = $this->output->createProgressBar($stats['total']);
        $progressBar->start();

        foreach ($applicants as $applicant) {
            try {
                $result = $this->syncSingleApplicant($applicant, $isDryRun);
                $stats[$result]++;
            } catch (\Exception $e) {
                $stats['errors']++;
                Log::error('Error syncing scoober_id for applicant', [
                    'applicant_id' => $applicant->applicant_id,
                    'flexapp_id' => $applicant->flexapp_id,
                    'scoober_id' => $applicant->scoober_id,
                    'error' => $e->getMessage()
                ]);
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        return $stats;
    }

    /**
     * Sync scoober_id for a single applicant.
     */
    private function syncSingleApplicant(JetApplicant $applicant, bool $isDryRun): string
    {
        // Check if info record exists
        $infoRecord = UserInfo::where('uuid', $applicant->flexapp_id)->first();

        if (!$infoRecord) {
            if (!$isDryRun) {
                // Create new info record
                UserInfo::create([
                    'uuid' => $applicant->flexapp_id,
                    'scoober_id' => $applicant->scoober_id
                ]);
            }
            return 'created_info_records';
        }

        // Check if update is needed
        if ($infoRecord->scoober_id === $applicant->scoober_id) {
            return 'skipped';
        }

        // Update the scoober_id
        if (!$isDryRun) {
            $infoRecord->update(['scoober_id' => $applicant->scoober_id]);
        }

        return 'updated';
    }

    /**
     * Display the results of the sync operation.
     */
    private function displayResults(array $stats, bool $isDryRun): void
    {
        $this->newLine();
        $this->info('📈 Sync Results:');
        $this->info("   Total processed: {$stats['total']}");
        $this->info("   Updated: {$stats['updated']}");
        $this->info("   Created info records: {$stats['created_info_records']}");
        $this->info("   Skipped (no change): {$stats['skipped']}");

        if ($stats['errors'] > 0) {
            $this->error("   Errors: {$stats['errors']}");
            $this->warn('   Check the logs for error details');
        }

        if ($isDryRun) {
            $this->warn('🔍 This was a dry run - no actual changes were made');
            $this->info('💡 Run without --dry-run to apply changes');
        } else {
            $this->info('✅ Sync completed successfully!');
        }
    }
}
