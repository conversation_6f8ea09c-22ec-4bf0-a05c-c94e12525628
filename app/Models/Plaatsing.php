<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Plaatsing extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'plaatsingen';
    protected $fillable = ['applicant_id', 'plaatsing_id', 'kpcode', 'offboard_date'];

    public function applicant(): BelongsTo
    {
        return $this->belongsTo(JetApplicant::class, 'applicant_id');
    }
}
