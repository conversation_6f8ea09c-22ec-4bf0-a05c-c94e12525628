<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class TwvInformation extends Model
{
    protected $connection = 'pgsql';

    protected $table = 'twv_information';

    protected $fillable = [
        'applicant_id',
        'twv_required',
        'twv_requested_date',
        'twv_approved_date',
        'twv_start_date',
        'twv_expiration_date',
    ];

    protected $casts = [
        'twv_required' => 'boolean',
        'twv_requested_date' => 'datetime',
        'twv_approved_date' => 'datetime',
        'twv_start_date' => 'date',
        'twv_expiration_date' => 'date',
    ];
    public function jetApplicant()
    {
        return $this->belongsTo(JetApplicant::class, 'applicant_id');
    }

    public function residencePermitRenewal()
    {
        return $this->hasOne(ResidencePermitRenewal::class, 'twv_information_id');
    }
}
