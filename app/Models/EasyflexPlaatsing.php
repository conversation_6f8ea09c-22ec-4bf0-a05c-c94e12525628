<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EasyflexPlaatsing extends Model
{
    protected $connection = 'easyflex';

    protected $table = 'ds_rl_plaatsingen';

    protected $primaryKey = 'id';

    public $keyType = 'integer';

    public function JetApplicant()
    {
        return $this->belongsTo(JetApplicant::class, 'easyflex_registratienummer', 'easyflex_id');
    }
    public function Easyflexuser(){
        return $this->belongsTo(EasyflexUser::class, 'easyflex_registratienummer', 'easyflex_registratienummer');
    }
}
