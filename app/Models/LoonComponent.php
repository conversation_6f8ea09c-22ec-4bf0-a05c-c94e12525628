<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LoonComponent extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'description', 'value', 'unit','loon_component_type_id','status'];


    public function Looncomponenttype(){
        return $this->belongsTo(LoonComponentType::class,'loon_component_type_id','id');
    }

    public function urenbestand(){
        return $this->hasMany(Urenbestand::class,'loon_component_id','id');
    }
}
