<?php

namespace App\Models;

use Carbon\Carbon;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;

class Urenbestand extends Model
{
    use HasFactory;

    protected $table = 'urenbestand';
    protected $fillable = ['driver_id', 'date', 'amount', 'hours', 'city', 'weeknr', 'loon_component_id', 'import_id', 'reason', 'to', 'from', 'sub_reason', 'absence'];
    protected $casts = ['to' => 'datetime', 'from' => 'datetime'];
    protected $guarded = ['to', 'from'];

    public function loonComponent()
    {
        return $this->belongsTo(LoonComponent::class);
    }

    public function import()
    {
        return $this->belongsTo(Import::class);
    }

    public function export()
    {
        return $this->belongsTo(Export::class);
    }

    public function applicant(): BelongsTo
    {
        return $this->belongsTo(JetApplicant::class, 'driver_id', 'scoober_id');
    }

    public function setAmountAttribute($value)
    {
        if(is_null($value)){
            $this->attributes['amount'] = null;
        }
        $formattedValue = str_replace(',', '.', $value);
        $decimalValue = (float)$formattedValue;
        $this->attributes['amount'] = $decimalValue;
    }

    public function getAmountAttribute($value)
    {
        $formattedValue = number_format($value, 2, ',', '.');
        return '€' . $formattedValue;
    }
}
