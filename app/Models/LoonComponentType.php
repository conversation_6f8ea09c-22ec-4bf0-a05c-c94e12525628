<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LoonComponentType extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'description', 'default_value', 'default_unit'];

    public function Looncomponent(){
        return $this->hasMany(LoonComponent::class,'loon_component_type_id','id');
    }

    public function Looncomponentbatch(){
        return $this->hasMany(LoonComponentBatch::class,'loon_component_id','id');
    }
}
