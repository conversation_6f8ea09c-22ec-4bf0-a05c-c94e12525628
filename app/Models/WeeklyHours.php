<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WeeklyHours extends Model
{
    use HasFactory;

    protected $fillable = [
        'applicant_id',
        'week_number',
        'year',
        'worked_minutes',
        'ort_minutes',
        'sickness_minutes',
        'vacation_minutes',
        'unpaid_leave_minutes',
        'short_leave_minutes',
    ];

    public function applicant()
    {
        return $this->belongsTo(JetApplicant::class, 'applicant_id');
    }

    public static function calculateAveragePast13Weeks($applicant_id)
    {
        $startDate = now()->subWeeks(13)->startOfWeek();

        $average = WeeklyHours::where('applicant_id', $applicant_id)
            ->where('week_number', '>=', $startDate->isoWeek())
            ->groupBy('applicant_id')
            ->selectRaw('SUM(worked_hours) / 13 AS average_worked_hours')
            ->first();

        return $average->average_worked_hours ?? 0;
    }
}
