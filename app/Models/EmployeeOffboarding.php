<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmployeeOffboarding extends Model
{
    use HasFactory;

    protected $table = 'employee_offboarding';
    protected $fillable = ['applicant_id', 'plaatsing_ids', 'reason', 'offboarding_date', 'contract_id', 'is_completed', 'processed_in_uwv', 'processed_reserveringen', 'signalering', 'user_id'];

    protected $casts = [
        'plaatsing_ids' => 'array',  // Automatically converts JSON to array
    ];
    public function applicant()
    {
        return $this->belongsTo(JetApplicant::class, 'applicant_id');
    }

}
