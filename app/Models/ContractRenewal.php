<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContractRenewal extends Model
{
    use HasFactory;

    protected $table = 'contract_renewals';

    protected $fillable = [
        'applicant_id',
        'wage_per_hour',
        'contract_hours',
        'is_wage_with_vacation_allowance',
        'is_wage_with_vacation_days',
        'delivery_area',
        'contract_start_date',
        'contract_end_date',
    ];

    public function applicant()
    {
        return $this->belongsTo(JetApplicant::class, 'applicant_id');
    }
}
