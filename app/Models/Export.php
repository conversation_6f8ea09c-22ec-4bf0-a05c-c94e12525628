<?php

namespace App\Models;

use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Exception;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Export extends Model
{
    use HasFactory;
    protected $table = 'exports';

    protected $casts = [
        'import_id' => 'array',
    ];
    protected $fillable = ['import_id', 'file_name', 'status', 'file_nr', 'loon_component_type_id', 'user_id'];

    public function user(): BelongsTo
    {
        if (static::hasPolymorphicUserRelationship()) {
            return $this->morphTo();
        }

        $authenticatable = app(Authenticatable::class);

        if ($authenticatable) {
            return $this->belongsTo($authenticatable::class);
        }

        if (! class_exists(User::class)) {
            throw new Exception('No [App\\Models\\User] model found. Please bind an authenticatable model to the [Illuminate\\Contracts\\Auth\\Authenticatable] interface in a service provider\'s [register()] method.');
        }

        return $this->belongsTo(User::class);
    }
    public function urenbestanden(){
        if (is_null($this->import_id)) {
            return $this->hasMany(Urenbestand::class, 'import_id', 'id')->whereRaw('1=0');
        }

//        if (is_array($this->import_id)) {
//            return Urenbestand::whereIn('import_id', $this->import_id)->get();
//        }

        return $this->hasManyThrough(Urenbestand::class, ImportExtended::class, 'id', 'import_id', 'import_id', 'id');
    }

    public function import()
    {
        return $this->belongsTo(ImportExtended::class, 'import_id', 'id');
    }
}
