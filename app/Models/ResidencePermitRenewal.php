<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ResidencePermitRenewal extends Model
{
    protected $connection = 'pgsql';

    protected $table = 'residence_permit_renewals';

    protected $primaryKey = 'id';

    protected $fillable = [
        'applicant_id',
        'datachecker_transaction_id',
        'twv_information_id',
        'notes',
        'reminder_dates',
        'renewal_completed'
    ];

    protected $casts = [
        'reminder_dates' => 'array', // Automatically decodes JSON
        'renewal_completed' => 'boolean',
    ];

    public function jetApplicant()
    {
        return $this->belongsTo(JetApplicant::class, 'applicant_id');
    }

    public function datacheckerTransaction()
    {
        return $this->belongsTo(DatacheckerTransaction::class, 'datachecker_transaction_id', 'id');
    }

    public function twvInformation()
    {
        return $this->belongsTo(TwvInformation::class, 'twv_information_id', 'id');
    }
}
