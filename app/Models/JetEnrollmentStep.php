<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class JetEnrollmentStep extends Model
{
    /**
     * The database connection that should be used by the model.
     * @var string
     */
    protected $connection = 'pgsql';

    /**
     * The table associated with the model.
     * @var string
     */
    protected $table = 'enrollment_steps_new';

    /**
     * The primary key associated with the table.
     * @var string
     */
    protected $primaryKey = 'step_id';

    /**
     * The data type of the auto-incrementing ID.
     * @var string
     */
    protected $keyType = 'int';

    /**
     * The attributes that aren't mass assignable.
     * @var array
     */
    protected $guarded = [];
}
