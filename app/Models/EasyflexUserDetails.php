<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EasyflexUserDetails extends Model
{
    use HasFactory;
    protected $connection = 'easyflex';

    protected $table = 'ds_fw_persoonsgegevens_all';

    protected $primaryKey = 'id';

    public $keyType = 'string';

    public function Easyflexuser(){
        return $this->belongsTo(EasyflexUser::class,'easyflex_registratienummer','easyflex_registratienummer');
    }
}
