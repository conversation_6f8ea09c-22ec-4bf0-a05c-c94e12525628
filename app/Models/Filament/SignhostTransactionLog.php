<?php

namespace App\Models\Filament;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Sushi\Sushi;

class SignhostTransactionLog extends Model implements RemoteModel
{
    use Sushi, SushiBuilder;

    protected $fillable = [
        'id',
        'transaction_id',
        'transaction_uuid',
        'action',
        'form_data',
        'is_just_eat',
        'transaction',
        'receivers',
        'signers',
        'files',
        'status',
        'details',
    ];

    protected function casts(): array
    {
        return [
            'id' => 'int',
            'transaction_id' => 'int',
            'transaction_uuid' => 'string',
            'action' => 'string',
            'form_data' => 'array',
            'is_just_eat' => 'bool',
            'transaction' => 'array',
            'receivers' => 'array',
            'signers' => 'array',
            'files' => 'array',
            'status' => 'bool',
            'details' => 'array',
        ];
    }

    public function newEloquentBuilder($query): Builder
    {
        return new SushiEloquentBuilder($query);
    }

    public function getRows(): array
    {
        return [...$this->getData()];
    }

    protected function getJsonFields(): array
    {
        return [
            'form_data',
            'files',
            'signers',
            'receivers',
            'transaction',
            'details',
        ];
    }

    public function remoteUrl(): string
    {
        return 'logs/transactions';
    }
}
