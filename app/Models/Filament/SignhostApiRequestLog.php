<?php

namespace App\Models\Filament;

use App\Services\SignhostService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Sushi\Sushi;

class SignhostApiRequestLog extends Model implements RemoteModel
{
    use Sushi, SushiBuilder;

    protected $fillable = [
        'id',
        'api_endpoint',
        'request_endpoint',
        'method',
        'headers',
        'transaction_id',
        'transaction',
        'stage',
        'response_code',
        'response_error_code',
        'response_message',
        'created_at',
        'updated_at',
    ];

    protected function casts(): array
    {
        return [
            'headers' => 'array',
            'transaction' => 'array',
            'response_message' => 'array',
        ];
    }

    public function newEloquentBuilder($query): Builder
    {
        return new SushiEloquentBuilder($query);
    }

    public function getRows()
    {
        return $this->getData()
            ->map(function (array $item) {
                if (array_key_exists('headers', $item)) {
                    $item['headers'] = collect($item['headers'])
                        ->unless(is_array($item['headers']), fn ($headers) => $headers
                            ->map(fn (string $header) => explode(': ', $header))
                            ->mapWithKeys(fn ($header) => [$header[0] => $header[1]]))
                        ->toJson();
                }

                return $item;
            })
            ->toArray();
    }

    protected function getJsonFields(): array
    {
        return [
            'transaction',
            'response_message',
        ];
    }

    public function remoteUrl(): string
    {
        return 'logs/signhost_requests';
    }
}
