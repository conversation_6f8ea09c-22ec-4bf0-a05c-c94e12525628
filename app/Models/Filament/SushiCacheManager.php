<?php

namespace App\Models\Filament;

use Illuminate\Support\Facades\Cache;

trait SushiCacheManager
{
    /**
     * Clear the cache for this model's remote data
     */
    public function clearRemoteCache(): void
    {
        if (!$this instanceof RemoteModel) {
            return;
        }

        $url = $this->remoteUrl();
        
        // Clear all cached entries for this model's URL
        $cacheKeys = Cache::getRedis()->keys("*{$url}*");
        
        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Force refresh the model data by clearing cache and re-fetching
     */
    public function forceRefresh(): void
    {
        $this->clearRemoteCache();
        
        // Clear Sushi's SQLite cache if it exists
        if (method_exists($this, 'clearSushiCache')) {
            $this->clearSushiCache();
        }
    }
}
