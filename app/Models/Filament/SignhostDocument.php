<?php

namespace App\Models\Filament;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Sushi\Sushi;

class SignhostDocument extends Model
{
    use Sushi, SushiBuilder;

    public $incrementing = false;

    protected $keyType = 'string';

    protected $fillable = [
        'id',
        'transaction_id',
        'user_id',
        'file_name',
        'file_extension',
        'status',
        'media_id',
        'transaction',
        'created_at',
        'updated_at',
        'deleted_at',
];

    protected function casts(): array
    {
        return [
            'id' => 'int',
            'transaction_id' => 'int',
            'transaction_uuid' => 'string',
            'file_name' => 'string',
            'file_path' => 'string',
            'file_size' => 'int',
            'file_type' => 'string',
            'file_content' => 'string',
            'transaction' => 'array',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
        ];
    }

    public function getKeyName(): string
    {
        return 'transaction_id';
    }

    public function newEloquentBuilder($query): Builder
    {
        return new SushiEloquentBuilder($query);
    }

    public function getRows(): array
    {
        return $this->getData()->toArray();
    }

    protected function sushiShouldCache(): bool
    {
        return true;
    }

    protected function getJsonFields(): array
    {
        return [
            'transaction',
        ];
    }
}
