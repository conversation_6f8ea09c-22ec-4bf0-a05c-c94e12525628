<?php

namespace App\Models\Filament;

use App\Services\SignhostService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

trait SushiBuilder
{
    abstract public function newEloquentBuilder($query): Builder;

    protected function getData(?array $query = [], ?array $fields = null): Collection
    {
        // Default pagination parameters - these will be overridden by the custom builder
        $defaultQuery = [
            'per_page' => 15,
            'page' => 1, // Default to page 1, will be overridden by SushiEloquentBuilder
        ];

        $logs = (new SignhostService())->modelRequest(
            remoteModel: $this,
            query: array_merge($defaultQuery, $query),
            fields: $fields,
        );

        return $logs
            ->when(fn (Collection $item) => $item->keys()->contains('data'), fn (Collection $item) => collect($item->get('data')))
            ->map(function ($item, $key) {
                if (is_string($key)) {
                    return in_array($key, $this->getJsonFields(), true) ? json_encode($item) : $item;
                }

                foreach (array_keys($item) as $itemKey) {
                    if (in_array($itemKey, $this->getJsonFields(), true)) {
                        if ($itemKey === 'postback') {
                            $item[$itemKey] = Str::of($item[$itemKey]['postback'])
                                ->replace("\r\n", '')
                                ->replace(" ", '')
                                ->toString();
                        }

                        $item[$itemKey] = is_array($item[$itemKey]) ? json_encode($item[$itemKey]) : $item[$itemKey];
                    }
                }

                return $item;
            });
    }

    abstract protected function getJsonFields(): array;
}
