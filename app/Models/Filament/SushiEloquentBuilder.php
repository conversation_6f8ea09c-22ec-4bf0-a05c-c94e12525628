<?php

namespace App\Models\Filament;

use App\Services\SignhostService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;

class SushiEloquentBuilder extends Builder
{
    public function __construct(
        /** @var QueryBuilder */
        protected $query,
    ) {
        parent::__construct($query);
    }

    public function paginate($perPage = null, $columns = ['*'], $pageName = 'page', $page = null, $total = null): LengthAwarePaginator
    {
        $page = $page ?: Paginator::resolveCurrentPage($pageName);
        $perPage = $perPage ?: $this->model->getPerPage();
        $service = new SignhostService();

        // Build query parameters including filters and constraints
        $queryParams = [
            'per_page' => $perPage,
            'page' => $page,
        ];

        // Add any where clauses from the query builder
        if (!empty($this->getQuery()->wheres)) {
            $queryParams['wheres'] = $this->getQuery()->wheres;
        }

        $response = $service->modelRequest(
            remoteModel: new $this->model(),
            query: $queryParams
        );

        // Handle response structure - check if data is nested or direct
        $data = $response->has('data') ? $response->get('data') : $response->toArray();
        $newTotal = $response->has('total') ? $response->get('total') :
                ($response->has('meta.total') ? $response->get('meta.total') : count($data));

        $items = $this->model->newCollection(collect($data)->mapInto($this->model::class)->all());

        return new LengthAwarePaginator($items, $newTotal, $perPage, $page, [
            'path' => Paginator::resolveCurrentPath(),
            'pageName' => $pageName,
        ]);
    }
}
