<?php

namespace App\Models\Filament;

use App\Services\SignhostService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;

class SushiEloquentBuilder extends Builder
{
    public function __construct(
        /** @var QueryBuilder */
        protected $query,
    ) {
        parent::__construct($query);
    }

    public function paginate($perPage = null, $columns = ['*'], $pageName = 'page', $page = null, $total = null): LengthAwarePaginator
    {
        $page = $page ?: Paginator::resolveCurrentPage($pageName);
        $perPage = $perPage ?: $this->model->getPerPage();
        $service = new SignhostService();

        $response = $service->modelRequest(
            remoteModel: new $this->model(),
            query: [
                'per_page' => $perPage,
                'page' => $page,
                'wheres' => $this->getQuery()->wheres,
            ]
        );
        dump($response->toArray());
        $items = $this->model->newCollection(collect($response->get('data'))->mapInto($this->model::class)->all());

        return new LengthAwarePaginator($items, data_get($response, 'total', 0), $perPage, $page, options: [
            'path' => Paginator::resolveCurrentPath(),
            'pageName' => $pageName,
        ]);
    }
}
