<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

use App\Models\JetApplicant;


class JetApplicantEnrollmentStatus extends Model
{
    /**
     * The database connection that should be used by the model.
     * @var string
     */
    protected $connection = 'pgsql';

    /**
     * The table associated with the model.
     * @var string
     */
    protected $table = 'applicants_enrollment_status';

    /**
     * The primary key associated with the table.
     * @var string
     */
    protected $primaryKey = 'applicant_enrollment_status_id';

    /**
     * The data type of the auto-incrementing ID.
     * @var string
     */
    protected $keyType = 'int';


    /**
     * The attributes that aren't mass assignable.
     * @var array
     */
    protected $guarded = [];

    /**
     * Get the Applicant that owns the Enrollment Status.
     */
    public function applicant(): BelongsTo
    {
        return $this->belongsTo(JetApplicant::class, 'applicant_id', 'applicant_id');
    }
}
