<?php

namespace App\Models;

use App\DTO\ResidencePermit;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $lead_id
 * @property string $entity
 * @property string $entity_id
 * @property string | null $http_method
 * @property int $http_status_code
 * @property ResidencePermit $payload
 * @property string | null $error_code
 * @property string | null $error_message
 * @property array $error_fields
 * @property string $response_entity_id
 * @property array $response_errors
 * @property bool $response_success
 *
 * @property-read JetApplicant $applicant
 */
class JetCourierDocumentRequestLog extends Model
{
    /* properties */
    public const
        TRANSACTION_ID = 'transaction_id',
        LEAD_ID = 'lead_id',
        ENTITY = 'entity',
        ENTITY_ID = 'entity_id',
        HTTP_METHOD = 'http_method',
        HTTP_STATUS_CODE = 'http_status_code',
        PAYLOAD = 'payload',
        ERROR_CODE = 'error_code',
        ERROR_MESSAGE = 'error_message',
        ERROR_FIELDS = 'error_fields';

    /* relations */
    public const APPLICANT = 'applicant';

    /** @var list<int, string> */
    protected $fillable = [
        self::TRANSACTION_ID,
        self::LEAD_ID,
        self::ENTITY,
        self::ENTITY_ID,
        self::HTTP_METHOD,
        self::HTTP_STATUS_CODE,
        self::PAYLOAD,
        self::ERROR_CODE,
        self::ERROR_MESSAGE,
        self::ERROR_FIELDS,
    ];

    /** @return list<string, string> */
    protected function casts(): array
    {
        return [
            self::HTTP_STATUS_CODE => 'integer',
            self::ERROR_MESSAGE => 'array',
            self::PAYLOAD => 'array',
        ];
    }

    public function applicant(): BelongsTo
    {
        return $this->belongsTo(JetApplicant::class, 'lead_id', self::LEAD_ID);
    }
}
