<?php

namespace App\Models;

use Filament\Actions\Imports\Models\Import;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ImportExtended extends Import
{
    use HasFactory;
    protected $table = 'imports';

    public function urenbestand(){
        return $this->hasMany(Urenbestand::class,'import_id','id');
    }

    public function export()
    {
        return $this->hasMany(Export::class, 'import_id', 'id');
    }

    public function connectedUser(){
        return $this->belongsTo(User::class,'user_id','id');
    }

    public function getLoonComponentIdAttribute()
    {
        $firstUrenbestand = Urenbestand::where('import_id', $this->id)->first();
        return $firstUrenbestand ? $firstUrenbestand->loon_component_id : null;
    }
}
