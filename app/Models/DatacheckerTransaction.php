<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DatacheckerTransaction extends Model
{
    protected $connection = 'datachecker';

    protected $table = 'transactions';

    protected $primaryKey = 'id';

    public $keyType = 'string';

    public $incrementing = false;

    public function jetApplicant()
    {
        return $this->belongsTo(JetApplicant::class, 'user_id', 'flexapp_id');
    }

    public function datacheckerTransactionResult()
    {
        return $this->hasOne(DatacheckerTransactionResult::class, 'transaction_id', 'id');
    }

    public function residencePermitRenewal()
    {
        return $this->hasOne(ResidencePermitRenewal::class, 'datachecker_transaction_id', 'id');
    }
}
