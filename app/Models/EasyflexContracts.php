<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class EasyflexContracts extends Model
{
    protected $connection = 'easyflex';

    protected $table = 'ds_fw_arbeidscontract';

    protected $primaryKey = 'fw_arbeidscontract_contract';

    public $keyType = 'integer';

    public function JetApplicant()
    {
        return $this->belongsTo(JetApplicant::class, 'easyflex_registratienummer', 'easyflex_id');
    }
    public function Easyflexuser(){
        return $this->belongsTo(EasyflexUser::class, 'easyflex_registratienummer', 'easyflex_registratienummer');
    }
}
