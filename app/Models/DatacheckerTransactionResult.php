<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DatacheckerTransactionResult extends Model
{
    protected $connection = 'datachecker';

    protected $table = 'transaction_results';

    protected $primaryKey = 'remote_result_id';

    public $keyType = 'string';

    public $incrementing = false;

    public function datacheckerTransaction()
    {
        return $this->belongsTo(DatacheckerTransaction::class, 'transaction_id', 'id');
    }

    public function datacheckerIdentityDocument()
    {
        return $this->hasOne(DatacheckerIdentityDocument::class, 'transaction_result_id', 'remote_result_id');
    }

    public function datacheckerResultImage()
    {
        return $this->hasMany(DatacheckerResultImage::class, 'transaction_result_id', 'remote_result_id');
    }
}
