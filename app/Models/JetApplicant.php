<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use App\Models\Plaatsing;

use App\Models\JetApplicantEnrollmentStatus as EnrollmentStatus;
use App\Models\JetApplicantEnrollmentStatusNew as EnrollmentStatusNew;
use App\Models\JetApplicantRenewalStatus as RenewalStatus;

class JetApplicant extends Model
{
    /**
     * The database connection that should be used by the model.
     * @var string
     */
    protected $connection = 'pgsql';

    /**
     * The table associated with the model.
     * @var string
     */
    protected $table = 'applicants';

    /**
     * The primary key associated with the table.
     * @var string
     */
    protected $primaryKey = 'applicant_id';

    /**
     * The data type of the auto-incrementing ID.
     * @var string
     */
    protected $keyType = 'int';


    /**
     * The attributes that aren't mass assignable.
     * @var array
     */
    protected $guarded = [];

    /**
     * Get the Enrollment Status associated with the user.
     */
    public function enrollmentStatus(): HasOne
    {
        return $this->hasOne(EnrollmentStatus::class, 'applicant_id');
    }

    /**
     * Get the Renewal Status associated with the user.
     */
    public function renewalStatus(): HasOne
    {
        return $this->hasOne(RenewalStatus::class, 'applicant_id', 'applicant_id');
    }

    /**
     * Get the Enrollment Status associated with the user.
     */
    public function enrollmentStatusNew(): HasOne
    {
        return $this->hasOne(EnrollmentStatusNew::class, 'applicant_id');
    }

    /**
     * Get the Enrollment Status associated with the user.
     */
    public function flexappData(): HasOne
    {
        return $this->hasOne(\App\Models\Flexapp\User::class, 'uuid', 'flexapp_id');
    }

    /**
     * Get the Enrollment Status associated with the user.
     */
    public function wenoteData(): HasOne
    {
        return $this->hasOne(\App\Models\WenoteUser::class, 'flex_id', 'flexapp_id');
    }

    /**
     * Get the Plaatsingen associated with the user.
     */
    public function plaatsing(): HasMany
    {
        return $this->hasMany(Plaatsing::class, 'applicant_id');
    }

    public function weeklyHours()
    {
        return $this->hasMany(WeeklyHours::class,  'applicant_id');
    }

    public function urenbestanden(): HasMany
    {
        return $this->hasMany(Urenbestand::class, 'driver_id', 'scoober_id');
    }

    public function employeeOffboarding(): hasMany
    {
        return $this->hasMany(EmployeeOffboarding::class, 'applicant_id');
    }

    public function EasyflexContract(): hasMany
    {
        return $this->hasMany(EasyflexContracts::class, 'easyflex_registratienummer');
    }

    public function EasyflexPlaatsing(): hasMany
    {
        return $this->hasMany(EasyflexPlaatsing::class, 'easyflex_registratienummer');
    }

    public function contractRenewals()
    {
        return $this->hasMany(ContractRenewal::class, 'applicant_id');
    }

    public function jetCourierDocumentRequestLogs(): HasMany
    {
        return $this->hasMany(JetCourierDocumentRequestLog::class, JetCourierDocumentRequestLog::LEAD_ID, 'lead_id');
	}

    public function residencePermitRenewal()
    {
        return $this->hasMany(ResidencePermitRenewal::class, 'applicant_id');
    }

    public function twvInformation()
    {
        return $this->hasMany(TwvInformation::class, 'applicant_id');
    }

    public function dataCheckerTransaction()
    {
        return $this->hasMany(DatacheckerTransaction::class, 'user_id', 'flexapp_id');
    }

    public function latestResidencePermitTransaction(): ?DatacheckerTransaction
    {
        return $this->dataCheckerTransaction()
            ->where('status', 20)
            ->whereHas('datacheckerTransactionResult', function ($query) {
                $query->where('status', 'APPROVED')
                    ->whereHas('datacheckerIdentityDocument', function ($q) {
                        $q->where('document_type', 'RESIDENCE_PERMIT');
                    });
            })
            ->latest()
            ->with('datacheckerTransactionResult.datacheckerIdentityDocument')
            ->first();
    }

    public function getLatestStickerInformation()
    {
        return $this->dataCheckerTransaction()
            ->where('status', 20)
            ->whereHas('datacheckerTransactionResult', function ($query) {
                $query->where('status', 'APPROVED')
                    ->whereHas('datacheckerIdentityDocument', function ($q) {
                        $q->where('document_type', 'PASSPORT');
                    });
            })->latest()
            ->first();
    }
}
