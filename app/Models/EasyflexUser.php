<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EasyflexUser extends Model
{
    use HasFactory;

    protected $connection = 'easyflex';

    protected $table = 'active_users';

    protected $primaryKey = 'id';

    public $keyType = 'string';

    public function Wenoteuser(){
        return $this->belongsTo(WenoteUser::class,'UKT_id','UKT_id');
    }
    public function Easyflexuserdetails(){
        return $this->belongsTo(EasyflexUserDetails::class,'easyflex_registratienummer','easyflex_registratienummer');
    }

}
