<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DatacheckerIdentityDocument extends Model
{
    protected $connection = 'datachecker';

    protected $table = 'identity_documents';

    protected $primaryKey = 'id';

    public $keyType = 'string';

    public $incrementing = false;
    public function datacheckerTransactionResult(): BelongsTo
    {
        return $this->belongsTo(DatacheckerTransactionResult::class, ownerKey: 'remote_result_id');
    }
}
