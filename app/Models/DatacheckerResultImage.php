<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class DatacheckerResultImage extends Model
{
    protected $connection = 'datachecker';

    protected $table = 'result_images';

    protected $primaryKey = 'id';

    public $keyType = 'string';

    public $incrementing = false;
    public function datacheckerTransactionResult(): BelongsTo
    {
        return $this->belongsTo(DatacheckerTransactionResult::class, 'transaction_result_id', 'remote_result_id');
    }
}
