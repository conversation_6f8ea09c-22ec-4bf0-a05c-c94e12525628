<?php

namespace App\Models\Flexapp;

use Illuminate\Database\Eloquent\Model;

class UserDrivingLicense extends Model
{
    /**
     * The database connection that should be used by the model.
     * @var string
     */
    protected $connection = 'userservice';

    /**
     * The table associated with the model.
     * @var string
     */
    protected $table = 'driving_licenses';

    /**
     * The primary key associated with the table.
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     * @var string
     */
    protected $keyType = 'int';


    /**
     * The attributes that aren't mass assignable.
     * @var array
     */
    // protected $guarded = [];
}
