<?php

namespace App\Models\Flexapp;

use App\Models\JetApplicant;
use App\Services\FusionauthService;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Http;



class User extends Model
{
    /**
     * The database connection that should be used by the model.
     * @var string
     */
    protected $connection = 'userservice';

    /**
     * The table associated with the model.
     * @var string
     */
    protected $table = 'jwt_users';

    /**
     * The primary key associated with the table.
     * @var string
     */
    protected $primaryKey = 'uuid';

    /**
     * The data type of the auto-incrementing ID.
     * @var string
     */
    protected $keyType = 'string';


    /**
     * The attributes that aren't mass assignable.
     * @var array
     */
    // protected $guarded = [];

    public function address(): HasOne {
        return $this->hasOne(\App\Models\Flexapp\UserAddress::class, 'uuid', 'uuid');
    }

    public function applicationProfile(): HasOne {
        return $this->hasOne(\App\Models\Flexapp\UserApplicationProfile::class, 'uuid', 'uuid');
    }

    public function financialDetails(): HasOne {
        return $this->hasOne(\App\Models\Flexapp\UserFinancialDetails::class, 'uuid', 'uuid');
    }

    public function identification(): HasOne {
        return $this->hasOne(\App\Models\Flexapp\UserIdentification::class, 'uuid', 'uuid');
    }

    public function drivingLicense(): HasOne {
        return $this->hasOne(\App\Models\Flexapp\UserDrivingLicense::class, 'uuid', 'uuid');
    }

    public function info(): HasOne {
        return $this->hasOne(\App\Models\Flexapp\UserInfo::class, 'uuid', 'uuid');
    }

    public function media(): HasMany {
        return $this->hasMany(\App\Models\Flexapp\UserMedia::class, 'model_id', 'uuid');
    }

    public function personalData(): HasOne {
        return $this->hasOne(\App\Models\Flexapp\UserPersonalData::class, 'uuid', 'uuid');
    }

    public function successiveEmployership(): HasOne {
        return $this->hasOne(\App\Models\Flexapp\UserSuccessiveEmployership::class, 'uuid', 'uuid');
    }

    public function wenoteUser(): HasOne {
        return $this->hasOne(\App\Models\WenoteUser::class, 'uuid', 'flex_id');
    }

    public function jetApplicant(): HasOne
    {
        return $this->hasOne(JetApplicant::class, 'flexapp_id', 'uuid');
    }

    public function mediaByFileName($fileName)
    {
        return $this->media()->where('file_name', $fileName)->get();
    }
    public function mostRecentValidDrivingLicenseImages(): Attribute {
        return Attribute::make(
            get: function(mixed $value, array $attributes) {
                $token = FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.verifai_service'));
                $url = config('services.verifai_service.url')
                    . '/get_most_recent_valid_driving_license_image_urls/'
                    . $attributes['uuid'];

                $imageUrls = Http::withToken($token)
                    ->get($url)
                    ->collect()
                    ->collapse();

                return [
                    'front' => $imageUrls['primary'] ?? '',
                    'back' => $imageUrls['secondary'] ?? '',
                ];
            }
        );
    }

    public function mostRecentValidIdentificationImages(): Attribute {
        return Attribute::make(
            get: function(mixed $value, array $attributes) {
                $token = FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.verifai_service'));
                $url = config('services.verifai_service.url')
                    . '/get_most_recent_valid_id_image_urls/'
                    . $attributes['uuid'];

                $imageUrls = Http::withToken($token)
                    ->get($url)
                    ->collect()
                    ->collapse();

                return [
                    'front' => $imageUrls['primary'] ?? '',
                    'back' => $imageUrls['secondary'] ?? '',
                ];
            }
        );
    }
}
