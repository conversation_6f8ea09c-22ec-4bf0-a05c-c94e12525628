<?php

namespace App\Models\Flexapp;

use App\Models\JetApplicant;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasOne;

class UserInfo extends Model
{
    /**
     * The database connection that should be used by the model.
     * @var string
     */
    protected $connection = 'userservice';

    /**
     * The table associated with the model.
     * @var string
     */
    protected $table = 'info';

    /**
     * The primary key associated with the table.
     * @var string
     */
    protected $primaryKey = 'id';

    /**
     * The data type of the auto-incrementing ID.
     * @var string
     */
    protected $keyType = 'int';


    /**
     * The attributes that are mass assignable.
     * @var array
     */
    protected $fillable = [
        'uuid',
        'scoober_id',
        'twv_expiration_date',
        'twv_approved_date',
        'final_information_check'
    ];

    public function personalData(): HasOne {
        return $this->hasOne(\App\Models\Flexapp\UserPersonalData::class, 'uuid', 'uuid');
    }
}
