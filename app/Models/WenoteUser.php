<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WenoteUser extends Model
{
    use HasFactory;

    protected $connection = 'wenote';

    protected $table = 'uitzendkrachten';

    protected $primaryKey = 'UKT_id';

    public $keyType = 'string';

    public function Easyflexuser() :BelongsTo{
        return $this->belongsTo(EasyflexUser::class,'UKT_id','UKT_id');
    }

    public function Flexappuser() :BelongsTo{
        return $this->belongsTo(\App\Models\Flexapp\User::class,'flex_id','uuid');
    }
}
