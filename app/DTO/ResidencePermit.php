<?php

namespace App\DTO;

use App\Enums\JetEntity;
use Spatie\LaravelData\Data;

class ResidencePermit extends Data
{
    public string $transactionId;

    public string $Name;

    public string $Lead__c;

    public ?string $flexappId;

    public string $Type__c;

    public string $filePath;

    public string $fileData;

    public string $documentNumber;

    public string $documentExpiresAt;

    public string $residencePermitType;

    public string $courierDocumentId;

    public string $contentVersionId;

    public string $contentDocumentId;

    public string $contentDocumentLinkId;

    public function setIdFor(string $entity, string $id): self
    {
        match (JetEntity::from($entity)) {
            JetEntity::COURIER_DOCUMENT => $this->courierDocumentId = $id,
            JetEntity::CONTENT_VERSION => $this->contentVersionId = $id,
            JetEntity::QUERY => $this->contentDocumentId = $id,
            JetEntity::CONTENT_DOCUMENT_LINK => $this->contentDocumentLinkId = $id,
        };

        return $this;
    }
}
