<?php

namespace App\Filament\Resources\ResidencePermitResource\Pages;

use App\DTO\ResidencePermit;
use App\Filament\Resources\ResidencePermitResource;
use App\Models\DatacheckerTransaction;
use App\Models\Flexapp\User;
use App\Models\JetApplicant;
use App\Models\ResidencePermitRenewal;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ListResidencePermits extends ListRecords
{
    protected static string $resource = ResidencePermitResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        $flexappIds = JetApplicant::select('flexapp_id')
            ->whereHas('enrollmentStatus', function(Builder $query) {
                return $query
                    ->whereRaw('on_hold IS NOT true')
                    ->where([
                        ['agency_status', '=', 'Approved'],
                        ['step_in_process', '=', 10]
                    ]);
            })
            ->where(function ($query) {
                $query->whereDoesntHave('employeeOffboarding')
                    ->orWhereHas('employeeOffboarding', function ($subQuery) {
                        $subQuery->whereDate('offboarding_date', '>', now());
                    });
            })
            ->pluck('flexapp_id')->toArray();

        $datacheckerIds = DatacheckerTransaction::selectRaw('distinct on (user_id) id')
            ->from('transactions as dt')
            ->whereIn('user_id', $flexappIds)
            ->where('status', 20)
            ->orderBy('user_id')
            ->orderByDesc('created_at')
            ->pluck('id');

        $tabs = [
            'toekomstige permits' => Tab::make(),
            'twv' => Tab::make()
                ->modifyQueryUsing(function (){
                        $renewed = JetApplicant::whereHas('residencePermitRenewal', function ($renewQuery) {
                            $renewQuery->where('twv_required', true);
                            $renewQuery->where('renewal_completed', false);
                        })
                        ->with('residencePermitRenewal')
                        ->get()
                        ->flatMap(function ($applicant) {
                            return $applicant->residencePermitRenewal->pluck('datachecker_transaction_id');
                        })
                        ->unique()
                        ->values();

                    return DatacheckerTransaction::query()
                        ->where('document_type', 'residencePermit')
                        ->where('status', 20)
                        ->whereIn('id', $renewed);
                }),
            'verlopen permits' => Tab::make()
                ->modifyQueryUsing(function () use ($flexappIds, $datacheckerIds) {
                    return DatacheckerTransaction::whereIn('id', $datacheckerIds)
                        ->where('document_type','=','residencePermit')
                        ->whereHas('datacheckerTransactionResult', function ($query) {
                            $query->where('status', 'APPROVED')
                                ->whereHas('datacheckerIdentityDocument', function ($subQuery) {
                                    $subQuery->whereDate('date_of_expiry', '<', Carbon::today());
                                });
                        });
                }),
            'open' => Tab::make()
                ->modifyQueryUsing(function (Builder $query) {
                    $renewed = JetApplicant::whereHas('residencePermitRenewal', function ($renewQuery) {
                        $renewQuery->whereNot('renewal_completed', true);
                    })
                        ->with('residencePermitRenewal')
                        ->get()
                        ->flatMap(function ($applicant) {
                            return $applicant->residencePermitRenewal->pluck('datachecker_transaction_id');
                        })
                        ->unique()
                        ->values();

                    return DatacheckerTransaction::query()
                        ->where('document_type', 'residencePermit')
                        ->where('status', 20)
                        ->whereIn('id', $renewed);
                }),
            'afgerond' => Tab::make()
                ->modifyQueryUsing(function (Builder $query) {
                    $renewed = JetApplicant::whereHas('residencePermitRenewal', function ($renewQuery) {
                        $renewQuery->where('renewal_completed', true);
                    })
                        ->with('residencePermitRenewal')
                        ->get()
                        ->flatMap(function ($applicant) {
                            return $applicant->residencePermitRenewal->pluck('datachecker_transaction_id');
                        })
                        ->unique()
                        ->values();

                    return DatacheckerTransaction::query()
                        ->where('document_type', 'residencePermit')
                        ->where('status', 20)
                        ->whereIn('id', $renewed);
                }),
        ];
        return $tabs;
    }
}
