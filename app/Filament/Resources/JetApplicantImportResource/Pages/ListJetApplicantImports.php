<?php

namespace App\Filament\Resources\JetApplicantImportResource\Pages;

use App\Filament\Resources\JetApplicantImportResource;
use App\Filament\Imports\JetApplicantImporter;
use App\Filament\Imports\JetApplicantImporterJob;

use Filament\Actions\ImportAction;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListJetApplicantImports extends ListRecords
{
    protected static string $resource = JetApplicantImportResource::class;

    protected static ?string $title = 'Jet applicant contractverlengingen imports';

    protected function getHeaderActions(): array
    {
        return [
            ImportAction::make()
                ->label('Import CSV')
                ->importer(JetApplicantImporter::class)
                ->job(JetApplicantImporterJob::class)
        ];
    }

    public function getTableQuery(): Builder
    {
        $query = JetApplicantImportResource::getEloquentQuery()->getModel()->newQuery();

        return $query->where('importer', JetApplicantImporter::class);
    }
}
