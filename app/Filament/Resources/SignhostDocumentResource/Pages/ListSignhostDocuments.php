<?php

namespace App\Filament\Resources\SignhostDocumentResource\Pages;

use App\Filament\Resources\SignhostDocumentResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Model;

class ListSignhostDocuments extends ListRecords
{
    protected static string $resource = SignhostDocumentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }

    public function getTableRecordKey(Model $record): string
    {
        return 'id';
    }
}
