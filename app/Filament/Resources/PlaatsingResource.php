<?php

namespace App\Filament\Resources;

use App\Filament\Resources\PlaatsingResource\Pages;
use App\Filament\Resources\PlaatsingResource\RelationManagers;
use App\Models\Plaatsing;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class PlaatsingResource extends Resource
{
    protected static ?string $model = Plaatsing::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'Plaatsingen';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('applicant_id')
                    ->label('Applicant ID')
                ,
                Forms\Components\TextInput::make('plaatsing_id')
                    ->label('Plaatsing ID')
                ,
                Forms\Components\TextInput::make('kpcode')
                    ->label('KP Code')
                ,
                Forms\Components\DatePicker::make('offboard_date')
                    ->label('Offboarding datum')
                ,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('applicant_id')
                    ->searchable()
                ,
                Tables\Columns\TextColumn::make('plaatsing_id')
                    ->searchable()
                ,
                Tables\Columns\TextColumn::make('created_at')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('offboard_date')
                    ->label('Offboarding datum')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('kpcode')
                    ->searchable()
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPlaatsings::route('/'),

        ];
    }
}
