<?php

namespace App\Filament\Resources\SignhostTransactionLogResource\Pages;

use App\Filament\Resources\SignhostTransactionLogResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSignhostTransactionLogs extends ListRecords
{
    protected static string $resource = SignhostTransactionLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }
}
