<?php

namespace App\Filament\Resources\LoonComponent\ExportResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Table;

class UrenbestandRelationManager extends RelationManager
{
    protected static string $relationship = 'urenbestanden';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('import_id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('import_id')
            ->columns([
                Tables\Columns\TextColumn::make('driver_id')
                    ->label('Scoober ID')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('date')
                    ->date('d-m-Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('hours')
                    ->default('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->default('-'),
                Tables\Columns\TextColumn::make('city')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('from')
                    ->time('H:i')
                    ->label('Start time')
                    ->sortable(),
                Tables\Columns\TextColumn::make('to')
                    ->label('End time')
                    ->time('H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('reason')
                    ->searchable()
                    ->default('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('sub_reason')
                    ->default('-')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\IconColumn::make('absence')
                    ->boolean()
                    ->sortable(),
            ])
            ->actions([
                DeleteAction::make(),
            ]);
    }
}
