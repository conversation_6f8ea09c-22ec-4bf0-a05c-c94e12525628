<?php

namespace App\Filament\Resources\LoonComponent\ExportResource\Pages;

use App\Filament\Resources\LoonComponent\ExportResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListExports extends ListRecords
{
    protected static string $resource = ExportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
