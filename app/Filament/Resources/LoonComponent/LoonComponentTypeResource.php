<?php

namespace App\Filament\Resources\LoonComponent;

use App\Filament\Clusters\LoonComponent as LoonComponentCluster;
use App\Filament\Resources\LoonComponent;
use App\Filament\Resources\LoonComponent\LoonComponentTypeResource\RelationManagers;
use App\Models\LoonComponentType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class LoonComponentTypeResource extends Resource
{
    protected static ?string $model = LoonComponentType::class;
    protected static ?string $navigationGroup = 'Loon info';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('description')
                    ->maxLength(255),
                Forms\Components\TextInput::make('default_value')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('default_unit')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->searchable(),
                Tables\Columns\TextColumn::make('default_value')
                    ->searchable(),
                Tables\Columns\TextColumn::make('default_unit')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [

        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => LoonComponent\LoonComponentTypeResource\Pages\ListLoonComponentTypes::route('/'),
            'create' => LoonComponent\LoonComponentTypeResource\Pages\CreateLoonComponentType::route('/create'),
            'edit' => LoonComponent\LoonComponentTypeResource\Pages\EditLoonComponentType::route('/{record}/edit'),
        ];
    }
}
