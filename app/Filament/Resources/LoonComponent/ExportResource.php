<?php

namespace App\Filament\Resources\LoonComponent;

use App\Filament\Resources\ExportResource\Pages;
use App\Filament\Resources\ExportResource\RelationManagers;
use App\Filament\Resources\LoonComponent;
use App\Models\Export;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ExportResource extends Resource
{
    protected static ?string $model = Export::class;
    protected static ?string $label = 'Export';
    protected static ?string $navigationGroup = 'Loon info';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('import_id')
                    ->disabled(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('import_id')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('file_name'),
                Tables\Columns\TextColumn::make('file_nr')->label('Easyflex nr'),
                Tables\Columns\TextColumn::make('created_at')
                    ->sortable(),
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            LoonComponent\ExportResource\RelationManagers\UrenbestandRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => LoonComponent\ExportResource\Pages\ListExports::route('/'),
            'create' => LoonComponent\ExportResource\Pages\CreateExport::route('/create'),
            'edit' => LoonComponent\ExportResource\Pages\EditExport::route('/{record}/edit'),
        ];
    }
}
