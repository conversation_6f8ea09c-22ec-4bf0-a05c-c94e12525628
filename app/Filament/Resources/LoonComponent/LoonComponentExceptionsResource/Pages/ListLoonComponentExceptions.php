<?php

namespace App\Filament\Resources\LoonComponent\LoonComponentExceptionsResource\Pages;

use App\Filament\Resources\LoonComponent\LoonComponentExceptionsResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLoonComponentExceptions extends ListRecords
{
    protected static string $resource = LoonComponentExceptionsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
