<?php

namespace App\Filament\Resources\LoonComponent\LoonComponentTypeResource\Pages;

use App\Filament\Resources\LoonComponent\LoonComponentTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLoonComponentTypes extends ListRecords
{
    protected static string $resource = LoonComponentTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
