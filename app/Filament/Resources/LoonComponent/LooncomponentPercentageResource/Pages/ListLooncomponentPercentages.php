<?php

namespace App\Filament\Resources\LoonComponent\LooncomponentPercentageResource\Pages;

use App\Filament\Resources\LoonComponent\LooncomponentPercentageResource;
use App\Models\LooncomponentPercentage;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Support\Facades\DB;

class ListLooncomponentPercentages extends ListRecords
{
    protected static string $resource = LooncomponentPercentageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        $tabs = [
            'alle' => Tab::make()->badge($this->getModel()::count())
        ];

        $totals = LooncomponentPercentage::select('function', DB::raw('count(*) as total'))
            ->groupBy('function')
            ->orderBy('function', 'asc')
            ->get();

        foreach ($totals as $total) {
            $name = $total->function;

            $tabs[$name] = Tab::make($name)
                ->badge($total->total)
                ->modifyQueryUsing(function ($query) use ($name) {
                    return $query->where('function', $name);
                });
        }

        return $tabs;
    }
}
