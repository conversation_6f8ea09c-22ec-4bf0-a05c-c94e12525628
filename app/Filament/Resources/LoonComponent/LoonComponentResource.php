<?php

namespace App\Filament\Resources\LoonComponent;

use App\Filament\Clusters\LoonComponent as LoonComponentCluster;
use App\Filament\Imports\CSVImporter;
use App\Filament\Resources\LoonComponent\LoonComponentResource\Pages\CreateLoonComponent;
use App\Filament\Resources\LoonComponent\LoonComponentResource\Pages\EditLoonComponent;
use App\Filament\Resources\LoonComponent\LoonComponentResource\Pages\ListLoonComponents;
use App\Filament\Resources\LoonComponentResource\Pages;
use App\Models\LoonComponent;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use League\Csv\Reader;


class LoonComponentResource extends Resource
{
    protected static ?string $model = LoonComponent::class;

    protected static ?string $navigationGroup = 'Loon info';
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('description')
                    ->maxLength(255),
                Forms\Components\TextInput::make('value')
                    ->maxLength(255),
                Forms\Components\TextInput::make('unit')
                    ->maxLength(255),
                Forms\Components\TextInput::make('status')
                    ->required()
                    ->numeric(),
                Forms\Components\Select::make('loon_component_type_id')
                    ->relationship(name: 'LoonComponentType', titleAttribute: 'name')
                    ->required()
            ]);
    }

    public function handle($csv, /* Add other setup/settings parameters here if needed */)
    {
        // Get the uploaded file path
        $filePath = $csv->storeAs('csv', $csv->getClientOriginalName());

        // Read the CSV file
        $reader = Reader::createFromPath(storage_path('app/' . $filePath), 'r');
        $reader->setHeaderOffset(0);

        // Process each row
        foreach ($reader as $row) {
            // Process each row based on your setup/settings
            // Example: Create a new CSV file with selected data
        }

        // Close the CSV reader
        $reader->close();

        // Return success message or perform other actions
        return 'CSV file imported and processed successfully.';
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('description')
                    ->searchable(),
                Tables\Columns\TextColumn::make('value')
                    ->searchable(),
                Tables\Columns\TextColumn::make('unit')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('looncomponenttype.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [

        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListLoonComponents::route('/'),
            'create' => CreateLoonComponent::route('/create'),
            'edit' => EditLoonComponent::route('/{record}/edit'),
        ];
    }
}
