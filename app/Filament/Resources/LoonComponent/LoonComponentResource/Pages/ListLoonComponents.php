<?php

namespace App\Filament\Resources\LoonComponent\LoonComponentResource\Pages;

use App\Filament\Resources\LoonComponent\LoonComponentResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLoonComponents extends ListRecords
{
    protected static string $resource = LoonComponentResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
