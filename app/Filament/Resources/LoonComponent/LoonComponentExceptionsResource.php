<?php

namespace App\Filament\Resources\LoonComponent;

use App\Filament\Resources\LoonComponentExceptionsResource\Pages;
use App\Filament\Resources\LoonComponentExceptionsResource\RelationManagers;
use App\Models\LoonComponentExceptions;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class LoonComponentExceptionsResource extends Resource
{
    protected static ?string $model = LoonComponentExceptions::class;

    protected static ?string $navigationLabel = 'Afwijkende data';

    protected static ?string $navigationGroup = 'Loon info';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('value')
                    ->label('Percentage')
                    ->required()
                    ->maxLength(255),
                Forms\Components\DatePicker::make('exception_date')
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->sortable(),
                Tables\Columns\TextColumn::make('value')
                    ->label('Percentage')
                    ->sortable(),
                Tables\Columns\TextColumn::make('exception_date')
                    ->date()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => LoonComponentExceptionsResource\Pages\ListLoonComponentExceptions::route('/'),
            'create' => LoonComponentExceptionsResource\Pages\CreateLoonComponentExceptions::route('/create'),
            'edit' => LoonComponentExceptionsResource\Pages\EditLoonComponentExceptions::route('/{record}/edit'),
        ];
    }
}
