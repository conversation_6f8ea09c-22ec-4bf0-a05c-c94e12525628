<?php

namespace App\Filament\Resources\JetCourierDocumentRequestLogResource\Pages;

use App\Filament\Resources\JetCourierDocumentRequestLogResource;
use Filament\Resources\Pages\ListRecords;

class ListJetCourierDocumentRequestLogs extends ListRecords
{
    protected static string $resource = JetCourierDocumentRequestLogResource::class;

    protected static ?string $title = 'Verblijfsvergunningen';

    protected static ?string $navigationGroup = 'Controle';

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }
}
