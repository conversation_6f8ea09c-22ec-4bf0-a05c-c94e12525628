<?php

namespace App\Filament\Resources\SignhostPostbackLogResource\Pages;

use App\Filament\Resources\SignhostPostbackLogResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSignhostPostbackLogs extends ListRecords
{
    protected static string $resource = SignhostPostbackLogResource::class;

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }
}
