<?php

namespace App\Filament\Resources\EasyflexUserResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EasyflexuserdetailsRelationManager extends RelationManager
{
    protected static string $relationship = 'Easyflexuserdetails';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('WKM_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('easyflex_registratienummer')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_inschrijver')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_dossiernummer')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_achternaam')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_voornaam')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_voorletters')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_voorvoegsels')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_meisjesachternaam')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_meisjesvoorvoegsels')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_geslacht')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_nationaliteit')
                    ->maxLength(255),
                Forms\Components\Toggle::make('fw_persoonsgegevens_all_nationaliteit_staatloos'),
                Forms\Components\DatePicker::make('fw_persoonsgegevens_all_gebdatum'),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_locatie')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_plaats')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_postcode')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_straat')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_huisnummer')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_toevoeging')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_land')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_burgerlijkestaat')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_relatiebeheerder')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_woonplaats')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_woonpostcode')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_woonstraat')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_woonhuisnummer')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_woontoevoeging')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_woonland')
                    ->maxLength(255),
                Forms\Components\DatePicker::make('fw_persoonsgegevens_all_datumindienst'),
                Forms\Components\DatePicker::make('fw_persoonsgegevens_all_inschrijfdatum'),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_flexwerkerstatus')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_autorisatiestatus')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_kentonsvan')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_redeninschrijving')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_voorheen')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_eersteindruk')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_briefhoofd')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_type')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_kvk_nummer')
                    ->numeric(),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_bl_kvk_nummer')
                    ->maxLength(255),
                Forms\Components\DatePicker::make('fw_persoonsgegevens_all_kvk_controle'),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_btwnummer')
                    ->maxLength(255),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_businessunits'),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_memorubrieken'),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_communicatie'),
                Forms\Components\DateTimePicker::make('fw_persoonsgegevens_all_wijzigdatum'),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_reservering'),
                Forms\Components\DatePicker::make('fw_persoonsgegevens_all_laatstgewerkt'),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_flexwrapp')
                    ->numeric(),
                Forms\Components\Toggle::make('fw_persoonsgegevens_afscherming_persoonsgegevens'),
                Forms\Components\DatePicker::make('fw_persoonsgegevens_all_einddatum_arbeidsmigrant'),
                Forms\Components\DatePicker::make('fw_persoonsgegevens_all_verloondtot'),
                Forms\Components\TextInput::make('fw_persoonsgegevens_all_bl_btwnummer')
                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('UKT_id')
            ->columns([
                Tables\Columns\TextColumn::make('WKM_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('easyflex_registratienummer')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_inschrijver')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_dossiernummer')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_achternaam')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_voornaam')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_voorletters')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_voorvoegsels')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_meisjesachternaam')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_meisjesvoorvoegsels')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_geslacht')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_nationaliteit')
                    ->searchable(),
                Tables\Columns\IconColumn::make('fw_persoonsgegevens_all_nationaliteit_staatloos')
                    ->boolean(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_gebdatum')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_locatie')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_plaats')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_postcode')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_straat')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_huisnummer')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_toevoeging')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_land')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_burgerlijkestaat')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_relatiebeheerder')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_woonplaats')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_woonpostcode')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_woonstraat')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_woonhuisnummer')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_woontoevoeging')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_woonland')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_datumindienst')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_inschrijfdatum')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_flexwerkerstatus')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_autorisatiestatus')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_kentonsvan')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_redeninschrijving')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_voorheen')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_eersteindruk')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_briefhoofd')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_type')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_kvk_nummer')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_bl_kvk_nummer')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_kvk_controle')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_btwnummer')
                    ->searchable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_wijzigdatum')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_laatstgewerkt')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_flexwrapp')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\IconColumn::make('fw_persoonsgegevens_afscherming_persoonsgegevens')
                    ->boolean(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_einddatum_arbeidsmigrant')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_verloondtot')
                    ->date()
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_persoonsgegevens_all_bl_btwnummer')
                    ->searchable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
