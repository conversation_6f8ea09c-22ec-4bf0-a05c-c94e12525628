<?php

namespace App\Filament\Resources;

use App\Filament\Resources\WenoteUserResource\Pages;
use App\Filament\Resources\WenoteUserResource\RelationManagers\EasyflexuserRelationManager;
use App\Models\WenoteUser;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use pxlrbt\FilamentExcel\Actions\Tables\ExportBulkAction;
use Illuminate\Support\Facades\Log;

class WenoteUserResource extends Resource
{
    protected static ?string $model = WenoteUser::class;

    protected static bool $shouldRegisterNavigation = true;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationLabel = 'Uitzendkrachten';

    public static function table(Table $table): Table
    {

        return $table
            ->columns([
                Tables\Columns\TextColumn::make('UKT_id')
                    ->label('UKT id')
                    ->searchable(),

                 Tables\Columns\TextColumn::make('naam')
                     ->getStateUsing(fn ($record) => $record->voornaam . ' ' . $record->tussenvoegsels . ' ' . $record->achternaam)
                     ->searchable(['voornaam', 'tussenvoegsels', 'achternaam']),

                Tables\Columns\TextColumn::make('flex_id')
                    ->searchable(),

                Tables\Columns\TextColumn::make('mobielnummer')
                    ->label('Telefoon')
                    ->searchable(),

                Tables\Columns\TextColumn::make('emailadres')
                    ->label('Emailadres')
                    ->searchable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->color(fn ($record) => ($record->status === 0) ? 'danger' : 'success')
                    ->searchable(),
            ])
            ->defaultPaginationPageOption(25)
            ->recordUrl(fn ($record) => $record->status === 1 ? Pages\ViewWenoteUser::getUrl(['record' => $record]) : Pages\ViewProgressWenoteUser::getUrl(['record' => $record]))
            ->defaultSort('UKT_id', 'desc')
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->multiple()
                    ->options([
                        '1' => '1',
                        '0' => '0'
                    ])
            ]);
    }

    public static function getRelations(): array
    {
        return [
            EasyflexuserRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWenoteUsers::route('/'),
            'create' => Pages\CreateWenoteUser::route('/create'),
            'view' => Pages\ViewWenoteUser::route('/{record}'),
            'view-progress' => Pages\ViewProgressWenoteUser::route('/{record}/progress'),
            'edit' => Pages\EditWenoteUser::route('/{record}/edit'),
        ];
    }
}
