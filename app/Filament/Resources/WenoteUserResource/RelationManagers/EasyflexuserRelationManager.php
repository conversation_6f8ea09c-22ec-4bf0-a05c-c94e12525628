<?php

namespace App\Filament\Resources\WenoteUserResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EasyflexuserRelationManager extends RelationManager
{
    protected static string $relationship = 'Easyflexuser';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('UKT_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('WKM_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('easyflex_registratienummer')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('flex_id'),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('UKT_id')
            ->columns([
                Tables\Columns\TextColumn::make('WKM_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('UKT_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('easyflex_registratienummer')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('flex_id'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
