<?php

namespace App\Filament\Resources\WenoteUserResource\Pages;

use App\Filament\Resources\WenoteUserResource;
use App\Http\Controllers\ContractController;
use App\Services\EasyflexService;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditWenoteUser extends EditRecord
{
    protected static string $resource = WenoteUserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
