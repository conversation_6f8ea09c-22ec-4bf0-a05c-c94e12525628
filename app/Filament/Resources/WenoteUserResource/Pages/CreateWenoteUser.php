<?php

namespace App\Filament\Resources\WenoteUserResource\Pages;

use App\Filament\Resources\WenoteUserResource;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Pages\CreateRecord;

class CreateWenoteUser extends CreateRecord
{
    protected static string $resource = WenoteUserResource::class;

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('voornaam')
                    ->maxLength(50),
                TextInput::make('tussenvoegsels')
                    ->maxLength(50),
                TextInput::make('achternaam')
                    ->maxLength(80),
                TextInput::make('voorletters')
                    ->maxLength(10),
                TextInput::make('geslacht')
                    ->maxLength(10),
                TextInput::make('bsn_nummer')
                    ->maxLength(15),
                TextInput::make('bankrekeningnummer')
                    ->maxLength(40),
                TextInput::make('mobielnummer')
                    ->maxLength(15),
                TextInput::make('emailadres')
                    ->email()
                    ->maxLength(150),
                TextInput::make('emailadres_alt')
                    ->email()
                    ->maxLength(150),
                TextInput::make('reisafstand')
                    ->numeric()
                    ->default(50),
                TextInput::make('mobile_confirmed')
                    ->numeric()
                    ->default(0),
                TextInput::make('mobile_code')
                    ->maxLength(4),
                TextInput::make('mobile_confirm_count')
                    ->numeric()
                    ->default(1),
                TextInput::make('status')
                    ->numeric()
                    ->default(1),
                TextInput::make('ready_for_jobs')
                    ->numeric()
                    ->default(0),
                TextInput::make('ADS_id')
                    ->numeric(),
                TextInput::make('ADS_id_ok')
                    ->numeric()
                    ->default(0),
                TextInput::make('ONU_id')
                    ->numeric(),
                DatePicker::make('datum_beschikbaarheid_aangemaakt'),
                DatePicker::make('datum_registratie_aangemaakt'),
                DatePicker::make('datum_activatie_registratie'),
                DatePicker::make('datum_uitgeschreven'),
                TextInput::make('reference_id_import')
                    ->maxLength(50),
                TextInput::make('language_selected')
                    ->maxLength(10),
                TextInput::make('burgelijke_staat')
                    ->maxLength(100),
                TextInput::make('nationaliteit_nederlands')
                    ->numeric()
                    ->default(1),
                TextInput::make('identificatie_akkoord')
                    ->numeric(),
                TextInput::make('werkzaam_zzp')
                    ->numeric(),
                TextInput::make('werkzaam_zzp_ook_kandidaat')
                    ->numeric(),
                TextInput::make('werkzaam_zzp_uurtarief')
                    ->numeric(),
                TextInput::make('communicatie_wijze')
                    ->numeric(),
                TextInput::make('werkzaam_zzp_heeft_var')
                    ->numeric(),
                TextInput::make('type_id')
                    ->maxLength(60),
                DatePicker::make('id_geldig_tot'),
                TextInput::make('id_filled_in')
                    ->numeric(),
                TextInput::make('id_nummer')
                    ->maxLength(100),
                TextInput::make('werkzaam_zzp_btw')
                    ->numeric(),
                TextInput::make('blokkade')
                    ->numeric()
                    ->default(0),
                TextInput::make('referring_friend_UKT_id')
                    ->numeric(),
                DatePicker::make('geboorte_datum'),
                TextInput::make('medewerker_nummer')
                    ->numeric(),
                TextInput::make('is_personeel')
                    ->numeric(),
                TextInput::make('heeft_werkvergunning')
                    ->numeric(),
                DatePicker::make('werkvergunning_einddatum'),
                Textarea::make('motivatie')
                    ->columnSpanFull(),
                TextInput::make('is_deleted')
                    ->numeric(),
                TextInput::make('referentie_naam')
                    ->maxLength(50),
                TextInput::make('referentie_nummer')
                    ->maxLength(50),
                TextInput::make('calamiteiten_naam')
                    ->maxLength(50),
                TextInput::make('calamiteiten_nummer')
                    ->maxLength(50),
                TextInput::make('UZB_id')
                    ->label('uzb id')
                    ->numeric(),
                TextInput::make('exported_to_easyflex')
                    ->maxLength(512),
                TextInput::make('easyflex_login')
                    ->maxLength(250),
                TextInput::make('easyflex_wachtwoord')
                    ->maxLength(250),
                TextInput::make('uren_lat')
                    ->maxLength(10),
                TextInput::make('opvolgend_werkgeverschap')
                    ->numeric(),
                TextInput::make('salaris')
                    ->maxLength(50),
                TextInput::make('loonheffing_toepassen')
                    ->numeric(),
                TextInput::make('easyflex_registratienummer')
                    ->maxLength(255),
                TextInput::make('from_frontoffice')
                    ->numeric(),
                TextInput::make('ready_for_frontoffice')
                    ->numeric(),
                TextInput::make('id_from_frontoffice')
                    ->numeric(),
                TextInput::make('IDA_id')
                    ->label('ida id')
                    ->numeric(),
                TextInput::make('uren_per')
                    ->maxLength(255),
                Textarea::make('id_api_response')
                    ->columnSpanFull(),
                TextInput::make('brp_filled_in')
                    ->numeric(),
                TextInput::make('payday_actief')
                    ->numeric(),
                TextInput::make('flex_id')
                    ->maxLength(255),
                TextInput::make('flexapp_final_information_check')
                    ->numeric(),
                TextInput::make('id_nationaliteit')
                    ->maxLength(2),
                Toggle::make('is_payroll'),
                TextInput::make('landcode_mobiel')
                    ->maxLength(10),
                Toggle::make('is_justeat'),
                TextInput::make('geboorte_plaats')
                    ->maxLength(255),
            ]);
    }
}
