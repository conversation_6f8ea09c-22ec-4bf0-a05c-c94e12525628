<?php

namespace App\Filament\Resources\WenoteUserResource\Pages;

use App\Filament\Resources\WenoteUserResource;
use Filament\Resources\Pages\ViewRecord;
use Filament\Infolists;
use Filament\Infolists\Infolist;

class ViewProgressWenoteUser extends ViewRecord
{
    protected static string $resource = WenoteUserResource::class;

    protected static string $recordTitleAttribute = 'name';

    public function getTitle(): string
    {
        $record = $this->getRecord();
        $name = [$record->voornaam, $record->tussenvoegsels, $record->achternaam];

        return join(' ', array_filter($name));
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist->schema([
            Infolists\Components\TextEntry::make('status')
                ->hiddenLabel()
                ->badge()
                ->color('success'),

            Infolists\Components\Section::make()->schema([
                Infolists\Components\Fieldset::make('')
                    ->schema([
                        Infolists\Components\ImageEntry::make('id_front'),
                        Infolists\Components\ImageEntry::make('id_back'),
                    ]),

                Infolists\Components\Fieldset::make('')
                    ->schema([
                        Infolists\Components\ImageEntry::make('rijbewijs_front'),
                        Infolists\Components\ImageEntry::make('rijbewijs_back'),
                    ]),

                Infolists\Components\Fieldset::make('Persoonlijke gegevens')
                    ->columns(1)
                    ->schema([
                        Infolists\Components\ViewEntry::make('voornaam')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('achternaam')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('voorletters')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('geslacht')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('bsn_nummer')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('nationaliteit')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('geboortedatum')->view('filament.components.view-field'),
                    ]),

                Infolists\Components\Fieldset::make('Financiële gegevens')
                    ->columns(1)
                    ->schema([
                        Infolists\Components\ViewEntry::make('bankrekeningnummer')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('voorletters')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('burgelijke_staat')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('loonheffing_toepassen')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('opvolgend_werkgeverschap')->view('filament.components.view-field'),
                    ]),

                Infolists\Components\Fieldset::make('Contactgegevens')
                    ->columns(1)
                    ->schema([
                        Infolists\Components\ViewEntry::make('emailadres')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('postcode')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('straat')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('huisnummer')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('plaats')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('telefoonnummer')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('landcode')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('land')->view('filament.components.view-field'),
                    ]),

                Infolists\Components\Fieldset::make('Legitimatie')
                    ->columns(1)
                    ->schema([
                        Infolists\Components\ViewEntry::make('nationaliteit')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('geboorteplaats')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('legitimatie')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('identificatie_akkoord')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('documentnr')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('id_geldig_tot')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('werkvergunning')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('werkvergunning_geldig_tot')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('rijbewijs')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('rijbewijs_geldig_tot')->view('filament.components.view-field'),
                        Infolists\Components\ViewEntry::make('rijbewijs_documentnr')->view('filament.components.view-field'),
                    ]),

                Infolists\Components\Fieldset::make('Status instellen')
                    ->columns(1)
                    ->schema([
                        Infolists\Components\Actions::make([
                            Infolists\Components\Actions\Action::make('Test status 0')
                                ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                ->iconPosition('after')
                                ->color('danger')
                                ->outlined()
                                ->action(function ($record) {
                                    $record->status = 0;
                                    $record->save();
                                }),

                            Infolists\Components\Actions\Action::make('Test status 1')
                                ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                ->iconPosition('after')
                                ->color('success')
                                ->outlined()
                                ->action(function ($record) {
                                    $record->status = 1;
                                    $record->save();
                                }),
                        ])
                    ]),

                Infolists\Components\Fieldset::make('Mag bezorgen met')
                    ->columns(1)
                    ->schema([
                        Infolists\Components\Actions::make([
                            Infolists\Components\Actions\Action::make('Fiets')
                                ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                ->iconPosition('after')
                                ->color('info')
                                ->disabled(),

                            Infolists\Components\Actions\Action::make('Scooter')
                                ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                ->icon('heroicon-c-x-mark')->iconPosition('after')
                                ->color('info'),

                            Infolists\Components\Actions\Action::make('auto')
                                ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                ->outlined()
                                ->iconPosition('after')
                                ->color('info')
                        ])
                    ]),

                Infolists\Components\Fieldset::make('Notitie')
                    ->columns(1)
                    ->schema([
                        Infolists\Components\Livewire::make('WenoteUserNote')
                    ])
            ])
        ]);
    }
}
