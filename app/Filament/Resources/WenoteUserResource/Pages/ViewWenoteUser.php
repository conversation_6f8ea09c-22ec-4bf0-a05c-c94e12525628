<?php

namespace App\Filament\Resources\WenoteUserResource\Pages;

use App\Filament\Resources\JetApplicantResource;
use App\Filament\Resources\WenoteUserResource;
use App\Http\Controllers\ContractController;
use App\Models\Export;
use App\Services\DataCheckerService;
use App\Services\EasyflexService;
use App\Services\UserService;
use Filament\Actions\Action;
use Filament\Actions\Imports\Models\Import;
use Filament\Infolists;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Infolist;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Carbon;

class ViewWenoteUser extends ViewRecord
{
    protected static string $resource = WenoteUserResource::class;

    protected static string $recordTitleAttribute = 'name';

    public function getTitle(): string
    {
        $record = $this->getRecord();
        $name = [$record->voornaam, $record->tussenvoegsels, $record->achternaam];

        return join(' ', array_filter($name));
    }

    protected function getHeaderWidgets(): array
    {
        return [];
    }

    protected function getActions(): array
    {
        return [
            Action::make('wenote')
                ->label('Open in Wenote')
                ->url(fn ($record) => config('services.wenote.app_url') . '/beheer/kandidaten.php?command=display_kandidaat&id=' .$record->UKT_id, shouldOpenInNewTab: true),

            Action::make('add legitimatie')
                ->label('Legitimatie toevoegen')
                ->iconPosition('after')
                ->requiresConfirmation()
                ->form([
                    TextInput::make('transaction_id')
                        ->label('Transactie ID')
                ])
                ->action(function (array $data, DataCheckerService $dataCheckerService) {
                    $record = $this->getRecord();
                    $flexid = $record->flex_id;

                    $response = $dataCheckerService->addTransaction([
                        'uuid' => $flexid,
                        'transactionId' => $data['transaction_id']
                    ]);

                    return Notification::make()
                        ->title($response['message'])
                        ->persistent()
                        ->success()
                        ->send();
                })
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
//        dd($this->record->Flexappuser->personalData);
        return $infolist->schema([
            Infolists\Components\Grid::make(['lg' => 2, '2xl' => 3])
                ->schema([
                    Infolists\Components\Section::make('Persoonlijke gegevens')
                        ->columnSpan(1)
                        ->headerActions([
                            Infolists\Components\Actions\Action::make('wijzig')
                                ->link()
                                ->modalHeading('Wijzig persoonlijke gegevens')

                                ->form([
                                    TextInput::make('voornaam')->maxLength(50),
                                    TextInput::make('achternaam')->maxLength(80),
                                    TextInput::make('voorletters')->maxLength(10),
                                    TextInput::make('geslacht')->maxLength(50),
                                    TextInput::make('bsn_nummer')->maxLength(50),
                                    TextInput::make('nationaliteit')->maxLength(50),
                                    DatePicker::make('geboorte_datum'),
                                    TextInput::make('nationaliteit')->maxLength(50),
                                ])
                        ])
                        ->schema([
                            Infolists\Components\ViewEntry::make('UKT_id')->label('Wenote ID')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('flex_id')->label('Flexapp ID')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('voornaam')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('achternaam')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('voorletters')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('geslacht')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('bsn_nummer')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('nationaliteit')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('geboorte_datum')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('nationaliteit')->view('filament.components.view-field'),
                        ]),

                    Infolists\Components\Section::make('Financiële gegevens')
                        ->columnSpan(1)
                        ->headerActions([
                            Infolists\Components\Actions\Action::make('Wijzig')->link()->slideOver()
                        ])
                        ->schema([
                            Infolists\Components\ViewEntry::make('bankrekeningnummer')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('voorletters')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('burgelijke_staat')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('bsn_nummer')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('loonheffing_toepassen')
                                ->getStateUsing(fn () => $this->getRecord()->loonheffing_toepassen ? 'Ja' : 'Nee')
                                ->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('opvolgend_werkgeverschap')->view('filament.components.view-field'),
                        ]),

                    Infolists\Components\Section::make('Identificaties')
                        ->columnSpan(1)
                        ->extraAttributes(['class' => 'fi-section--row-2'])
                        ->schema([
                            Infolists\Components\Livewire::make('DataCheckerDocuments', [
                                'uuid' => $this->getRecord()?->flex_id,
                            ]),
                        ]),

                    Infolists\Components\Section::make('Contact gegevens')
                        ->columnSpan(1)
                        ->headerActions([
                            Infolists\Components\Actions\Action::make('Wijzig')->link()->slideOver()
                        ])
                        ->schema([
                            Infolists\Components\ViewEntry::make('Flexappuser.personalData.email')->label('E-mailadres')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('street')->label('Straat')
                                ->getStateUsing(fn () => $this->getRecord()?->Flexappuser?->address?->street.' '.$this->getRecord()?->Flexappuser?->address?->house_number.'-'.$this->getRecord()?->Flexappuser?->address?->house_number_addition)
                                ->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('Flexappuser.address.zipcode')->label('Postcode')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('Flexappuser.address.city')->label('Woonplaats')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('Flexappuser.address.country')->label('Land')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('Flexappuser.address.country_code')->label('Landcode')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('phone_number')
                                ->getStateUsing(fn () => $this->getRecord()?->Flexappuser?->personalData?->country_code.' '.$this->getRecord()?->Flexappuser?->personalData?->phone_number)
                                ->label('Telefoonnummer')->view('filament.components.view-field'),
                        ]),

                    Infolists\Components\Section::make('Legitimatie')
                        ->columnSpan(1)
                        ->headerActions([
                            Infolists\Components\Actions\Action::make('Wijzig')->link()->slideOver()
                        ])
                        ->schema([
                            Infolists\Components\ViewEntry::make('emailadres')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('geboorte_plaats')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('legitimatie')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('identificatie_akkoord')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('id_geldig_tot')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('werkvergunning')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('werkvergunning_geldig_tot')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('rijbewijs')->view('filament.components.view-field'),
                            Infolists\Components\ViewEntry::make('rijbewijs_geldig_tot')->view('filament.components.view-field'),
                        ])
                ])
        ]);
    }
}
