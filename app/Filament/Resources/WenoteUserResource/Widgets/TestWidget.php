<?php

namespace App\Filament\Resources\WenoteUserResource\Widgets;

use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class TestWidget extends BaseWidget
{
    protected static ?string $pollingInterval = null;

    protected function getStats(): array
    {
        return [
            Stat::make('Uren', Number::format(24.5, locale: 'nl')),
            Stat::make('Declaraties',2),
            Stat::make('Bonus', Number::currency(121.78, 'EUR', 'nl'))
        ];
    }
}
