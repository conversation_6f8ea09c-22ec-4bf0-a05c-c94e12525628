<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EasyflexUserResource\Pages;
use App\Filament\Resources\EasyflexUserResource\RelationManagers;
use App\Filament\Resources\WenoteUserResource\RelationManagers\EasyflexuserRelationManager;
use App\Models\EasyflexUser;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class EasyflexUserResource extends Resource
{
    protected static ?string $model = EasyflexUser::class;

    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    public static function form(Form $form): Form
    {
        return $form
            ->schema(
                [
                Forms\Components\TextInput::make('WKM_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('UKT_id')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('easyflex_registratienummer')
                    ->required()
                    ->numeric(),
                Forms\Components\TextInput::make('flex_id'),
            ]
            );

    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('WKM_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('UKT_id')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('easyflex_registratienummer')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('flex_id'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),

                Tables\Actions\ViewAction::make(),
                // ...

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\EasyflexuserdetailsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEasyflexUsers::route('/'),
            'create' => Pages\CreateEasyflexUser::route('/create'),
            'edit' => Pages\EditEasyflexUser::route('/{record}/edit'),
        ];
    }
}
