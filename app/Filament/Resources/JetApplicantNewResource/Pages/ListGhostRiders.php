<?php

namespace App\Filament\Resources\JetApplicantNewResource\Pages;

use App\Filament\Resources\JetApplicantNewResource;
use Carbon\Carbon;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListGhostRiders extends ListRecords
{
    protected static string $resource = JetApplicantNewResource::class;

    protected static ?string $title = 'Ghost Riders';

    public function getTableQuery(): Builder
    {
        $query = JetApplicantNewResource::getEloquentQuery()->getModel()->newQuery();

        return $query
            ->whereHas('enrollmentStatusNew', function(Builder $query) {
                return $query
                    ->whereRaw('on_hold IS NOT true')
                    ->where([
                        ['agency_status', '=', 'Approved'],
                        ['step_in_process', '=', 16]
                    ])
                    ;
            })
            ->whereDate('pre_contract_start_date', '<=', Carbon::today()->subWeeks(2))
            ->leftJoin('urenbestand', 'applicants.scoober_id', '=', 'urenbestand.driver_id')
            ->whereNull('urenbestand.driver_id')
            ->whereDoesntHave('employeeOffboarding')
            ->select('applicants.*')
            ;
    }
}
