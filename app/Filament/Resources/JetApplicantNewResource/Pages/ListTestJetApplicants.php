<?php

namespace App\Filament\Resources\JetApplicantNewResource\Pages;

use App\Filament\Resources\JetApplicantNewResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListTestJetApplicants extends ListRecords
{
    protected static string $resource = JetApplicantNewResource::class;

    protected static ?string $title = 'Test applicants';

    public function getTableQuery(): Builder
    {
        $query = JetApplicantNewResource::getEloquentQuery()->getModel()->newQuery();

        return $query->where('is_test', true);
    }
}
