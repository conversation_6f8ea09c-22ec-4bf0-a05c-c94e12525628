<?php

namespace App\Filament\Resources\JetApplicantNewResource\Pages;

use App\Filament\Resources\JetApplicantNewResource;
use App\Models\Flexapp\UserInfo as FlexappUserInfo;

use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListNewJetApplicants extends ListRecords
{
    protected static string $resource = JetApplicantNewResource::class;

    protected static ?string $title = 'New applicants';

    public function getTableQuery(): Builder
    {
        $query = JetApplicantNewResource::getEloquentQuery()->getModel()->newQuery();

        return $query
            ->whereRaw('is_test IS NOT true')
            ->whereHas('enrollmentStatusNew', function(Builder $query) {
                return $query
                    ->where('agency_status', '=', 'New')
                    ->orWhere(function(Builder $query) {
                        return $query
                            ->where([
                                ['agency_status', '=', 'Approved'],
                                ['step_in_process', '<', 16]
                            ])
                        ;
                    })
                ;
            })
        ;
    }

    public function getTabs(): array
    {
        $twvRequieredUserFlexappIds = FlexappUserInfo::where('origin', 'jet')
            ->where('twv_required', true)
            ->whereNull('twv_approved')
            ->pluck('uuid')
        ;
        $newApplicantsQuery = $this->getTableQuery()->clone()->whereRaw('is_import IS NOT true')->whereNotIn('flexapp_id', $twvRequieredUserFlexappIds);
        $newApplicantsTwvQuery = $this->getTableQuery()->clone()->whereRaw('is_import IS NOT true')->whereIn('flexapp_id', $twvRequieredUserFlexappIds);
        $newApplicantsRandstadQuery = $this->getTableQuery()->clone()->where('is_import', true);
        return [
            'Alle' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $newApplicantsQuery)
                ->badge($newApplicantsQuery->count())
            ,
            'TWV' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $newApplicantsTwvQuery)
                ->badge($newApplicantsTwvQuery->count())
            ,
            'Randstad' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $newApplicantsRandstadQuery)
                ->badge($newApplicantsRandstadQuery->count())
            ,
        ];
    }
}
