<?php

namespace App\Filament\Resources\JetApplicantNewResource\Pages;

use App\Filament\Resources\JetApplicantNewResource;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListOnHoldJetApplicants extends ListRecords
{
    protected static string $resource = JetApplicantNewResource::class;

    protected static ?string $title = 'On hold applicants';

    public function getTableQuery(): Builder
    {
        $query = JetApplicantNewResource::getEloquentQuery()->getModel()->newQuery();

        return $query
            ->whereRaw('is_test IS NOT true')
            ->whereHas('enrollmentStatusNew', function(Builder $query) {
                return $query->where('on_hold', true);
            })
        ;
    }

    public function getTabs(): array
    {
        $onHoldApplicantsQuery = $this->getTableQuery()->clone()->whereRaw('is_import IS NOT true');
        $onHoldApplicantsRandstadQuery = $this->getTableQuery()->clone()->where('is_import', true);
        return [
            'Alle' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $onHoldApplicantsQuery)
                ->badge($onHoldApplicantsQuery->count())
            ,
            'Randstad' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $onHoldApplicantsRandstadQuery)
                ->badge($onHoldApplicantsRandstadQuery->count())
            ,
        ];
    }
}
