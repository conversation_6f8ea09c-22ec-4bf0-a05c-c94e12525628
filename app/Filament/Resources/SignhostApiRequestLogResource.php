<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SignhostApiRequestResource\Pages;
use App\Models\Filament\SignhostApiRequestLog;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SignhostApiRequestLogResource extends Resource
{
    protected static ?string $model = SignhostApiRequestLog::class;

    protected static ?string $navigationGroup = 'Controle';

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('transaction_id')
                    ->label('Signhost transaction')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('stage')
                    ->badge()
                    ->sortable()
                    ->searchable(),

                TextColumn::make('method')
                    ->label('HTTP method')
                    ->formatStateUsing(fn (string $state) => strtoupper($state))
                    ->sortable()
                    ->searchable(),

                TextColumn::make('response_code')
                    ->numeric()
                    ->badge()
                    ->sortable()
                    ->searchable(),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->label('Excecuted at')
                    ->sortable(),
            ])
            ->defaultGroup('transaction_id')
            ->filters([
                SelectFilter::make('stage')
                    ->modifyBaseQueryUsing(function (Builder $query, $state) {
                        if (! $state['value']) {
                            return $query;
                        }

                        return $query->where('stage', $state['value']);
                    })
                    ->options([
                        'make_request' => 'make_request',
                        'make_response' => 'make_response',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSignhostApiRequestLog::route('/'),
            'view' => Pages\ViewSignhostApiRequestLog::route('/{record}'),
        ];
    }
}
