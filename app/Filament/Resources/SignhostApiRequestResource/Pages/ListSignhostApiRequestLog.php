<?php

namespace App\Filament\Resources\SignhostApiRequestResource\Pages;

use App\Filament\Resources\SignhostApiRequestLogResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSignhostApiRequestLog extends ListRecords
{
    protected static string $resource = SignhostApiRequestLogResource::class;
    protected static ?string $navigationGroup = 'Controle';

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }
}
