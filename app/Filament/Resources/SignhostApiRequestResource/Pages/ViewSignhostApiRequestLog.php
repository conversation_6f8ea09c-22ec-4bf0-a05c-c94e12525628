<?php

namespace App\Filament\Resources\SignhostApiRequestResource\Pages;

use App\Filament\Resources\SignhostApiRequestLogResource;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Pages\ViewRecord;

class ViewSignhostApiRequestLog extends ViewRecord
{
    protected static string $resource = SignhostApiRequestLogResource::class;

    protected static ?string $navigationGroup = 'Controle';

    protected function getHeaderActions(): array
    {
        return [
            //
        ];
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('transaction_id')
                    ->label('Signhost transaction')
                    ->readOnly(),
                TextInput::make('method')
                    ->label('HTTP method'),
                TextInput::make('stage'),
                TextInput::make('response_code'),
                TextInput::make('response_error_code'),

                TextInput::make('response_message.Message'),

                Section::make([
                    KeyValue::make('headers')
                        ->hiddenLabel()
                        ->keyLabel('Header')
                        ->valueLabel('Value'),
                ])
                    ->description('Request headers that were sent to the Signhost API.'),

                Section::make([
                    KeyValue::make('transaction')
                        ->hiddenLabel()
                        ->keyLabel('Property')
                        ->valueLabel('Value'),
                ])
                    ->description('Information about the Signhost transaction'),
            ]);
    }
}
