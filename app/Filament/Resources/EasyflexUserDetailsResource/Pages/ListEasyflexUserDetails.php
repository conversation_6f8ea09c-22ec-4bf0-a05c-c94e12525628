<?php

namespace App\Filament\Resources\EasyflexUserDetailsResource\Pages;

use App\Filament\Resources\EasyflexUserDetailsResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListEasyflexUserDetails extends ListRecords
{
    protected static string $resource = EasyflexUserDetailsResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
