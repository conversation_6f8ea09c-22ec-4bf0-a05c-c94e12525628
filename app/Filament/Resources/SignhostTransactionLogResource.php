<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SignhostTransactionLogResource\Pages\ListSignhostTransactionLogs;
use App\Filament\Resources\SignhostTransactionLogResource\Pages\ViewSignhostTransactionLogs;
use App\Models\Filament\SignhostTransactionLog;
use Filament\Forms\Components\KeyValue;
use Filament\Forms\Components\Toggle as FormToggle;
use Filament\Forms\Components\TextInput as FormTextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn as TableIconColumn;
use Filament\Tables\Columns\TextColumn as TableTextColumn;
use Filament\Tables\Table;

class SignhostTransactionLogResource extends Resource
{
    protected static ?string $model = SignhostTransactionLog::class;

    protected static ?string $navigationGroup = 'Controle';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                FormTextInput::make('transaction_id'),
                FormTextInput::make('transaction_uuid'),
                FormTextInput::make('action'),
                FormToggle::make('status'),
                KeyValue::make('details')->json(),
                FormToggle::make('is_just_eat'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
            TableTextColumn::make('transaction_id'),
            TableTextColumn::make('transaction_uuid'),
            TableTextColumn::make('action'),
            TableIconColumn::make('status')->boolean(),
            TableIconColumn::make('details'),
            TableIconColumn::make('is_just_eat')->boolean(),
        ])
            ->filters([
                //
            ])
            ->actions([
                ViewAction::make(),
            ])
            ->bulkActions([
                //
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListSignhostTransactionLogs::route('/'),
            'view' => ViewSignhostTransactionLogs::route('/{record}'),
        ];
    }
}
