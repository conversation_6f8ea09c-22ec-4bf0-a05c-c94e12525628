<?php

namespace App\Filament\Resources\PlaatsingResource\Pages;

use App\Filament\Resources\PlaatsingResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPlaatsings extends ListRecords
{
    protected static string $resource = PlaatsingResource::class;

    protected static ?string $title = 'Plaatsingen';

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
