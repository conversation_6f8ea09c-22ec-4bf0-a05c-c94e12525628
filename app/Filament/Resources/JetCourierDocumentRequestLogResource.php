<?php

namespace App\Filament\Resources;

use App\Enums\JetEntity;
use App\Filament\Resources\JetCourierDocumentRequestLogResource\Pages\ListJetCourierDocumentRequestLogs;
use App\Models\JetCourierDocumentRequestLog;
use Filament\Infolists\Components\KeyValueEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Split;
use Filament\Infolists\Infolist;
use Filament\Tables\Actions\Action;
use Filament\Infolists\Components\TextEntry;
use Filament\Resources\Resource;
use Filament\Tables\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class JetCourierDocumentRequestLogResource extends Resource
{
    protected static ?string $model = JetCourierDocumentRequestLog::class;

    protected static bool $shouldRegisterNavigation = false;

    protected static ?string $navigationGroup = 'Controle';

    public static function getPages(): array
    {
        return [
            'index' => ListJetCourierDocumentRequestLogs::route('/'),
        ];
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        $record = $infolist->getRecord();

        return $infolist
            ->schema([
                Split::make([
                    Section::make([
                        TextEntry::make('created_at')
                            ->dateTime()
                            ->inlineLabel(),
                        TextEntry::make('transaction_id')
                            ->copyable()
                            ->inlineLabel('Transaction ID')
                            ->badge(),
                        TextEntry::make('entity')
                            ->inlineLabel('Entity')
                            ->badge(),
                        TextEntry::make('entity_id')
                            ->copyable()
                            ->inlineLabel('Entity ID')
                            ->badge(),
                    ])
                        ->description('Basic'),
                    Section::make([
                        TextEntry::make('http_method')
                            ->inlineLabel('Method')
                            ->badge(),
                        TextEntry::make('http_status_code')
                            ->inlineLabel('Status Code')
                            ->badge(),
                    ])
                        ->description('Request')
                        ->grow(false),
                ])->columnSpanFull(),

                Section::make()
                    ->description('Error details')
                    ->when(filled($record?->error_code), fn (Section $section) => $section->schema([
                        TextEntry::make('error_code')->inlineLabel('Error code')->badge(),
                        TextEntry::make('error_message')->inlineLabel('Error message')->badge(),
                        TextEntry::make('error_fields')->inlineLabel('Error fields')->badge(),
                    ]), default: fn (Section $section) => $section->schema([TextEntry::make('No errors have occurred')])),

                Section::make([
                    KeyValueEntry::make('payload')
                        ->hiddenLabel()
                        ->keyLabel('Property')
                        ->valueLabel('Value'),
                ])
                    ->description('Information about the payload that the process has.'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                IconColumn::make('successful')
                    ->boolean()
                    ->getStateUsing(fn (JetCourierDocumentRequestLog $record) => $record->http_status_code < 300),
                TextColumn::make('lead_id')->searchable()->forceSearchCaseInsensitive(),
                TextColumn::make('transaction_id')->searchable()->forceSearchCaseInsensitive(),
                TextColumn::make('entity')->badge(),
                TextColumn::make('error_message'),
            ])
            ->groups([
                Group::make('lead_id')
                    ->label('Lead')
                    ->getDescriptionFromRecordUsing(fn (JetCourierDocumentRequestLog $record): string => sprintf(
                        'Verblijfsvergunningen proces voor: %1$s %2$s <%3$s>',
                        $record->applicant()->value('first_name'),
                        $record->applicant()->value('last_name'),
                        $record->applicant()->value('email')),
                    ),
                Group::make('transaction_id'),
            ])
            ->defaultGroup('lead_id')
            ->filters([
                SelectFilter::make('entity')
                    ->options([
                        JetEntity::COURIER_DOCUMENT->value => JetEntity::COURIER_DOCUMENT->value,
                        JetEntity::CONTENT_VERSION->value => JetEntity::CONTENT_VERSION->value,
                        JetEntity::QUERY->value => JetEntity::QUERY->value,
                        JetEntity::CONTENT_DOCUMENT_LINK->value => JetEntity::CONTENT_DOCUMENT_LINK->value,
                    ]),
                Filter::make('http_status_code')
                    ->name('Heeft errors')
                    ->query(fn(Builder $query): Builder => $query->whereNotNull('error_code')),
            ])
            ->actions([
                Action::make('Filament')
                    ->button()
                    ->url(fn (JetCourierDocumentRequestLog $record) => route('filament.admin.resources.jet-applicants.view-check', ['record' => $record->applicant]))
                    ->openUrlInNewTab(),
                Action::make('Portal')
                    ->button()
                    ->url(fn (JetCourierDocumentRequestLog $record) => sprintf(
                        '%1$s/beheer/kandidaten.php?command=display_kandidaat&id=%2$d',
                        config('services.wenote.app_url'),
                        $record->applicant()->value('wenote_id')
                    ))
                    ->openUrlInNewTab(),
                ViewAction::make()->hiddenLabel(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }
}
