<?php

namespace App\Filament\Resources;

use App\Filament\Actions\NotifyJetAction;
use App\Filament\Resources\ResidencePermitResource\Pages;
use App\Filament\Resources\ResidencePermitResource\RelationManagers;
use App\Http\Controllers\EasyflexContractsController;
use App\Models\EasyflexContracts;
use App\Models\JetApplicant;
use App\Models\DatacheckerTransaction;
use App\Models\ResidencePermitRenewal;
use App\Models\TwvInformation;
use App\Services\CommunicationService;
use App\Services\EasyflexService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class ResidencePermitResource extends Resource
{
    protected static ?string $model = DatacheckerTransaction::class;

    protected static bool $shouldRegisterNavigation = false;
    protected static ?string $navigationLabel = 'Verblijfsvergunningen';
    protected static ?string $modelLabel = 'Verblijfsvergunningen';
    protected static ?string $pluralModelLabel = 'Verblijfsvergunningen';


    public static function getEloquentQuery(): Builder
    {
        $flexappIds = JetApplicant::select('flexapp_id')
            ->whereHas('enrollmentStatus', function(Builder $query) {
                $query->whereRaw('on_hold IS NOT true')
                    ->where('agency_status', 'Approved')
                    ->where('step_in_process', 10);
            })
            ->where(function ($query) {
                $query->whereDoesntHave('employeeOffboarding')
                    ->orWhereHas('employeeOffboarding', function ($subQuery) {
                        $subQuery->whereDate('offboarding_date', '>', now());
                    });
            })
            ->pluck('flexapp_id')->toArray();

        $datacheckerIds = DatacheckerTransaction::selectRaw('distinct on (user_id) id')
            ->from('transactions as dt')
            ->whereIn('user_id', $flexappIds)
            ->where('document_type', 'residencePermit')
            ->where('status', 20)
            ->orderBy('user_id')
            ->orderByDesc('created_at')
            ->pluck('id')
            ->toArray();

        $completedApprovals = ResidencePermitRenewal::where('renewal_completed', 1)->pluck('datachecker_transaction_id')->toArray();
        $datacheckerIds = array_diff($datacheckerIds, $completedApprovals);

        return parent::getEloquentQuery()
            ->whereIn('id', $datacheckerIds)
            ->whereHas('datacheckerTransactionResult', function ($query) {
                $query->where('status', 'APPROVED')
                    ->whereHas('datacheckerIdentityDocument', function ($subQuery) {
                        $subQuery->whereDate('date_of_expiry', '>=', Carbon::today());
                    });
            });
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns(components: [
                Tables\Columns\TextColumn::make('datacheckerTransactionResult.datacheckerIdentityDocument.full_name')
                    ->label('Full name')
                    ->url(fn ($record): string => route('filament.admin.resources.jet-applicant-news.view-check', ['record' => $record->jetApplicant->applicant_id]))
                    ->openUrlInNewTab()
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('datacheckerTransactionResult.datacheckerIdentityDocument.date_of_expiry')
                    ->label('Date of expiry')
                    ->getStateUsing(function ($record) {
                        $endDate = $record->datacheckerTransactionResult?->datacheckerIdentityDocument?->date_of_expiry;
                        return $endDate ? Carbon::createFromFormat('Y-m-d', $endDate)->format('d-m-Y') : null;
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('id')
                    ->label('Datachecker ID'),
                Tables\Columns\TextColumn::make('datacheckerTransactionResult.datacheckerIdentityDocument.result')
                    ->label('Status'),
                Tables\Columns\TextColumn::make('reminders_sent')
                    ->label('Reminders sent')
                    ->getStateUsing(function ($record) {
                        $reminderDates = $record->residencePermitRenewal?->reminder_dates ?? [];

                        if (empty($reminderDates)) {
                            return "No reminders sent";
                        }

                        $formattedReminders = collect($reminderDates)
                            ->map(function ($date) {
                                return \Carbon\Carbon::parse($date)->format('d-m-Y');
                            });

                        return $formattedReminders;
                    }),
                Tables\Columns\TextColumn::make('jetApplicant.flexappData.personalData.phone_number')
                    ->label('Tel'),
                Tables\Columns\IconColumn::make('residencePermitRenewal.twv_required')
                    ->label('TWV nodig?')
                    ->boolean()
                    ->getStateUsing(function ($record) {
                        return $record->residencePermitRenewal?->twv_required;
                    }),
                Tables\Columns\IconColumn::make('residencePermitRenewal.twvInformation.twv_requested_date')
                    ->label('TWV aangevraagd?')
                    ->boolean()
                    ->getStateUsing(function ($record) {
                        return !is_null($record->residencePermitRenewal?->twvInformation?->twv_requested_date);
                    }),
                Tables\Columns\IconColumn::make('residencePermitRenewal.twvInformation.twv_approved_date')
                    ->label('TWV toegekend?')
                    ->boolean()
                    ->getStateUsing(function ($record) {
                        return !is_null($record->residencePermitRenewal?->twvInformation?->twv_approved_date);
                    }),
                Tables\Columns\IconColumn::make('residencePermitRenewal.renewal_completed')
                    ->label('Afgerond')
                    ->boolean()
                    ->getStateUsing(function ($record) {
                        return (bool) $record->residencePermitRenewal?->renewal_completed;
                    }),
            ])
            ->defaultSort('datacheckerTransactionResult.datacheckerIdentityDocument.date_of_expiry', 'asc')
            ->defaultPaginationPageOption(25)
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('Stuur herinnering')
                    ->button()
                    ->icon('heroicon-m-bell')
                    ->color('danger')
                    ->action(function(CommunicationService $communicationService, $record) {
                        if (empty($record->residencePermitRenewal)) {
                            $residencePermit = new ResidencePermitRenewal();
                            $residencePermit->applicant_id = $record->jetApplicant->applicant_id;
                            $residencePermit->datachecker_transaction_id = $record->id;
                        } else {
                            $residencePermit = $record->residencePermitRenewal;
                            $reminders = $residencePermit->reminder_dates ?? [];

                            if (count($reminders) >= 4) {
                                return Notification::make()
                                    ->title('Er zijn al 4 herinneringen verstuurd')
                                    ->persistent()
                                    ->danger()
                                    ->send();
                            }
                        }

                        $data = [
                            'email' => $record->jetApplicant?->flexappData?->personalData?->email,
                            'name' => $record->jetApplicant?->flexappData?->personalData?->first_name.' '.$record->jetApplicant?->flexappData?->personalData?->last_name,
                            'expiration_date' => $record->datacheckerTransactionResult?->datacheckerIdentityDocument?->date_of_expiry
                        ];
                        $whatsappData = [
                            'language' => 'en',
                            'phone_number' => $record->jetApplicant?->flexappData?->personalData?->country_code.''.$record->jetApplicant?->flexappData?->personalData?->phone_number
                        ];
                        $communicationService->sendResidencePermitReminder($data, $whatsappData);

                        $reminders[] = now();
                        $residencePermit->reminder_dates = $reminders;
                        $residencePermit->save();
                    }),
                Tables\Actions\Action::make('approveResidencePermitRenewal')
                    ->label('Verlenging goedkeuren')
                    ->requiresConfirmation()
                    ->icon('heroicon-m-check')
                    ->button()
                    ->color('success')
                    ->form([
                        Forms\Components\Textarea::make('notes')
                            ->label('Notitie')
                            ->default(function ($record): string {
                                return $record->residencePermitRenewal?->notes ?? '';
                            }),
                    ])
                    ->hidden(fn ($record) => $record->jetApplicant?->flexappData?->info?->twv_required == true)
                    ->action(function($record) {
                        $residencePermit = $record->residencePermitRenewal ?? new ResidencePermitRenewal([
                            'applicant_id' => $record->jetApplicant->applicant_id,
                            'datachecker_transaction_id' => $record->id,
                        ]);

                        $residencePermit->notes = $data['notes'] ?? $residencePermit->notes;
                        $residencePermit->renewal_completed = true;
                        $residencePermit->save();
                    }),
                Tables\Actions\Action::make('updateTWVInfo')
                    ->label('TWV info')
                    ->requiresConfirmation()
                    ->icon('heroicon-m-pencil-square')
                    ->button()
                    ->color('warning')
                    ->hidden(fn ($record) => $record->jetApplicant?->flexappData?->info?->twv_required == false)
                    ->form([
                        Forms\Components\Checkbox::make('twv_required')
                            ->label('TWV nodig?')
                            ->default(function ($record): string {
                                return $record->residencePermitRenewal?->twv_required ?? false;
                            }),
                        Forms\Components\DatePicker::make('twv_requested_date')
                            ->label('TWV requested date')
                            ->default(function ($record): string {
                                return $record->residencePermitRenewal?->twvInformation?->twv_requested_date ?? '';
                            }),
                        Forms\Components\DatePicker::make('twv_approved_date')
                            ->label('TWV approved date')
                            ->default(function ($record): string {
                                return $record->residencePermitRenewal?->twvInformation?->twv_approved_date ?? '';
                            }),
                        Forms\Components\DatePicker::make('twv_start_date')
                            ->label('TWV start date')
                            ->default(function ($record): string {
                                return $record->residencePermitRenewal?->twvInformation?->twv_start_date ?? '';
                            }),
                        Forms\Components\DatePicker::make('twv_expiration_date')
                            ->label('TWV expiration date')
                            ->default(function ($record): string {
                                return $record->residencePermitRenewal?->twvInformation?->twv_expiration_date ?? '';
                            }),
                        Forms\Components\TextInput::make('twv_number')
                            ->label('TWV nummer')
                            ->default(function ($record): string {
                                return $record->residencePermitRenewal?->twvInformation?->twv_number ?? '';
                            }),
                        Forms\Components\TextInput::make('twv_issued_by')
                            ->label('TWV afgegeven door')
                            ->default(function ($record): string {
                                return $record->residencePermitRenewal?->twvInformation?->twv_issued_by ?? '';
                            }),
                        Forms\Components\Textarea::make('notes')
                            ->label('Notities')
                            ->default(function ($record): string {
                                return $record->residencePermitRenewal?->notes ?? '';
                            }),

                        Forms\Components\FileUpload::make('twv_file')
                            ->label('TWV file')
                            ->disk('identification')
                            ->acceptedFileTypes(['application/pdf'])
                            ->getUploadedFileNameForStorageUsing(
                                fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                    ->prepend('twv_'.Carbon::now()->timestamp.'_'),
                            )
                            ->directory('twv')
                            ->hidden(fn ($record) => $record->residencePermitRenewal?->twvInformation?->file_name ? true : false)
                            ->label('TWV bestand'),

                        Forms\Components\Checkbox::make('renewal_completed')
                            ->label('Is de verlenging van de verblijfsvergunning afgerond? (Alleen aanvinken als gegevens in EF staan)')
                            ->default(function ($record): string {
                                return $record->residencePermitRenewal?->renewal_completed ?? false;
                            }),
                    ])
                    ->action(function (array $data, DatacheckerTransaction $record, EasyflexService $easyflexService, CommunicationService $communicationService) {
                            $residencePermit = $record->residencePermitRenewal ?? new ResidencePermitRenewal([
                                'applicant_id' => $record->jetApplicant->applicant_id,
                                'datachecker_transaction_id' => $record->id,
                            ]);

                            $residencePermit->notes = $data['notes'] ?? $residencePermit->notes;
                            $residencePermit->renewal_completed = !empty($data['renewal_completed']);
                            $residencePermit->twv_required = $data['twv_required'] ?? false;

                            $twvInformation = TwvInformation::where('applicant_id', $record->jetApplicant->applicant_id)
                                ->orderby('created_at', 'desc')
                                ->firstOrNew(['applicant_id' => $record->jetApplicant->applicant_id]);

                            $twvRequestDateOriginalValue = $twvInformation->twv_requested_date;

                            $twvInformation->twv_requested_date = $data['twv_requested_date'] ?? null;
                            $twvInformation->save();

                            if ($twvInformation->wasChanged('twv_requested_date') && $twvRequestDateOriginalValue === null) {
                                $whatsappData = [
                                    'name' => $record->jetApplicant->flexappData?->personalData?->first_name ?? $record->jetApplicant->first_name,
                                    'phone_number' => $record->jetApplicant->flexappData?->personalData?->phone_number,
                                    'language' => ($record->flexappData?->applicationProfile?->language === 'Engels') ? 'en' : 'nl'
                                ];
                                $communicationService->sendWorkPermitRequest($whatsappData);
                            }

                            $residencePermit->twv_information_id = $twvInformation->id;
                            $residencePermit->save();

                            return $easyflexService->storeTwvFile($record->jetApplicant, $data);
                    }),
                    NotifyJetAction::makeTable()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
//                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public function informJetForm()
    {
        return [
            Forms\Components\Textarea::make('notes')
                ->label('Notitie')
                ->default(function ($record): string {
                    return $record->residencePermitRenewal?->notes ?? '';
                }),
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListResidencePermits::route('/'),
//            'create' => Pages\CreateResidencePermit::route('/create'),
//            'edit' => Pages\EditResidencePermit::route('/{record}/edit'),
        ];
    }
}
