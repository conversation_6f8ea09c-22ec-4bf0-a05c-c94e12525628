<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ContractRenewalResource\Pages;
use App\Filament\Resources\ContractRenewalResource\RelationManagers;
use App\Http\Controllers\ContractController;
use App\Http\Controllers\EasyflexContractsController;
use App\Models\EasyflexContracts;
use App\Models\JetApplicant;
use App\Models\JetApplicantRenewalStatus;
use Filament\Forms;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Carbon;
use Illuminate\Support\HtmlString;

class ContractRenewalResource extends Resource
{
    protected static ?string $model = EasyflexContracts::class;
    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'Contract verlengingen';
    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('easyflex_registratienummer')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('scoober_id')
                    ->label('Scoober ID')
                    ->getStateUsing(function (EasyflexContracts $record) {
                        if($record->JetApplicant?->scoober_id){
                            return $record->JetApplicant?->scoober_id;
                        }
                    }),
                Tables\Columns\TextColumn::make('fw_arbeidscontract_contract')
                    ->sortable(),
                Tables\Columns\TextColumn::make('JetApplicant.name')
                    ->getStateUsing(function ($record) {
                        if($record->JetApplicant?->flexapp_id && isset($record->JetApplicant?->flexappData?->personalData)) {
                            return $record->JetApplicant->flexappData->personalData->first_name . ' ' . $record->JetApplicant->flexappData->personalData->last_name;
                        }
                        return $record->JetApplicant?->first_name . ' ' . $record->JetApplicant?->last_name;
                    }),
                Tables\Columns\TextColumn::make('fw_arbeidscontract_contracteinde')
                    ->label('Einde contract')
                    ->getStateUsing(function (EasyflexContracts $record) {
                        $endDate = $record->fw_arbeidscontract_contracteinde;
                        return $endDate ? Carbon::createFromFormat('Y-m-d', $endDate)->format('d-m-Y') : null;
                    })
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_arbeidscontract_contracturen_week')
                    ->label('Contracturen')
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_arbeidscontract_contractvolgnummer')
                    ->label('Volgnummer')
                    ->sortable(),
                Tables\Columns\TextColumn::make('fw_arbeidscontract_contractuurloon')
                    ->label('Uurloon')
                    ->getStateUsing(function (EasyflexContracts $record) {
                        $formattedValue = number_format($record->fw_arbeidscontract_contractuurloon, 2, ',', '.');
                        return '€' . $formattedValue;
                    }),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\Action::make('renew_contract')
                    ->label('Verleng contract')
                    ->icon('heroicon-s-document-text')
                    ->color('success')
                    ->requiresConfirmation()
                    ->modalHeading('Contract verlengen')
                    ->hidden(fn($record) => $record->JetApplicant?->renewalStatus?->step_in_process >= 1)
                    ->action(function(EasyflexContractsController $easyflexContractsController, $record) {
                        $easyflexContractsController->renewContract($record);
                    }),
                Tables\Actions\Action::make('terminate_contract')
                    ->label('Beëindig contract')
                    ->icon('heroicon-s-trash')
                    ->color('danger')
                    ->requiresConfirmation()
                    ->form([
                        Checkbox::make('skip_email')
                            ->label('Geen email versturen')
                    ])
                    ->modalHeading('Contract beëindigen')
                    ->hidden(fn($record) => $record->JetApplicant?->renewalStatus?->step_in_process >= 1)
                    ->action(function(EasyflexContractsController $easyflexContractsController, $record, $data) {
                        $easyflexContractsController->terminateContract($record, $data['skip_email']);
                    })
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    private function verlengForm(): array
    {
        return [
            Placeholder::make('')
                ->content(new HtmlString('<div class="mb-4 px-4 py-4 bg-danger-100 border-l-4 border-danger-500"><strong>Let op!:</strong> De Applicant had aangegeven meer dan 3 contracten te hebben gekregen. En heeft dus recht op een onbepaalde tijd contract.</div>'))
                ->hidden(fn ($record) => ($record->flexappData?->successiveEmployership?->number_of_contracts <= 2))
                ->hiddenLabel()
            ,

            Select::make('contract_type')
                ->label('Contract type')
                ->options([
                    'contract' => 'Eerste contract',
                    'contract-wijziging' => 'Contract wijziging',
                    'obt_contract' => 'Onbepaalde tijd contract'
                ])
                ->default(function ($record): string {
                    if ($record->flexappData?->successiveEmployership?->number_of_contracts >= 3) {
                        return 'obt_contract';
                    }

                    return 'contract';
                })
                ->required()
        ];
    }

    protected function getTableActions(): array
    {
        return [
            // ...
        ];
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContractRenewals::route('/'),
            'create' => Pages\CreateContractRenewal::route('/create'),
            'edit' => Pages\EditContractRenewal::route('/{record}/edit'),
        ];
    }
}
