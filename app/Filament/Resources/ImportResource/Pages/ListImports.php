<?php

namespace App\Filament\Resources\ImportResource\Pages;

use App\Filament\Imports\CSVImporter;
use App\Filament\Resources\ImportResource;
use App\Jobs\ExtendImportJob;
use Filament\Actions\ImportAction;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ListImports extends ListRecords
{
    protected static string $resource = ImportResource::class;

    protected static ?string $title = 'Loon component imports';

    protected function getHeaderActions(): array
    {
        return [
            ImportAction::make()
                ->label('Import CSV')
                ->importer(CSVImporter::class)
                ->chunkSize(150)
        ];
    }

    public function getTabs(): array
    {
        $tabs = [
            'alle' => Tab::make()->badge($this->getModel()::count()),
            'exported' => Tab::make()
                ->modifyQueryUsing(function (Builder $query) {
                    $query->whereExists(function ($subQuery) {
                        $subQuery->select(DB::raw(1))
                            ->from('exports')
                            ->where('file_name', 'NOT LIKE', 'plaatsingen/plaatsingen%')
                            ->whereRaw('exports.import_id @> to_jsonb(imports.id)');
                    });
                })
                ->badge(function () {
                    $exportedCount = DB::table('imports')
                        ->whereExists(function ($subQuery) {
                            $subQuery->select(DB::raw(1))
                                ->from('exports')
                                ->where('file_name', 'NOT LIKE', 'plaatsingen/plaatsingen%')
                                ->whereRaw('exports.import_id @> to_jsonb(imports.id)');
                        })
                        ->count();
                    return $exportedCount;
                }),
            'not_exported' => Tab::make()
                ->modifyQueryUsing(function (Builder $query) {
                    $query->whereNotExists(function ($subQuery) {
                        $subQuery->select(DB::raw(1))
                            ->from('exports')
                            ->where('file_name', 'NOT LIKE', 'plaatsingen/plaatsingen%')
                            ->whereRaw('exports.import_id @> to_jsonb(imports.id)');
                    });
                })
                ->badge(function () {
                    $totalCount = DB::table('imports')->count();
                    $exportedCount = DB::table('exports')
                        ->where('file_name', 'NOT LIKE', 'plaatsingen/plaatsingen%')
                        ->distinct()
                        ->count('import_id');
                    return $totalCount - $exportedCount;
                }),
        ];
        return $tabs;
    }

    public function getTableQuery(): Builder
    {
        $query = ImportResource::getEloquentQuery()->getModel()->newQuery();

        return $query->where('importer', CSVImporter::class);
    }
}
