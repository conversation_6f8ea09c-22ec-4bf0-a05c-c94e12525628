<?php

namespace App\Filament\Resources\ImportResource\Pages;

use App\Filament\Resources\ImportResource;
use App\Models\Flexapp\User;
use App\Models\Urenbestand;
use Carbon\Carbon;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\Concerns\InteractsWithRecord;
use Filament\Resources\Pages\Page;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Spatie\SimpleExcel\SimpleExcelWriter;
use Filament\Actions\Action;


class Arbeidstijdenwet extends Page
{
    use InteractsWithRecord;

    protected static string $resource = ImportResource::class;

    protected static string $view = 'filament.resources.import-resource.pages.arbeidstijdenwet';

    public $workedHours;
    public $underageData;
    public $regularData;
    public $studentData;
    public $weeknr;

    public function mount(int | string $record): void
    {
        $this->record = $this->resolveRecord($record);
        $this->weeknr = $this->getWeekNrById($this->record->id);
        $this->workedHours = $this->getAllWorkedHours();
        $this->regularData = $this->getRegularApplicantsInformation($this->workedHours['regular']);
        $this->studentData = $this->getStudentData($this->workedHours['student']);
        $this->underageData = $this->getUnderageApplicantsData($this->workedHours['underage']);
    }

    public function getRecord()
    {
        return $this->record;
    }

    public function getWorkedHours()
    {
        return $this->workedHours;
    }

    public function getWeekNr()
    {
        return $this->weeknr;
    }

    public function getHours()
    {
        $query = Urenbestand::where('driver_id', $this->applicant->scoober_id)
            ->where('absence', false);

        if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $this->date))
        {
            // If it's a date (YYYY-MM-DD), filter by date
            $query->where('date', $this->date);
        } elseif (preg_match('/^\d{6}$/', $this->date))
        {
            // If it's a week number (YYYYWW), filter by weeknr
            $query->where('weeknr', $this->date);
        }

        return $query->get();
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('title'),
                TextColumn::make('slug'),
                IconColumn::make('is_featured')
                    ->boolean(),
            ]);
    }

    public function getWeekNrById($import_id)
    {
        return Urenbestand::where('import_id', $import_id)->selectRaw('weeknr')->first()?->weeknr;
    }

    public function getAllWorkedHours()
    {
        $data = Urenbestand::where('weeknr', $this->weeknr)
            ->where('urenbestand.absence', false)
            ->join('applicants', 'applicants.scoober_id', '=', 'urenbestand.driver_id')
            ->groupBy('urenbestand.driver_id', 'applicants.flexapp_id', 'applicants.applicant_id', 'urenbestand.weeknr', 'applicants.easyflex_id')
            ->selectRaw('urenbestand.driver_id, applicants.flexapp_id, applicants.applicant_id, SUM(urenbestand.hours) as total_hours, weeknr, applicants.easyflex_id',)
            ->get();

        $userIds = $data->pluck('flexapp_id')->unique();
        $users = User::whereIn('uuid', $userIds)->with('personalData', 'info')->get()->keyBy('uuid');

        $underageData = [];
        $eighteenPlus = [];
        $workStudent = [];

        foreach ($data as $user)
        {
            $userInfo = $users[$user->flexapp_id] ?? null;
            if (!$userInfo || !$userInfo->personalData || !$userInfo->personalData->date_of_birth)
            {
                continue;
            }

            $dob = Carbon::parse($userInfo->personalData->date_of_birth);
            $age = $dob->age;
            $student = (bool)$userInfo->info->twv_is_student ?? false;

            if ($age < 18)
            {
                $underageData[] = $user;
            } elseif ($age >= 18 && !$student)
            {
                $eighteenPlus[] = $user;
            } else
            {
                $workStudent[] = $user;
            }
        }

        return [
            'student' => $workStudent,
            'regular' => $eighteenPlus,
            'underage' => $underageData
        ];
    }

    public function getRegularApplicantsInformation($users, $weeknr = null)
    {
        $userIds = collect($users)->pluck('driver_id')->unique();
        if(is_null($weeknr)) {
            $weeknr = $this->weeknr;
        }

        $individualUserHours = Urenbestand::join('applicants', 'urenbestand.driver_id', '=', 'applicants.scoober_id')
            ->whereIn('urenbestand.driver_id', $userIds)
            ->where('absence', false)
            ->where('urenbestand.weeknr', $weeknr)
            ->groupBy('urenbestand.date', 'urenbestand.driver_id', 'applicants.applicant_id', 'applicants.easyflex_id')
            ->selectRaw('urenbestand.date, urenbestand.driver_id, applicants.applicant_id, SUM(urenbestand.hours) as total_hours, applicants.easyflex_id')
            ->havingRaw('SUM(urenbestand.hours) >= 12')
            ->orderByDesc('total_hours')
            ->get();

        $tooManyWeekly = collect($users)->filter(fn($user) => $user['total_hours'] >= 48)->toArray();

        return [
            'daily' => $individualUserHours->toArray(),
            'weekly' => $tooManyWeekly
        ];
    }

    public function getUnderageApplicantsData($users, $weeknr = null)
    {
        $userIds = collect($users)->pluck('driver_id')->unique();
        if(is_null($weeknr)) {
            $weeknr = $this->weeknr;
        }

        $individualUserHours = Urenbestand::join('applicants', 'urenbestand.driver_id', '=', 'applicants.scoober_id')
            ->whereIn('urenbestand.driver_id', $userIds)
            ->where('absence', false)
            ->where('urenbestand.weeknr', $weeknr)
            ->groupBy('urenbestand.date', 'urenbestand.driver_id', 'applicants.applicant_id', 'applicants.easyflex_id')
            ->selectRaw('urenbestand.date, urenbestand.driver_id, applicants.applicant_id, SUM(urenbestand.hours) as total_hours, applicants.easyflex_id')
            ->havingRaw('SUM(urenbestand.hours) >= 9')
            ->orderByDesc('total_hours')
            ->get();


        $tooManyWeekly = collect($users)->filter(fn($user) => $user['total_hours'] >= 40)->toArray();

        return [
            'daily' => $individualUserHours->toArray(),
            'weekly' => $tooManyWeekly
        ];
    }

    public function getStudentData($users)
    {
        $tooManyWeekly = collect($users)->filter(fn($user) => $user['total_hours'] >= 16)->toArray();

        return [
            'weekly' => $tooManyWeekly
        ];
    }
}
