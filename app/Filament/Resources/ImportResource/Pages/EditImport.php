<?php

namespace App\Filament\Resources\ImportResource\Pages;

use App\Actions\CsvActions;
use App\Filament\Resources\ImportResource;
use App\Http\Controllers\ExportController;
use App\Http\Controllers\PlaatsingController;
use App\Jobs\ExportJob;
use App\Jobs\StorePlaatsingenJob;
use App\Models\Export;
use App\Models\ImportExtended;
use App\Models\LoonComponent;
use App\Models\Urenbestand;
use App\Models\User;
use App\Services\EasyflexService;
use Carbon\Carbon;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Actions\Imports\Models\Import;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class EditImport extends ViewRecord
{
    protected static string $resource = ImportResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('Maak plaatsingen')
                ->label('1. Maak plaatsingen')
                ->icon('heroicon-m-document-check')
                ->color('warning')
                ->requiresConfirmation()
                ->action(function (ExportController $export, $record) {
                    $import_id = $record->id;
                    $filename = 'plaatsingen/plaatsingen_export_bestand_'.Carbon::now()->timestamp.'.csv';
                    ExportJob::dispatch($import_id, 99, $filename, auth()->user()->id);
                    return Notification::make()
                        ->title('Plaatsingen worden aangemaakt')
                        ->persistent()
                        ->success()
                        ->send();
//                    $export->createExport($import_id, 99, $filename, auth()->user()->id);
                }),

            Action::make('Save plaatsingen')
                ->label('2. Opslaan')
                ->icon('heroicon-m-arrow-down-on-square')
                ->color('warning')
                ->requiresConfirmation()
                ->action(function (ExportController $export, $record) {
                    $import_id = $record->id;
                    StorePlaatsingenJob::dispatch($import_id);
                    return Notification::make()
                        ->title('Plaatsingen worden opgeslagen')
                        ->persistent()
                        ->success()
                        ->send();
                }),

            Action::make('Export CSV')
                ->label('3. Export CSV')
                ->icon('heroicon-m-arrow-up-on-square')
                ->color('success')
                ->requiresConfirmation()
                ->disabled(function ($record) {
                    $export = Export::where(function ($query) use ($record) {
                        $query->where('import_id', $record->id)
                            ->orWhereJsonContains('import_id', $record->id);
                    })->where('file_name', 'NOT LIKE', '%plaatsingen%')
                        ->first();

                    if($export){
                        return true;
                    }
                    return false;
                })
                ->action(function (CsvActions $csvActions, $record) {
                    return $csvActions->exportAction($record);
                }),

            Action::make('Download gegevens')
                ->icon('heroicon-m-arrow-down-circle')
                ->color('gray')
                ->requiresConfirmation()
                ->action(function (ExportController $export, $record) {
                    $file = $export->downloadData($record);
                    return response()->download($file);
                }),

            Action::make('download_failed_rows_csv')
                ->label(fn ($record): string => "Mislukte rijen downloaden")
                ->url(fn ($record): string => route('filament.imports.failed-rows.download', ['import' => $record->id],  absolute: false))
                ->icon('heroicon-m-arrow-down-tray')
                ->color('info')
                ->disabled(function ($record): bool {
                    //FIXME: $record is already an Import, but the relation assumes the matching column is "import_extended_id" instead of "import_id"
                    $import = Import::where('id', $record->id)->first();
                    return ($import?->getFailedRowsCount() <= 0 || !$import?->failedRows()->first());
                }),

            DeleteAction::make()
                ->icon('heroicon-c-x-circle')
                ->requiresConfirmation()
                ->modalDescription('Weet je zeker dat je deze import wilt verwijderen? Je verwijdert hiermee ook de bijbehorende uren.')
                ->modalHeading('Delete import')
                ->modalSubmitActionLabel('Verwijder import')
                ->label('Verwijder import')
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make()
                    ->schema([
                        Infolists\Components\Fieldset::make('Import')
                            ->columns(1)
                            ->schema([
                                Infolists\Components\ViewEntry::make('file_name')
                                    ->view('filament.components.view-field'),

                                Infolists\Components\ViewEntry::make('total_rows')
                                    ->view('filament.components.view-field'),

                                Infolists\Components\ViewEntry::make('Looncomponent Type')
                                    ->view('filament.components.view-field')
                                    ->getStateUsing(function (ImportExtended $record): string {
                                        if($record->loon_component_id){
                                            $looncomponent = LoonComponent::find($record->loon_component_id);
                                            return $looncomponent->name;
                                        }
                                        return 'Geen urenbestand regels gevonden';
                                    }),

                                Infolists\Components\ViewEntry::make('total_hours')
                                    ->view('filament.components.view-field')
                                    ->getStateUsing(function (ImportExtended $record): string {
                                        $test = $record->urenbestand;
                                        $totalSum = Urenbestand::where('import_id', $record->id)->sum('hours');
                                        return $totalSum;
                                    }),


                                Infolists\Components\ViewEntry::make('Export status')
                                    ->view('filament.components.view-field')
                                    ->getStateUsing(function (Import $record, EasyflexService $easyflexService) {
                                        $export = Export::where(function ($query) use ($record) {
                                            $query->where('import_id', $record->id)
                                                ->orWhereJsonContains('import_id', $record->id);
                                        })->where('file_name', 'NOT LIKE', '%plaatsingen%')
                                            ->first();

                                        if(!$export){
                                            return 'Not exported yet';
                                        }

                                        $status = $easyflexService->getDocumentStatus($export->file_nr);
                                        $color = 'warning';

                                        if($status === 'Verwerkt') {
                                            $color = 'success';
                                        } else if($status === 'Verwerkt (warning)' || $status === 'Fout') {
                                            $color = 'danger';
                                        } else if($status === 'Unknown status') {
                                            $color = 'gray';
                                        }

                                        return [
                                            'type' => 'badge',
                                            'color' => $color,
                                            'value' => $status
                                        ];
                                    }),

                                Infolists\Components\ViewEntry::make('Alerts')
                                    ->view('filament.components.view-field')
                                    ->getStateUsing(function (Import $record, ExportController $export) {
                                        $total = $export->countApplicantsWithoutPlaatsing($record->id);
                                        $color = 'warning';

                                        if($total['error'] === 'Missing easyflex_ids') {
                                            $color = 'danger';
                                        } else if($total['error'] === 'All applicants have a plaatsing') {
                                            $color = 'success';
                                        }

                                        $tooltip = '';
                                        if($total['info']){
                                            $tooltip = 'UserIDs: '.implode(' ,',$total['info']);
                                        }

                                        return [
                                            'type' => 'badge',
                                            'tooltip' => $tooltip,
                                            'color' => $color,
                                            'value' => $total['error']
                                        ];
                                    }),

                                Infolists\Components\ViewEntry::make('user_id')
                                    ->label('User')
                                    ->view('filament.components.view-field')
                                    ->getStateUsing(function (Import $record): string {
                                        $user = $record->connectedUser;
                                        return $user->name;
                                    }),

                                Infolists\Components\ViewEntry::make('created_at')
                                    ->view('filament.components.view-field')
                                    ->getStateUsing(function (Import $record): string {
                                        return Carbon::make($record->created_at)->format('d-m-Y H:i:s');
                                    }),

                                Infolists\Components\ViewEntry::make('completed_at')
                                    ->view('filament.components.view-field')
                                    ->getStateUsing(function (Import $record): string {
                                        if($record->completed_at){
                                            return Carbon::createFromTimestamp($record->completed_at)->format('d-m-Y H:i:s');
                                        }
                                        return 'Not completed yet';
                                    }),
                                Infolists\Components\Actions::make([
                                    Infolists\Components\Actions\Action::make('test')
                                        ->label('Download nieuwste export')
                                        ->action(function ($record) {
                                            $import_id = $record->id;

                                            $export = Export::where(function ($query) use ($import_id) {
                                                $query->where(function ($query) use ($import_id) {
                                                    $query->where('import_id', $import_id)
                                                        ->orWhereJsonContains('import_id', $import_id);
                                                })->where('file_name', 'NOT LIKE', 'plaatsingen/plaatsingen%');
                                            })->first();
                                            if($export){
                                                $filename = $export->file_name;
                                                if (Storage::disk('jet')->exists($filename)) {
                                                    return Storage::disk('jet')->download($filename);
                                                }
                                            }
                                            return Notification::make()
                                                ->title('Geen export gevonden')
                                                ->danger()
                                                ->send();
                                        }),
                                    Infolists\Components\Actions\Action::make('arbeidstijdenwet')
                                        ->color('success')
                                        ->label('Bekijk arbeidstijdenwet')
                                        ->url(fn (): string => route('filament.admin.resources.imports.arbeidstijdenwet', ['record' => $this->record->id]))
                                ])
                            ])
                    ])
            ]);
    }
}
