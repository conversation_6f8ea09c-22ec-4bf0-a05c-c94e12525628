<?php

namespace App\Filament\Resources\ImportResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Actions\Action;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\BulkActionGroup;
use Filament\Tables\Actions\DeleteAction;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UrenbestandRelationManager extends RelationManager
{
    protected static string $relationship = 'urenbestand';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('import_id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public function isReadOnly(): bool
    {
        return false;
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('import_id')
            ->columns([
                Tables\Columns\TextColumn::make('driver_id')
                    ->label('Scoober ID')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('date')
                    ->date('d-m-Y')
                    ->sortable(),
                Tables\Columns\TextColumn::make('hours')
                    ->default('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->default('-'),
                Tables\Columns\TextColumn::make('city')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('from')
                    ->time('H:i')
                    ->label('Start time')
                    ->sortable(),
                Tables\Columns\TextColumn::make('to')
                    ->label('End time')
                    ->time('H:i')
                    ->sortable(),
                Tables\Columns\TextColumn::make('reason')
                    ->searchable()
                    ->default('-')
                    ->sortable(),
                Tables\Columns\TextColumn::make('sub_reason')
                    ->default('-')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\IconColumn::make('absence')
                    ->boolean()
                    ->sortable(),
            ])
            ->defaultSort('hours', 'desc')
            ->defaultPaginationPageOption(25)
            ->actions([
                DeleteAction::make(),
            ]);
    }
}
