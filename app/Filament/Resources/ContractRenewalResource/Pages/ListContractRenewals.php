<?php

namespace App\Filament\Resources\ContractRenewalResource\Pages;

use App\Filament\Resources\ContractRenewalResource;
use App\Filament\Resources\JetApplicantResource;
use App\Models\EasyflexContracts;
use Carbon\Carbon;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ListContractRenewals extends ListRecords
{
    protected static string $resource = ContractRenewalResource::class;

    protected static ?string $title = 'Contract verlengingen';

    protected function getHeaderActions(): array
    {
        return [
//            Actions\CreateAction::make(),
        ];
    }

    public function getTableQuery(): Builder
    {
        $query = ContractRenewalResource::getEloquentQuery()->getModel()->newQuery();
        $eightWeeksFromNow = Carbon::now()->addWeeks(8);

        // get all indefinite contracts
        $excludeIndefiniteContracts = DB::connection('easyflex')->table('ds_fw_arbeidscontract')
            ->where('WKM_id', 7)
            ->where('fw_arbeidscontract_contractsoort', 22612)
            // ->orWhereNotNull('fw_arbeidscontract_contracteindereden')
            ->pluck('easyflex_registratienummer');

        // get all applicants that are not in the renewal process
        $includedJetApplicants = DB::table('applicants')
            ->leftJoin('applicant_renewal_status', 'applicants.applicant_id', '=', 'applicant_renewal_status.applicant_id')
            ->whereNotNull('applicants.easyflex_id') // Ensure easyflex_id is not null
            ->whereNotNull('applicants.scoober_id') // Ensure scoober_id is not null
            ->where(function ($query) {
                $query->whereNull('applicant_renewal_status.applicant_id') // No matching row in applicant_renewal_status
                ->orWhere('applicant_renewal_status.step_in_process', 0); // OR step_in_process = 0
            })
            ->pluck('applicants.easyflex_id');

        $subquery = DB::connection('easyflex')->table('ds_fw_arbeidscontract as sub_ec')
            ->selectRaw('MAX(sub_ec.fw_arbeidscontract_contractvolgnummer) AS max_contractvolgnummer')
            ->whereColumn('sub_ec.easyflex_registratienummer', 'ds_fw_arbeidscontract.easyflex_registratienummer');

        return $query
            ->where(function ($query) use ($subquery) {
                $query->where('fw_arbeidscontract_contractvolgnummer', $subquery)
                    ->orWhereNull('fw_arbeidscontract_contractvolgnummer');
            })
            ->whereNotIn('easyflex_registratienummer', $excludeIndefiniteContracts)
            ->whereIn('easyflex_registratienummer', $includedJetApplicants)
            ->whereBetween('fw_arbeidscontract_contracteinde', [Carbon::now()->subDays(7), $eightWeeksFromNow])
            ->where('WKM_id', 7)
            ->orderBy('fw_arbeidscontract_contracteinde');
    }

    private function getExpireContracts()
    {
        $query = ContractRenewalResource::getEloquentQuery()->getModel()->newQuery();

        // get all applicants that are not in the renewal process
        $includedJetApplicants = DB::table('applicants')
            ->leftJoin('applicant_renewal_status', 'applicants.applicant_id', '=', 'applicant_renewal_status.applicant_id')
            ->where('applicant_renewal_status.step_in_process', 0)
            ->whereNotNull('applicant_renewal_status.notes')
            ->pluck('applicants.easyflex_id');

        $subquery = DB::connection('easyflex')->table('ds_fw_arbeidscontract as sub_ec')
            ->selectRaw('MAX(sub_ec.fw_arbeidscontract_contractvolgnummer) AS max_contractvolgnummer')
            ->whereColumn('sub_ec.easyflex_registratienummer', 'ds_fw_arbeidscontract.easyflex_registratienummer');

        return $query
            ->where(function ($query) use ($subquery) {
                $query->where('fw_arbeidscontract_contractvolgnummer', $subquery)
                    ->orWhereNull('fw_arbeidscontract_contractvolgnummer');
            })
            ->whereIn('easyflex_registratienummer', $includedJetApplicants)
            ->where('WKM_id', 7)
            ->orderBy('fw_arbeidscontract_contracteinde');
    }

    public function getTabs(): array
    {
        $defaultQuery = $this->getTableQuery()->clone();
        $expireContracts = $this->getExpireContracts()->clone();

        return [
            'Alle' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $defaultQuery)
                ->badge($defaultQuery->count())
            ,
            'Verlopen contracten' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $expireContracts)
                ->badge($expireContracts->count())
            ,
        ];
    }
}
