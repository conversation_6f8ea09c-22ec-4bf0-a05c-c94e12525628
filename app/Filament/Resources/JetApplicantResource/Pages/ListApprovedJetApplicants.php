<?php

namespace App\Filament\Resources\JetApplicantResource\Pages;

use App\Filament\Resources\JetApplicantResource;
use Filament\Forms\Components\Select;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class ListApprovedJetApplicants extends ListRecords
{
    protected static string $resource = JetApplicantResource::class;

    protected static ?string $title = 'Approved applicants';

    public function getTableQuery(): Builder
    {
        $query = JetApplicantResource::getEloquentQuery()->getModel()->newQuery();

        return $query
            ->whereRaw('is_test IS NOT true')
            ->whereHas('enrollmentStatus', function(Builder $query) {
                return $query
                    ->whereRaw('on_hold IS NOT true')
                    ->where([
                        ['agency_status', '=', 'Approved'],
                        ['step_in_process', '=', 10]
                    ])
                ;
            })
        ;
    }


    public function table(Table $table): Table
    {
        $columns = parent::table($table)->getColumns();
        $filters = parent::table($table)->getFilters();
        return $table
            ->columns([...$columns,
            TextColumn::make('renewalStatus.step_in_process')
                ->label('Renewal Status')
                ->state(function (Model $record) {
                    return JetApplicantResource::$renewStatusses[$record->renewalStatus->step_in_process ?? 0]['label'];
                })
                ->badge()
            ->color(function(Model $record) {
                return JetApplicantResource::$renewStatusses[$record->renewalStatus->step_in_process ?? 0]['color'];
            })
            ->sortable()
            ])
            ->filters([...$filters,
                Filter::make('renewalStatus')
                    ->form([
                        Select::make('renewal_step_in_process')
                            ->label('Verlenging status')
                            ->options(collect(JetApplicantResource::$renewStatusses)
                                ->mapWithKeys(fn($status, $key) => [$key => $status['label']])
                                ->toArray()
                            ),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (isset($data['renewal_step_in_process']) || $data['renewal_step_in_process'] === '0') {
                            return $query->whereRelation('renewalStatus', 'step_in_process', '=', $data['renewal_step_in_process']);
                        }
                        return $query;
                    })
                    ->indicator('Status')
                    ->indicateUsing(function (array $data) {
                        if (isset($data['renewal_step_in_process']) || $data['renewal_step_in_process'] === '0') {
                            return JetApplicantResource::$renewStatusses[$data['renewal_step_in_process']]['label'] ?? 'Unknown';
                        }
                        return null;
                    })
            ]);
    }

    public function getTabs(): array
    {
        $approvedApplicantsQuery = $this->getTableQuery()->clone()->whereRaw('is_import IS NOT true');
        $approvedApplicantsRandstadQuery = $this->getTableQuery()->clone()->where('is_import', true);
        $approvedApplicantsVerlengingQuery = $this->getTableQuery()->clone()->whereHas('renewalStatus')->whereDoesntHave('employeeOffboarding');

        return [
            'Alle' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $approvedApplicantsQuery)
                ->badge($approvedApplicantsQuery->count())
            ,
            'Randstad' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $approvedApplicantsRandstadQuery)
                ->badge($approvedApplicantsRandstadQuery->count())
            ,
            '365 Verlenging' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $approvedApplicantsVerlengingQuery)
                ->badge($approvedApplicantsVerlengingQuery->count())
            ,
        ];
    }
}
