<?php

namespace App\Filament\Resources\JetApplicantResource\Pages;

use App\Actions\JetActions;
use App\Actions\JetApplicantEnrollmentActions;
use App\Actions\UserActions;
use App\Http\Controllers\EasyflexContractsController;
use App\Http\Controllers\EmployeeOffboardingController;
use App\Models\EmployeeOffboarding;
use App\Models\Plaatsing;
use App\Models\TwvInformation;
use App\Models\WeeklyHours;
use App\Services\CommunicationService;
use App\Services\DataCheckerService;
use App\Services\SignhostService;
use App\Services\UserService;
use App\Filament\Resources\JetApplicantResource;
use App\Http\Controllers\ContractController;
use App\Models\JetApplicant;
use App\Models\LooncomponentPercentage;
use App\Services\EasyflexService;
use App\Services\FusionauthService;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Filament\Actions\Action;
use Filament\Actions\Imports\Models\Import;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\HtmlString;
use Livewire\Features\SupportFileUploads\TemporaryUploadedFile;

class ViewCheckJetApplicants extends ViewRecord
{
    public static string $resource = JetApplicantResource::class;

    protected static string $recordTitleAttribute = 'name';

    private array $documentTypes = [
        'I' => 'Identiteitskaart',
        'P' => 'Paspoort',
        'RP' => 'Verblijfsvergunning'
    ];

    private $verifaiScanData = null;

    public function getTitle(): string
    {
        $record = $this->getRecord();

        if($record->flexapp_id) {
            return $record->flexappData?->personalData?->first_name . ' ' . $record->flexappData?->personalData?->last_name;
        }

        return $record->first_name . ' ' . $record->last_name;
    }

    public function getHeaderActions(): array
    {
        return [
            Action::make('wenote')
                ->label('Open in Wenote')
                ->url(fn ($record) => config('services.wenote.app_url') . '/beheer/kandidaten.php?command=display_kandidaat&id=' .$record->wenote_id, shouldOpenInNewTab: true)
                ->hidden(fn ($record) => !$record->wenote_id),

            Action::make('add legitimatie')
                ->label('Legitimatie toevoegen')
                ->iconPosition('after')
                ->requiresConfirmation()
                ->form([
                    TextInput::make('transaction_id')
                        ->label('Transactie ID')
                ])
                ->action(function (array $data, DataCheckerService $dataCheckerService) {
                    $record = $this->getRecord();
                    $flexid = $record->flexapp_id;

                    $response = $dataCheckerService->addTransaction([
                        'uuid' => $flexid,
                        'transactionId' => $data['transaction_id']
                    ]);

                    return Notification::make()
                        ->title($response['message'])
                        ->persistent()
                        ->success()
                        ->send();
                }),
            Action::make('uitdienst')
                ->label('Uitdienst melden')
                ->color('danger')
                ->form([
                    ...$this->uitdienstForm()
                ])
                ->disabled(function ($record) {
                    $offboarded = EmployeeOffboarding::where('applicant_id', $record->applicant_id)
                        ->first();

                    if($offboarded){
                        return true;
                    }
                    return false;
                })
                ->requiresConfirmation()
                ->action(function (array $data, EmployeeOffboardingController $offboardingController) {
                    $offboard = $offboardingController->offboardUser($data, $this->getRecord());

                    if($offboard){
                        return Notification::make()
                            ->title('Uitdienst melding gelukt')
                            ->persistent()
                            ->success()
                            ->send();
                    }
                    return Notification::make()
                        ->title('Uitdienst melding mislukt')
                        ->persistent()
                        ->danger()
                        ->send();
                })
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        $record = $this->getRecord();
        $enrollmentStatus = $record->enrollmentStatus;
        $renewalStatus = $record->renewalStatus->step_in_process ?? null;

        $agencyStatus = $enrollmentStatus->agency_status;
        $currentStatus = $enrollmentStatus->step_in_process ?? 0;

        $schema = [
            ...($record->flexappData ? [
                ...$this->identificationImages(),
                ...$this->identificationInfo(),
                ...$this->personalInfo(),
                ...$this->contactInfo(),
                ...$this->financialInfo(),
                ...$this->successiveEmploymentInfo(),
            ] : [])
        ];

        $actions = [];

        if($renewalStatus === 3 || $renewalStatus === 4) {
            $actions = [
                ...$this->identificationActions(),
            ];
        }else{

            if($currentStatus === 10 && $record->flexappData->info->twv_required == true)
                $actions = [
                    ...$this->twvActions(),
                ];
        }

        if($currentStatus === 3) {
            $actions = [
                ...$this->enrollmentReminder(),
            ];
        } else if($currentStatus === 4) {
            $schema = [
                ...$this->identificationImages(),
                ...$this->identificationInfo(),
            ];

            $actions = [
                ...$this->identificationActions(),
            ];
        } else if($currentStatus === 5) {
            $schema = [
                ...$this->identificationImages(),
                ...$this->identificationInfo(),
                ...$this->personalInfo(),
                ...$this->contactInfo(),
                ...$this->financialInfo(),
                ...$this->successiveEmploymentInfo(),
            ];

            $actions = [
                ...$this->dataCheckActions(),
            ];
        } else if($currentStatus === 8) {
            $actions = [
                ...$this->easyflexActions(),
            ];
        }

        if(!$record->easyflex_id && ($currentStatus > 5  || ($record->flexappData->info->data_checked_by_recruiter ?? false)) && $currentStatus <= 8) {
            $actions = [
                ...$this->storeEasyflexActions(),
            ];
        }

        if($currentStatus > 5 || ($record->flexappData->info->data_checked_by_recruiter ?? false)) {
            $actions = [
                ...$actions,
                ...$this->sendContractActions(),
            ];
        }

        if($this->ifAgeIsUnder16()) {
            $actions = [
                Infolists\Components\TextEntry::make('')
                    ->getStateUsing(fn($record) => 'De kandidaat is jonger dan 16 jaar en daarmee te jong is om te bezorgen')
                    ->extraAttributes(['class' => 'mb-4 px-4 py-4 bg-danger-100 border-l-4 border-danger-500'])
                    ->size(Infolists\Components\TextEntry\TextEntrySize::Medium)
            ];
        }

        if($agencyStatus === 'Rejected') {
            $actions = [
                Infolists\Components\TextEntry::make('')
                    ->getStateUsing(fn($record) => 'Afgewezen met de volgende reden:<br> <strong>' . (JetApplicantResource::$reasons[$record->enrollmentStatus->agency_status_reason] ?? $record->enrollmentStatus->agency_status_reason) . '</strong>')
                    ->extraAttributes(['class' => 'mb-4 px-4 py-4 bg-danger-100 border-l-4 border-danger-500'])
                    ->size(Infolists\Components\TextEntry\TextEntrySize::Medium)
                    ->html(),
            ];
        }

        return $infolist
            ->columns(12)
            ->schema([
                Infolists\Components\Fieldset::make('')
                    ->columnSpanFull()
                    ->extraAttributes(['class' => '-ms-6 fi-fieldset--flex'])
                    ->schema([
                        ...$this->currentStatusesBadges()
                    ]),

                Infolists\Components\Section::make()
                    ->columnSpan(8)
                    ->schema([
                        ...($currentStatus < 5 ? [
                            ...$this->verifaiStatuses(),
                        ] : []),

                        ...$this->notes(),
                        ...$this->consentInfo(),

                        ...$schema,

                        ...$this->jetInfo(),
                    ]),

                Infolists\Components\Section::make()
                    ->extraAttributes(['class' => 'fi-section--sticky'])
                    ->columnSpan(4)

                    ->schema([
                        Infolists\Components\Section::make('Acties uitvoeren')
                            ->extraAttributes(['class' => 'fi-section--actions'])
                            ->schema([
                                ...$this->idsList(),

                                ...($record->enrollmentStatus?->on_hold ? [
                                    Infolists\Components\TextEntry::make('')
                                        ->getStateUsing(fn($record) => 'On hold door de volgende reden:<br> <strong>' . $record->enrollmentStatus?->on_hold_reason . '</strong>')
                                        ->extraAttributes(['class' => 'mb-4 px-4 py-4 bg-danger-100 border-l-4 border-danger-500'])
                                        ->size(Infolists\Components\TextEntry\TextEntrySize::Medium)
                                        ->html()
                                ] : []),


                                ...(auth()->user()->hasRole('super_admin') ? [
                                    Infolists\Components\Actions::make([
                                        Infolists\Components\Actions\Action::make('Verander emailadres')
                                            ->label('Verander emailadres')
                                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                            ->iconPosition('after')
                                            ->color('warning')
                                            ->requiresConfirmation()
                                            ->form([
                                                TextInput::make('new_email_address')
                                                    ->email()
                                                    ->required()
                                                    ->maxLength(255)
                                                    ->label('Nieuw emailadres')
                                                    ->helperText(str('**Let op**: Verander hierna zelf het email adres in Easyflex!')->inlineMarkdown()->toHtmlString()),
                                            ])
                                            ->action(function (array $data, $record) {
                                                UserActions::updateUserEmailAddress($record->flexapp_id, $data['new_email_address']);
                                            }),
                                ])] : []),

                                Infolists\Components\Actions::make([
                                    Infolists\Components\Actions\Action::make('download concept')
                                        ->label('Download concept')
                                        ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                        ->iconPosition('after')
                                        ->color('info')
                                        ->outlined()
                                        ->requiresConfirmation()
                                        ->form([
                                            ...$this->contractForm(true)
                                        ])
                                        ->action(function (ContractController $contract, $record, array $data) {
                                            $data['total_forms'] = ['contract'];
                                            $concept_contract = $contract->generateContract($record, $data, true);
                                            if($concept_contract){
                                                return response()->download(storage_path('/app/'.$concept_contract))->deleteFileAfterSend(true);
                                            }
                                        }),
                                ]),

                                ...($agencyStatus !== 'Rejected' && $agencyStatus !== 'Approved' ? [
                                    Infolists\Components\Actions::make([
                                        Infolists\Components\Actions\Action::make('Afwijzen')
                                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                            ->iconPosition('after')
                                            ->color('danger')
                                            ->requiresConfirmation()
                                            ->form([
                                                TextInput::make('reject_reason')
                                                    ->required()
                                                    ->maxLength(255)
                                                    ->label('Reden van afwijzen')
                                                    ->datalist(JetApplicantResource::$reasons)
                                            ])
                                            ->action(function (array $data, $record, SignhostService $signhostService) {
                                                JetActions::rejectApplicant($record->applicant_id, $data['reject_reason']);
                                                $signhostService->cancelAllContracts($record->flexapp_id);
                                            }),
                                    ]),
                                ] : []),

                                ...$this->applicantFunction(),

                                ...$actions,

                                ...(($agencyStatus === 'Rejected' && auth()->user()->hasRole('super_admin')) ? [
                                    Infolists\Components\Actions::make([
                                        Infolists\Components\Actions\Action::make('Afwijzing intrekken')
                                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                            ->iconPosition('after')
                                            ->color('danger')
                                            ->requiresConfirmation()
                                            ->action(function (array $data, $record) {
                                                JetActions::revertRejectedApplicant($record->applicant_id);
                                            }),
                                    ]),
                                ] : []),
                            ]),

                        ...($currentStatus > 5 || ($record->flexappData->info->data_checked_by_recruiter ?? false) ? [
                            Infolists\Components\Section::make('Contracten')
                                ->extraAttributes(['class' => 'fi-section--actions'])
                                ->schema([
                                    Infolists\Components\Livewire::make('JetApplicantsContractStatuses')
                                        ->key('JetApplicantsContractStatuses')
                                        ->lazy()
                                ])
                        ] : [])
                    ]),
        ]);
    }

    private function idsList(): array
    {
        return [
            Infolists\Components\TextEntry::make('flexapp_id')
                ->extraAttributes(['class' => '-mt-2 mb-4'])
                ->getStateUsing(function($record) {
                    $ids = [];

                    if($record->scoober_id) {
                        $ids[] = 'Scoober: ' . $record->scoober_id;
                    }

                    if($record->easyflex_id) {
                        $ids[] = 'Easyflex: ' . $record->easyflex_id;
                    }

                    if($record->wenote_id) {
                        $ids[] = 'Wenote: ' . $record->wenote_id;
                    }

                    if($record->lead_id) {
                        $ids[] = 'Salesforce: ' . $record->lead_id;
                    }

                    if($record->flexapp_id) {
                        $ids[] = 'Flexapp: ' . $record->flexapp_id;
                    }

                    return $ids;
                })
                ->hiddenLabel()
                ->badge()
        ];
    }

    private function currentStatusesBadges(): array
    {
        $badges = [];
        $record = $this->getRecord();
        $agencyStatus = $record->enrollmentStatus->agency_status;
        $stepInProcess = $record->enrollmentStatus->step_in_process ?? 0;

        if ($record->enrollmentStatus?->on_hold) {
            $badges[] = Infolists\Components\TextEntry::make('')
                ->getStateUsing('On hold')
                ->hiddenLabel()
                ->badge()
                ->color('danger')
            ;
        }

        if($agencyStatus === 'Rejected' || ($agencyStatus === 'Approved' && $stepInProcess === 10)) {
            $badges[] = Infolists\Components\TextEntry::make('enrollmentStatus.agency_status')
                ->getStateUsing(fn () => $agencyStatus)
                ->hiddenLabel()
                ->badge()
                ->color(fn () => $agencyStatus === 'Approved' ? 'success' : 'danger')
            ;
            if($record->renewalStatus){
                $badges[] = Infolists\Components\TextEntry::make('renewalStatus.step_in_process') // step_name
                ->getStateUsing(fn ($record) => JetApplicantResource::$renewStatusses[$record->renewalStatus->step_in_process ?? 0]['label'])
                    ->hiddenLabel()
                    ->badge()
                    ->color(fn ($record) => JetApplicantResource::$renewStatusses[$record->renewalStatus->step_in_process ?? 0]['color'])
                ;
            }

        } else {
            $badges[] = Infolists\Components\TextEntry::make('enrollmentStatus.step_in_process') // step_name
                ->getStateUsing(fn ($record) => JetApplicantResource::$statusses[$record->enrollmentStatus->step_in_process ?? 0]['label'])
                ->hiddenLabel()
                ->badge()
                ->color(fn ($record) => JetApplicantResource::$statusses[$record->enrollmentStatus->step_in_process ?? 0]['color'])
            ;

            if($record->renewalStatus){
                $badges[] = Infolists\Components\TextEntry::make('renewalStatus.step_in_process') // step_name
                ->getStateUsing(fn ($record) => JetApplicantResource::$renewStatusses[$record->renewalStatus->step_in_process ?? 0]['label'])
                    ->hiddenLabel()
                    ->badge()
                    ->color(fn ($record) => JetApplicantResource::$renewStatusses[$record->renewalStatus->step_in_process ?? 0]['color'])
                ;
            }

            if($record->flexappData) {
                $otherStatuses = [
                    'identification_approved' => ['Identificatie afgekeurd', 'Identificatie goedgekeurd'],
                    'right_to_work' => ['Right to Work - Nee', 'Right to Work - Ja'],
                    'twv_required' => ['TWV aanvraag niet vereist', 'TWV aanvraag vereist'],
                    'twv_requested' => ['', 'TWV aangevraagd'],
                    'twv_approved' => ['TWV afgekeurd', 'TWV goedgekeurd'],
                    'uwv_notified' => ['', 'Aangemeld bij UWV'],
                    'data_checked_by_recruiter' => ['', 'Gegevens kloppen'],
                ];

                foreach($otherStatuses as $status => $label) {
                    if($record->flexappData->info[$status] !== null) {
                        $badges[] = Infolists\Components\TextEntry::make('')
                            ->getStateUsing(fn() => $label[$record->flexappData->info[$status]])
                            ->hiddenLabel()
                            ->badge()
                            ->color(function($record) use ($status) {
                                if($status === 'twv_required') {
                                    return !$record->flexappData->info[$status] ? 'success' : 'danger';
                                }

                                return $record->flexappData->info[$status] ? 'success' : 'danger';
                            })
                        ;
                    }
                }
            }
        }

        if ($record->is_import) {
            $badges[] = Infolists\Components\TextEntry::make('')
                ->getStateUsing('Randstad verlenging')
                ->hiddenLabel()
                ->badge()
                ->color('warning')
            ;
        }

        return $badges;
    }

    private function ifAgeIsUnder16(): bool
    {
        $record = $this->getRecord();

        if($record->flexapp_id) {
            $dateOfBirth = $record->flexappData?->personalData?->date_of_birth;
        } else {
            $dateOfBirth = $record->date_of_birth;
        }

        $age = Carbon::parse($dateOfBirth)->age;

        return $age < 16;
    }

    private function enrollmentReminder(): array
    {
        return [
            Infolists\Components\Fieldset::make('Inschrijving')
                ->columns(1)
                ->schema([
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('Verstuur herinnering')
                            ->extraAttributes(['class' => 'fi-ac-btn-action--single-action fi-ac-btn-action-chip'])
                            ->outlined()
                            ->iconPosition('after')
                            ->color('info')
                            ->requiresConfirmation()
                            ->action(function (array $data, $record) {
                                UserActions::sendJETRegistrationReminder($record->flexapp_id);
                            }),
                    ])
                ])
        ];
    }

    private function verifaiStatuses (): array
    {
        $verifaiScanData = $this->getVerifaiScanData();
        $identificationStatus = $verifaiScanData['identification']['statusMostRecent'];
        $driversLicenseStatus = $verifaiScanData['drivingLicense']['statusMostRecent'];

        return [
            Infolists\Components\Fieldset::make('')
                ->columns(1)
                ->schema([
                Infolists\Components\TextEntry::make('')
                    ->getStateUsing(function ($record) use ($identificationStatus) {
                        $documentType = $record?->flexappData?->identification?->document_type;

                        if($identificationStatus === 'approved') {
                            return $documentType . ' scan is goedgekeurd door Verifai';
                        } else if($identificationStatus === 'rejected') {
                            return $documentType . ' scan is afgekeurd door Verifai';
                        } else if($identificationStatus === 'pending') {
                            return $documentType . ' wordt momenteel gescand door Verifai';
                        }
                    })
                    ->extraAttributes(function () use ($identificationStatus) {
                        $classes = 'mb-4 px-4 py-4 border-l-4';

                        if($identificationStatus === 'approved') {
                            $classes .= ' bg-success-100 border-success-500';
                        } else if($identificationStatus === 'rejected') {
                            $classes .= ' bg-danger-100 border-danger-500';
                        } else if($identificationStatus === 'pending') {
                            $classes .= ' bg-blue-100 border-info-500';
                        }

                        return ['class' => $classes];
                    })
                    ->size(Infolists\Components\TextEntry\TextEntrySize::Medium)
                    ->hidden(fn () => $verifaiScanData['identification']['statusMostRecent'] === null),

                Infolists\Components\TextEntry::make('')
                    ->getStateUsing(function () use ($driversLicenseStatus) {
                        if($driversLicenseStatus === 'approved') {
                            return 'Rijbewijs scan is goedgekeurd door Verifai';
                        } else if($driversLicenseStatus === 'rejected') {
                            return 'Rijbewijs scan is afgekeurd door Verifai';
                        } else if($driversLicenseStatus === 'pending') {
                            return 'Rijbewijs wordt momenteel gescand door Verifai';
                        }
                    })
                    ->extraAttributes(function () use ($driversLicenseStatus) {
                        $classes = 'mb-4 px-4 py-4 border-l-4';

                        if($driversLicenseStatus === 'approved') {
                            $classes .= ' bg-success-100 border-success-500';
                        } else if($driversLicenseStatus === 'rejected') {
                            $classes .= ' bg-danger-100 border-danger-500';
                        } else if($driversLicenseStatus === 'pending') {
                            $classes .= ' bg-blue-100 border-info-500';
                        }

                        return ['class' => $classes];
                    })
                    ->size(Infolists\Components\TextEntry\TextEntrySize::Medium)
                    ->hidden(fn () => $verifaiScanData['drivingLicense']['statusMostRecent'] === null),
            ])
        ];
    }

    private function identificationImages(): array
    {
        if ($this->getRecord()->flexappData->info->uses_datachecker) {
            return [
                Infolists\Components\Fieldset::make('DataChecker')
                    ->columns(1)
                    ->schema([
                        Infolists\Components\Livewire::make('DataCheckerDocuments', [
                            'uuid' => $this->getRecord()?->flexapp_id,
                        ])->key('DataCheckerDocuments' . ($this->getRecord()->enrollmentStatus->step_in_process ?? 0)),
                    ])
            ];
        } else {
            $verifaiScanData = $this->getVerifaiScanData();

            return [
                Infolists\Components\Fieldset::make('')
                    ->schema([
                        Infolists\Components\ImageEntry::make('front')
                            ->defaultImageUrl("{$verifaiScanData['identification']['primary']}")
                            ->hidden(fn() => $verifaiScanData['identification']['primary'] == '')
                            ->extraImgAttributes(['loading' => 'lazy'])
                            ->label(fn($record) => $record->flexappData->identification->document_type . ' voorkant'),
                        Infolists\Components\ImageEntry::make('back')
                            ->defaultImageUrl("{$verifaiScanData['identification']['secondary']}")
                            ->hidden(fn($record) => $record->flexappData->identification->document_type === $this->documentTypes['P']
                                || $verifaiScanData['identification']['secondary'] == ''
                            )
                            ->extraImgAttributes(['loading' => 'lazy'])
                            ->label(fn($record) => $record->flexappData->identification->document_type . ' achterkant')
                    ]),

                Infolists\Components\Fieldset::make('')
                    ->schema([
                        Infolists\Components\ImageEntry::make('front')
                            ->defaultImageUrl("{$verifaiScanData['drivingLicense']['primary']}")
                            ->hidden(fn() => !$verifaiScanData['drivingLicense']['primary'])
                            ->extraImgAttributes(['loading' => 'lazy'])
                            ->label('Rijbewijs voorkant'),
                        Infolists\Components\ImageEntry::make('back')
                            ->defaultImageUrl("{$verifaiScanData['drivingLicense']['secondary']}")
                            ->hidden(fn() => !$verifaiScanData['drivingLicense']['secondary'])
                            ->extraImgAttributes(['loading' => 'lazy'])
                            ->label('Rijbewijs achterkant'),
                    ]),
            ];
        }
    }

    private function getVerifaiScanData() {
        if($this->verifaiScanData === null) {
            $flexapp_id = $this->getRecord()?->flexapp_id;

            $identificationReport = (array) DB::connection('verifai')
                ->table('reports')
                ->where([
                    ['flex_id', $flexapp_id],
                    ['selected_document_type', '!=', 'D']
                ])
                ->orderBy('verifai_created', 'desc')
                ->first();

            $drivingLicenseReport = (array) DB::connection('verifai')
                ->table('reports')
                ->where([
                    ['flex_id', $flexapp_id],
                    ['selected_document_type', 'D']
                ])
                ->orderBy('verifai_created', 'desc')
                ->first();

            if($identificationReport == []) {
                $identificationImageURLs = [
                    "primary" => "",
                    "secondary" => ""
                ];
            } else {
                $images = DB::connection('verifai')
                    ->table('identity_document_pages')
                    ->where('identity_document_id', $identificationReport['id'])
                    ->get(['type', 'image_cropped_uuid'])
                ;

                $identificationFolder = App::environment('production')
                    ? "identifications"
                    : "test_identifications"
                ;

                $identificationImageURLs = $images->map(function ($item) use ($identificationFolder) {
                    return [
                        $item->type => Storage::disk('identification')
                            ->temporaryUrl("$identificationFolder/{$item->image_cropped_uuid}",
                                now()->addMinutes(5))
                    ];
                })->collapse();

                if (!array_key_exists('secondary', $identificationImageURLs->toArray())) {
                    $identificationImageURLs['secondary'] = '';
                }
            }

            if($drivingLicenseReport == []) {
                $drivingLicenseImageURLs = [
                    "primary" => "",
                    "secondary" => ""
                ];
            } else {
                $images = DB::connection('verifai')
                    ->table('identity_document_pages')
                    ->where('identity_document_id', $drivingLicenseReport['id'])
                    ->get(['type', 'image_cropped_uuid']);

                $identificationFolder = App::environment('production') ? "identifications" : "test_identifications";

                $drivingLicenseImageURLs = $images->map(function ($item) use ($identificationFolder) {
                    return [
                        $item->type => Storage::disk('identification')
                            ->temporaryUrl("$identificationFolder/{$item->image_cropped_uuid}",
                                now()->addMinutes(5))
                    ];
                })->collapse();
            }

            #region Get pending reports count
            $webhooks = DB::connection('verifai')
                ->table('webhooks')
                ->where('flex_id', $flexapp_id)
                ->get();
            $completedIdWebhooks = $webhooks->where('processing_status', 'completed')
                ->where('selected_document_type', '!=', 'D')
                ->pluck('flex_timestamp');
            $pendingIdReportCount = $webhooks->whereNotIn('flex_timestamp', $completedIdWebhooks)
                ->where('selected_document_type', '!=', 'D')
                ->groupBy('profile_uuid')
                ->count();
            $completedDrivingLicenseWebhooks = $webhooks->where('processing_status', 'completed')
                ->where('selected_document_type', '=', 'D')
                ->pluck('flex_timestamp');
            $pendingDrivingLicenseReportCount = $webhooks->whereNotIn('flex_timestamp', $completedDrivingLicenseWebhooks)
                ->where('selected_document_type', '=', 'D')
                ->groupBy('profile_uuid')
                ->count();
            #endregion

            #region Get most recent scan status
            $statusMostRecentId = DB::connection('verifai')
                ->table('reports')
                ->where([
                    ['flex_id', $flexapp_id],
                    ['selected_document_type', '!=', 'D']
                ])
                ->orderBy('verifai_created', 'desc')
                ->value('report_status');
            if ($pendingIdReportCount > 0) {
                $statusMostRecentId = 'pending';
            }

            $statusMostRecentDrivingLicense = DB::connection('verifai')
                ->table('reports')
                ->where([
                    ['flex_id', $flexapp_id],
                    ['selected_document_type', '=', 'D']
                ])
                ->orderBy('verifai_created', 'desc')
                ->value('report_status');
            if ($pendingDrivingLicenseReportCount > 0) {
                $statusMostRecentDrivingLicense = 'pending';
            }
            #endregion

            $verifaiScanData['identification'] = $identificationImageURLs;
            $verifaiScanData['identification']['amountPending'] = $pendingIdReportCount;
            $verifaiScanData['identification']['statusMostRecent'] = $statusMostRecentId;

            $verifaiScanData['drivingLicense'] = $drivingLicenseImageURLs;
            $verifaiScanData['drivingLicense']['amountPending'] = $pendingDrivingLicenseReportCount;
            $verifaiScanData['drivingLicense']['statusMostRecent'] = $statusMostRecentDrivingLicense;

            $this->verifaiScanData = $verifaiScanData;
        }

        return $this->verifaiScanData;
    }

    private function identificationInfo(): array
    {
        return [
            Infolists\Components\Fieldset::make('Legitimatie')
                ->schema([
                    Infolists\Components\ViewEntry::make('flexappData.identification.nationality')
                        ->label('Nationaliteit')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('flexappData.identification.place_of_birth')
                        ->label('Geboorteplaats')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('flexappData.identification.document_number')
                        ->label('Documentnummer')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('flexappData.identification.citizen_service_number')
                        ->label('BSN')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('flexappData.personalData.date_of_birth')
                        ->label('Geboortedatum')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('flexappData.identification.identification_valid_until')
                        ->label('Geldig tot')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('flexappData.info.driving_license_required')
                        ->label('Rijbewijs verplicht')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('flexappData.drivingLicense.document_number')
                        ->label('Rijbewijs documentnummer')
                        ->hidden(fn ($record) => !($record->flexappData->drivingLicense['document_number'] ?? null))
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('flexappData.drivingLicense.date_of_expiry')
                        ->getStateUsing(fn ($record) => Carbon::createFromFormat('Y-m-d', $record->flexappData->drivingLicense->date_of_expiry)->format('d-m-Y'))
                        ->label('Rijbewijs geldig tot')
                        ->hidden(fn ($record) => !($record->flexappData->drivingLicense['date_of_expiry'] ?? null))
                        ->view('filament.components.view-field'),
                ]),
        ];
    }

    private function twvActions(): array
    {
        $record = $this->getRecord();
        $twvRequired = $record->flexappData->info->twv_required;
        $mostRecentTwv = $record->twvInformation()->latest()->first();

        if(!$twvRequired){
            return [];
        }

        $identificationApprovedActions = [
            Infolists\Components\Fieldset::make('Tewerkstellingsvergunning (vernieuwen)')
                ->columns(1)
                ->schema([
                    Infolists\Components\TextEntry::make('')
                        ->getStateUsing(function () use ($mostRecentTwv) {
                            if(!$mostRecentTwv){
                                return 'Geen twv aanvraag geregistreerd';
                            }
                            return $mostRecentTwv->twv_expiration_date
                                ? 'Meest recente twv geldig tot ' . $mostRecentTwv->twv_expiration_date?->format('d-m-Y')
                                : 'Meest recente twv aangevraagd op ' . $mostRecentTwv->twv_requested_date?->format('d-m-Y');
                        })
                        ->extraAttributes(['class' => 'mb-4 px-4 py-4 bg-info-100 border-l-4 border-blue-500'])
                        ->size(Infolists\Components\TextEntry\TextEntrySize::Medium),
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('TWV aangevraagd')
                            ->label('Aangevraagd')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->outlined()
                            ->requiresConfirmation()
                            ->form([
                                DatePicker::make('twv_requested_date')
                                    ->default(now())
                                    ->label('TWV aangevraagd datum')
                                    ->required(),
                                Select::make('twv_is_student')
                                    ->label('Is student')
                                    ->options([
                                        1 => 'Ja',
                                        0 => 'Nee',
                                    ])
                                    ->required(),
                            ])
                            ->action(function (array $data, $record, CommunicationService $communicationService) {
                                // TWV aangevraagd & TWV aangevraagd date
                                $record->flexappData->info->twv_requested = true;
                                $record->flexappData->info->twv_requested_date = $data['twv_requested_date'];
                                $record->flexappData->info->twv_is_student = (bool) $data['twv_is_student'];
                                $record->flexappData->info->save();

                                TwvInformation::create([
                                    'applicant_id' => $record->applicant_id,
                                    'twv_requested_date' => $data['twv_requested_date'],
                                ]);

                                $whatsappData = [
                                    'name' => $record->flexappData?->personalData?->first_name,
                                    'phone_number' => $record->flexappData?->personalData?->phone_number,
                                    'language' => ($record->flexappData?->applicationProfile?->language === 'Engels') ? 'en' : 'nl'
                                ];

                                $communicationService->sendWorkPermitRequest($whatsappData);
                            })
                    ])->fullWidth(),
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('Goedkeuren')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->hidden(fn() => !$mostRecentTwv)
                            ->outlined(fn ($record) => !$record->flexappData->info->twv_approved)
                            ->requiresConfirmation()
                            ->form([
                                TextInput::make('twv_number')
                                    ->label('TWV nummer')
                                    ->default(function () use ($mostRecentTwv): string {
                                        return $mostRecentTwv->twv_number ?? '';
                                    })
                                    ->required(),
                                TextInput::make('twv_issued_by')
                                    ->label('TWV afgegeven door')
                                    ->default(function () use ($mostRecentTwv): string {
                                        return $mostRecentTwv->twv_issued_by ?? '';
                                    })
                                    ->required(),
                                DatePicker::make('twv_approved_date')
                                    ->label('TWV goedgekeurd datum')
                                    ->default(function () use ($mostRecentTwv): string {
                                        return $mostRecentTwv->twv_approved_date ?? '';
                                    })
                                    ->required(),
                                DatePicker::make('twv_start_date')
                                    ->label('TWV ingangsdatum')
                                    ->default(function () use ($mostRecentTwv): string {
                                        return $mostRecentTwv->twv_start_date ?? '';
                                    })
                                    ->required(),
                                DatePicker::make('twv_expiration_date')
                                    ->label('TWV verloopdatum')
                                    ->default(function () use ($mostRecentTwv): string {
                                        return $mostRecentTwv->twv_expiration_date ?? '';
                                    })
                                    ->required(),
                                FileUpload::make('twv_file')
                                    ->disk('identification')
                                    ->acceptedFileTypes(['application/pdf'])
                                    ->getUploadedFileNameForStorageUsing(
                                        fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                            ->prepend('twv_'. \Illuminate\Support\Carbon::now()->timestamp.'_'),
                                    )
                                    ->directory('twv')
                                    ->label('TWV bestand'),
                            ])
                            ->action(function (array $data, $record, EasyflexContractsController $easyflexContractsController, EasyflexService $easyflexService) {
                                $easyflexService->storeTwvFile($record, $data);
                            }),

                        Infolists\Components\Actions\Action::make('Afkeuren')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->hidden(fn() => !$mostRecentTwv)
                            ->outlined(fn ($record) => $record->flexappData->info->twv_approved !== false)
                            ->requiresConfirmation()
                            ->action(function ($record) {
                                // RTW Nee & TWV afgekeurd
                                $record->flexappData->info->right_to_work = false;
                                $record->flexappData->info->twv_approved = false;
                                $record->flexappData->info->save();
                            }),
                    ])->fullWidth()
                ])
        ];

        return $identificationApprovedActions;
    }

    private function identificationActions(): array
    {
        $record = $this->getRecord();

        if ($record->flexappData->info->uses_datachecker) {
            $dataCheckerService = new DataCheckerService();
            $data = $dataCheckerService->getMostRecentDocuments($record?->flexapp_id);
            $typeOfDocument = 'identity';

            if (!empty($data)) {
                if (isset($data['residencePermit'])) {
                    if (isset($data['identity'])) {
                        $identityDate = Carbon::parse($data['identity']['info']['created_at']);
                        $residencePermitDate = Carbon::parse($data['residencePermit']['info']['created_at']);

                        if ($residencePermitDate->isAfter($identityDate)) {
                            $typeOfDocument = 'residencePermit';
                        }
                    } else {
                        $typeOfDocument = 'residencePermit';
                    }
                }
            }

            $typeOfResidencePermit = $data[$typeOfDocument]['compare_documents']['residence_permit_type'] ?? null;
        } else {
            $typeOfDocument = match($record->flexappData->identification->document_type) {
                'Identiteitskaart', 'Paspoort' => 'identity',
                'Verblijfsvergunning' => 'residencePermit',
                default => 'identity',
            };

            $typeOfResidencePermit = null;
        }

        $nationality = $record->flexappData->identification->nationality;
        $identificationApproved = $record->flexappData->info->identification_approved;
        $rightToWork = $record->flexappData->info->right_to_work;

        $actions = [];

        $identificationApprovedActions = [
            Infolists\Components\Fieldset::make('Identificatie bewijs goedkeuren')
                ->columns(1)
                ->schema([
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('Goedkeuren')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->outlined(fn ($record) => !$record->flexappData->info->identification_approved)
                            ->requiresConfirmation()
                            ->action(function ($record) use ($typeOfDocument, $typeOfResidencePermit) {
                                // ID goedgekeurd door HR met DataChecker of Verifai
                                $record->flexappData->info->identification_approved = true;
                                $record->flexappData->info->save();

                                if(
                                    $record->flexappData->info->identification_approved
                                    && $record->flexappData->info->right_to_work
                                    && $typeOfDocument !== 'residencePermit'
                                    && $record->flexappData->identification->nationality !== 'UKR'
                                    && $typeOfResidencePermit !== 'O'
                                ) {
                                    $step4FinishedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 4, true);
                                    $step5StartedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 5);
                                    JetApplicantEnrollmentActions::finishStep($record->applicant_id, 10, false, $step4FinishedAt);
                                    JetApplicantEnrollmentActions::startStep($record->applicant_id, 11, false, $step5StartedAt);
                                }
                            }),

                        Infolists\Components\Actions\Action::make('Afkeuren')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip break-after-always'])
                            ->iconPosition('after')
                            ->color('info')
                            ->outlined(fn ($record) => $record->flexappData->info->identification_approved !== false)
                            ->requiresConfirmation()
                            ->action(function ($record) {
                                // ID afgekeurd door HR met datachecker of verifai
                                $record->flexappData->info->identification_approved = false;
                                $record->flexappData->info->save();
                            }),
                    ])->fullWidth()
                ])
        ];

        $allowedVehiclesActions = [
            Infolists\Components\Fieldset::make('Mag bezorgen met')
                ->columns(1)
                ->schema([
                    Infolists\Components\TextEntry::make('')
                        ->getStateUsing(fn($record) => 'Heeft aangegeven te kunnen rijden met: <strong>' . $record->active_transportation_type . '</strong>')
                        ->extraAttributes(['class' => 'mb-4 px-4 py-4 bg-info-100 border-l-4 border-blue-500'])
                        ->size(Infolists\Components\TextEntry\TextEntrySize::Medium)
                        ->html(),

                    ...((!($record->flexappData->drivingLicense['document_number'] ?? null)) ? [
                        Infolists\Components\TextEntry::make('')
                            ->getStateUsing(fn($record) => 'Er is geen rijbewijs aanwezig en kan daarom alleen met de fiets bezorgen')
                            ->extraAttributes(['class' => 'mb-4 px-4 py-4 bg-danger-100 border-l-4 border-danger-500'])
                            ->size(Infolists\Components\TextEntry\TextEntrySize::Medium)
                    ] : []),

                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('Fiets')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->disabled(),

                        Infolists\Components\Actions\Action::make('Scooter')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->color('info')
                            ->iconPosition('after')
                            ->icon(fn ($record) => $record->flexappData->info->allowed_delivery_vehicle === 1 ? 'heroicon-c-x-mark' : null)
                            ->outlined(fn ($record) => $record->flexappData->info->allowed_delivery_vehicle < 1)
                            ->disabled(function ($record) {
                                if($record->flexappData->info->allowed_delivery_vehicle > 1) {
                                    return true;
                                }

                                if(!($record->flexappData->drivingLicense['document_number'] ?? null)) {
                                    return true;
                                }

                                return false;
                            })
                            ->action(function ($record) {
                                $record->flexappData->info->allowed_delivery_vehicle = $record->flexappData->info->allowed_delivery_vehicle === 1 ? 0 : 1;
                                $record->flexappData->info->save();
                            }),

                        Infolists\Components\Actions\Action::make('Auto')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->color('info')
                            ->iconPosition('after')
                            ->icon(fn ($record) => $record->flexappData->info->allowed_delivery_vehicle === 2 ? 'heroicon-c-x-mark' : null)
                            ->color('info')
                            ->outlined(fn ($record) => $record->flexappData->info->allowed_delivery_vehicle < 2)
                            ->disabled(fn ($record) => !($record->flexappData->drivingLicense['document_number'] ?? null))
                            ->action(function ($record) {
                                $record->flexappData->info->allowed_delivery_vehicle = $record->flexappData->info->allowed_delivery_vehicle === 2 ? 1 : 2;
                                $record->flexappData->info->save();
                            }),
                    ])->fullWidth()
                ]),
        ];

        $rtwActions = [
            Infolists\Components\Fieldset::make('Right to work')
                ->columns(1)
                ->schema([
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('Ja')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->outlined(fn ($record) => !$record->flexappData->info->right_to_work)
                            ->requiresConfirmation()
                            ->action(function ($record) use ($typeOfDocument, $typeOfResidencePermit) {
                                // RTW Ja
                                $record->flexappData->info->right_to_work = true;
                                $record->flexappData->info->save();

                                if(
                                    $record->flexappData->info->identification_approved
                                    && $record->flexappData->info->right_to_work
                                    && $typeOfDocument !== 'residencePermit'
                                    && $record->flexappData->identification->nationality !== 'UKR'
                                    && $typeOfResidencePermit !== 'O'
                                ) {
                                    $step4FinishedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 4, true);
                                    $step5StartedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 5);
                                    JetApplicantEnrollmentActions::finishStep($record->applicant_id, 10, false, $step4FinishedAt);
                                    JetApplicantEnrollmentActions::startStep($record->applicant_id, 11, false, $step5StartedAt);
                                }
                            }),

                        Infolists\Components\Actions\Action::make('Nee')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->outlined(fn ($record) => $record->flexappData->info->right_to_work !== false)
                            ->requiresConfirmation()
                            ->action(function ($record) {
                                // RTW Nee
                                $record->flexappData->info->right_to_work = false;
                                $record->flexappData->info->save();
                            }),
                    ])->fullWidth()
                ])
        ];

        if($typeOfDocument === 'residencePermit' && $typeOfResidencePermit !== 'O') {

            $twvRequired = $record->flexappData->info->twv_required;
            $twvRequested = $record->flexappData->info->twv_requested;

            if($identificationApproved === null || $twvRequired === null) {
                $actions = [
                    ...$actions,

                    Infolists\Components\Fieldset::make('Identificatiebewijs')
                        ->schema([
                            ...$identificationApprovedActions,
                            ...$allowedVehiclesActions,
                        ]),

                    Infolists\Components\Fieldset::make('Tewerkstellingsvergunning')
                        ->schema([
                            Infolists\Components\Fieldset::make('Vereist?')
                                ->columns(1)
                                ->schema([
                                    Infolists\Components\Actions::make([
                                        Infolists\Components\Actions\Action::make('Vereist')
                                            ->label('Vereist')
                                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                            ->iconPosition('after')
                                            ->color('info')
                                            ->outlined(fn ($record) => !$record->flexappData->info->twv_required)
                                            ->requiresConfirmation()
                                            ->action(function ($record) {
                                                // TWV vereist
                                                $record->flexappData->info->twv_required = true;
                                                $record->flexappData->info->save();
                                            }),

                                        Infolists\Components\Actions\Action::make('Niet vereist')
                                            ->label('Niet vereist')
                                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                            ->iconPosition('after')
                                            ->color('info')
                                            ->outlined(fn ($record) => $record->flexappData->info->twv_required !== false)
                                            ->requiresConfirmation()
                                            ->action(function ($record) {
                                                // RTW op ja & TWV niet vereist
                                                $record->flexappData->info->right_to_work = true;
                                                $record->flexappData->info->twv_required = false;
                                                $record->flexappData->info->save();

                                                $step4FinishedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 4, true);
                                                $step5StartedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 5);
                                                JetApplicantEnrollmentActions::finishStep($record->applicant_id, 10, false, $step4FinishedAt);
                                                JetApplicantEnrollmentActions::startStep($record->applicant_id, 11, false, $step5StartedAt);
                                            }),
                                    ])->fullWidth()
                                ])
                        ])
                ];
            } else if($identificationApproved && $twvRequired) {
                if(!$twvRequested) {
                    $actions = [
                        ...$actions,

                        Infolists\Components\Fieldset::make('Tewerkstellingsvergunning')
                            ->columns(1)
                            ->schema([
                                Infolists\Components\Actions::make([
                                    Infolists\Components\Actions\Action::make('TWV aangevraagd')
                                        ->label('Aangevraagd')
                                        ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                        ->iconPosition('after')
                                        ->color('info')
                                        ->outlined(fn ($record) => !$record->flexappData->info->twv_requested)
                                        ->requiresConfirmation()
                                        ->form([
                                            DatePicker::make('twv_requested_date')
                                                ->default(now())
                                                ->label('TWV aangevraagd datum')
                                                ->required(),
                                            Select::make('twv_is_student')
                                                ->label('Is student')
                                                ->options([
                                                    1 => 'Ja',
                                                    0 => 'Nee',
                                                ])
                                                ->required(),
                                        ])
                                        ->action(function (array $data, $record, EasyflexService $efService, CommunicationService $communicationService) {
                                            // TWV aangevraagd & TWV aangevraagd date
                                            $record->flexappData->info->twv_requested = true;
                                            $record->flexappData->info->twv_requested_date = $data['twv_requested_date'];
                                            $record->flexappData->info->twv_is_student = !!$data['twv_is_student'];
                                            $record->flexappData->info->save();

                                            $twvInformation = new TwvInformation();
                                            $twvInformation->applicant_id = $record->applicant_id;
                                            $twvInformation->twv_requested_date = $data['twv_requested_date'];
                                            $twvInformation->save();

                                            $whatsappData = [
                                                'name' => $record->flexappData?->personalData?->first_name,
                                                'phone_number' => $record->flexappData?->personalData?->phone_number,
                                                'language' => ($record->flexappData?->applicationProfile?->language === 'Engels') ? 'en' : 'nl'
                                            ];

                                            $communicationService->sendWorkPermitRequest($whatsappData);

                                            // if contractRenewal update step
                                            if($record->renewalStatus){
                                                JetActions::timeApplicantRenewalStep($record->applicant_id, 3, true);
                                                JetActions::timeApplicantRenewalStep($record->applicant_id, 4, true);
                                            }

                                            // sync naar easyflex
                                            if($record->easyflex_id == null){
                                                $efService->storeEasyflexUser($record);
                                            }
                                        }),
                                        Infolists\Components\Actions\Action::make('TWV toch niet vereist')
                                            ->label('Toch niet vereist')
                                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                            ->iconPosition('after')
                                            ->color('info')
                                            ->outlined(fn ($record) => !$record->flexappData->info->twv_requested)
                                            ->requiresConfirmation()
                                            ->action(function (array $data, $record) {
                                                // RTW op ja & TWV niet vereist
                                                $record->flexappData->info->right_to_work = true;
                                                $record->flexappData->info->twv_required = false;
                                                $record->flexappData->info->save();

                                                $step4FinishedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 4, true);
                                                $step5StartedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 5);
                                                JetApplicantEnrollmentActions::finishStep($record->applicant_id, 10, false, $step4FinishedAt);
                                                JetApplicantEnrollmentActions::startStep($record->applicant_id, 11, false, $step5StartedAt);

                                            }),
                                    ])->fullWidth()
                            ])
                    ];
                } else {
                    $actions = [
                        ...$actions,

                        Infolists\Components\Fieldset::make('Tewerkstellingsvergunning')
                            ->columns(1)
                            ->schema([
                                Infolists\Components\TextEntry::make('')
                                    ->getStateUsing(fn($record) => 'Twv aangevraagd op ' . Carbon::parse($record->flexappData->info->twv_requested_date)->locale('nl')->translatedFormat('j M Y'))
                                    ->extraAttributes(['class' => 'mb-4 px-4 py-4 bg-info-100 border-l-4 border-blue-500'])
                                    ->size(Infolists\Components\TextEntry\TextEntrySize::Medium),

                                Infolists\Components\Fieldset::make('Tewerkstellingsvergunning (TWV)')
                                    ->columns(1)
                                    ->schema([
                                        Infolists\Components\Actions::make([
                                            Infolists\Components\Actions\Action::make('Goedkeuren')
                                                ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                                ->iconPosition('after')
                                                ->color('info')
                                                ->outlined(fn ($record) => !$record->flexappData->info->twv_approved)
                                                ->requiresConfirmation()
                                                ->form([
                                                    TextInput::make('twv_number')
                                                        ->label('TWV nummer')
                                                        ->required(),
                                                    TextInput::make('twv_issued_by')
                                                        ->label('TWV afgegeven door')
                                                        ->required(),
                                                    DatePicker::make('twv_approved_date')
                                                        ->label('TWV goedgekeurd datum')
                                                        ->default(now())
                                                        ->required(),
                                                    DatePicker::make('twv_start_date')
                                                        ->label('TWV ingangsdatum')
                                                        ->required(),
                                                    DatePicker::make('twv_expiration_date')
                                                        ->label('TWV verloopdatum')
                                                        ->required(),
                                                    FileUpload::make('twv_file')
                                                        ->disk('identification')
                                                        ->acceptedFileTypes(['application/pdf'])
                                                        ->getUploadedFileNameForStorageUsing(
                                                            fn (TemporaryUploadedFile $file): string => (string) str($file->getClientOriginalName())
                                                                ->prepend('twv_'. \Illuminate\Support\Carbon::now()->timestamp.'_'),
                                                        )
                                                        ->directory('twv')
                                                        ->label('TWV bestand'),
                                                ])
                                                ->action(function (array $data, $record, EasyflexContractsController $easyflexContractsController, EasyflexService $easyflexService) {
                                                    // sync naar easyflex
                                                    if($record->easyflex_id == null){
                                                        $easyflexService->storeEasyflexUser($record);
                                                    }

                                                    $easyflexService->storeTwvFile($record, $data);

                                                    $step4FinishedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 4, true);
                                                    $step5StartedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 5);
                                                    JetApplicantEnrollmentActions::finishStep($record->applicant_id, 10, false, $step4FinishedAt);
                                                    JetApplicantEnrollmentActions::startStep($record->applicant_id, 11, false, $step5StartedAt);

                                                    $userService = new UserService;
                                                    $userService->updateUser($record->flexapp_id, [
                                                        'info' => [ 'start_date' => Carbon::parse($data['twv_start_date'])->toDateString() ]
                                                    ]);

                                                    // if contractRenewal update step & resend contract
                                                    if($record->renewalStatus && $record->renewalStatus->step_in_process === 5){
                                                        JetActions::timeApplicantRenewalStep($record->applicant_id, 5, true);
                                                        JetActions::timeApplicantRenewalStep($record->applicant_id, 6);
                                                        // retry sending contract
                                                        $easyflexContractsController->renewContract($record, true);
                                                    }
                                                }),

                                            Infolists\Components\Actions\Action::make('Afkeuren')
                                                ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                                ->iconPosition('after')
                                                ->color('info')
                                                ->outlined(fn ($record) => $record->flexappData->info->twv_approved !== false)
                                                ->requiresConfirmation()
                                                ->action(function ($record) {
                                                    // RTW Nee & TWV afgekeurd
                                                    $record->flexappData->info->right_to_work = false;
                                                    $record->flexappData->info->twv_approved = false;
                                                    $record->flexappData->info->save();
                                                }),
                                        ])->fullWidth()
                                    ])
                            ])
                    ];
                }
            }
        } else {
            if($identificationApproved === null || $rightToWork === null) {
                $actions = [
                    ...$actions,

                    Infolists\Components\Fieldset::make('Identificatiebewijs')
                        ->schema([
                            ...$identificationApprovedActions,
                            ...$allowedVehiclesActions,
                            ...$rtwActions
                        ]),
                ];
            } else if($nationality === 'UKR' || $typeOfResidencePermit === 'O') {
                $uwvNotified = $record->flexappData->info->uwv_notified;

                if($uwvNotified === null) {
                    $actions = [
                        ...$actions,

                        Infolists\Components\Fieldset::make('Aanmelden bij UWV')
                            ->columns(1)
                            ->schema([
                                Infolists\Components\Actions::make([
                                    Infolists\Components\Actions\Action::make('set_uwv_notified')
                                        ->label('Aangemeld')
                                        ->extraAttributes(['class' => 'fi-ac-btn-action--single-action fi-ac-btn-action-chip'])
                                        ->iconPosition('after')
                                        ->color('info')
                                        ->outlined()
                                        ->requiresConfirmation()
                                        ->form([
                                            DatePicker::make('uwv_notified_date')
                                                ->label('UWV aangemeld datum')
                                                ->helperText('De werknemer mag als Oekraïner of derdelander op zijn vroegst twee dagen na de UWV aanmelddatum beginnen met werken. De startdatum zal automatisch aangepast worden als die te vroeg is.')
                                                ->default(now())
                                                ->required(),
                                        ])
                                        ->action(function (array $data, $record) {
                                            // UWV aangevraagd + popup voor aanmelddatum
                                            $record->flexappData->info->uwv_notified = true;
                                            $record->flexappData->info->uwv_notified_date = $data['uwv_notified_date'];
                                            $record->flexappData->info->save();

                                            $step4FinishedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 4, true);
                                            $step5StartedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 5);
                                            JetApplicantEnrollmentActions::finishStep($record->applicant_id, 10, false, $step4FinishedAt);
                                            JetApplicantEnrollmentActions::startStep($record->applicant_id, 11, false, $step5StartedAt);

                                            /* The start date should be the original start date
                                             * if it is later than the date that UWV was notified,
                                             * otherwise it should be two days after UWV was notified:
                                             * https://www.uwv.nl/nl/werkvergunning/oekrainer-aanmelden
                                             * "Een werknemer met een permanente Oekraïense verblijfsvergunning,
                                             * valt tot 4 maart 2026 onder de Richtlijn Tijdelijke Bescherming. [...]
                                             * De werknemer mag tot deze datum zonder werkvergunning in Nederland werken.
                                             * Als een werkgever een werknemer uit Oekraïne in dienst neemt
                                             * die valt onder de Richtlijn Tijdelijke Bescherming,
                                             * moet de werkgever de werknemer bij ons aanmelden. [...]
                                             * 2 dagen nadat de werkgever deze melding gedaan heeft, kan de werknemer starten met werken."
                                             */

                                            $startDate = new Carbon($record->flexappData->info->start_date ?? 0); // Defaults to today if null, so default to 1970-01-01 instead
                                            $uwvNotifiedDatePlusTwo = (new Carbon($data['uwv_notified_date']))->addDays(2);
                                            $latestStartDate = $startDate->maximum($uwvNotifiedDatePlusTwo);

                                            $userService = new UserService;
                                            $userService->updateUser($record->flexapp_id, [
                                                'info' => ['start_date' => $latestStartDate->toDateString()]
                                            ]);
                                        }),
                                ])
                            ])
                    ];
                }
            }
        }

        return $actions;
    }

    private function personalInfo(): array
    {
        return [
            Infolists\Components\Fieldset::make('Persoonlijke gegevens')
                ->relationship('flexappData.personalData')
                ->schema([
                    Infolists\Components\ViewEntry::make('gender')
                        ->label('Geslacht')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('initial')
                        ->label('Initialen')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('first_name')
                        ->label('Voornaam')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('tussenvoegsel')
                        ->hidden(fn ($record) => !$record->flexappData->personalData->tussenvoegsel)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('last_name')
                        ->label('Achternaam')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('date_of_birth')
                        ->label('Geboortedatum')
                        ->view('filament.components.view-field'),
                ]),
        ];
    }

    private function contactInfo(): array
    {
        return [
            Infolists\Components\Fieldset::make('Contactgegevens')
                ->relationship('flexappData')
                ->columns(4)
                ->schema([
                    Infolists\Components\ViewEntry::make('personalData.email')
                        ->label('E-mailadres')
                        ->columnSpan(4)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('address.street')
                        ->label('Straat')
                        ->columnSpan(2)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('address.house_number')
                        ->label('Huisnummer')
                        ->columnSpan(1)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('address.house_number_addition')
                        ->label('Toevoeging')
                        ->columnSpan(1)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('address.postal_code')
                        ->label('Postcode')
                        ->columnSpan(2)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('address.city')
                        ->label('Plaats')
                        ->columnSpan(2)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('address.country')
                        ->label('Land')
                        ->columnSpan(2)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('personalData.country_code')
                        ->label('Land code')
                        ->columnSpan(2)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('personalData.phone_number')
                        ->label('Telefoonnummer')
                        ->columnSpan(2)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('personalData.emergency_phone_number')
                        ->label('Noodnummer')
                        ->columnSpan(2)
                        ->view('filament.components.view-field'),
                ]),
        ];
    }

    private function financialInfo(): array
    {
        return [
            Infolists\Components\Fieldset::make('Financiële gegevens')
                ->relationship('flexappData.financialDetails')
                ->schema([
                    Infolists\Components\ViewEntry::make('iban')
                        ->label('Bankrekeningnummer')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('relationship_status')
                        ->label('Burgelijke staat')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('loonheffing')
                        ->label('Loonheffing toepassen')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('is_wage_with_vacation_allowance')
                        ->label('Vakantiegeld direct uitbetalen')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('is_wage_with_vacation_days')
                        ->label('Bovenwettelijke vakantiedagen uitbetalen')
                        ->view('filament.components.view-field'),
                ]),
        ];
    }

    private function successiveEmploymentInfo(): array
    {
        return [
            Infolists\Components\Fieldset::make('Opvolgend werkgeverschap')
                ->relationship('flexappData.successiveEmployership')
                ->schema([
                    Infolists\Components\ViewEntry::make('successive_employership')
                        ->label('Opvolgend werkgeverschap?')
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('job_title')
                        ->label('Functie')
                        ->hidden(fn ($record) => !$record?->flexappData?->successiveEmployership?->successive_employership)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('number_of_contracts')
                        ->label(fn ($record) => 'Aantal contracten'. ($record->flexappData->successiveEmployership->number_of_contracts !== $record->number_of_previous_contracts ? ' (Applicant / Jet)' : ''))
                        ->getStateUsing(function ($record) {
                            if($record->flexappData->successiveEmployership->number_of_contracts !== $record->number_of_previous_contracts) {
                                return $record->flexappData->successiveEmployership->number_of_contracts . ' / ' . $record->number_of_previous_contracts;
                            }

                            return $record->flexappData->successiveEmployership->number_of_contracts;
                        })
                        ->hidden(fn ($record) => !$record?->flexappData?->successiveEmployership?->successive_employership)
                        ->view('filament.components.view-field'),

                    Infolists\Components\ViewEntry::make('number_of_months')
                        ->label(fn ($record) => 'Aantal maanden'. ($record?->flexappData->successiveEmployership->number_of_months !== $record->months_of_work_experience ? ' (Applicant / Jet)' : ''))
                        ->getStateUsing(function ($record) {
                            //number_of_previous_contracts
                            if($record->flexappData->successiveEmployership->number_of_months !== $record->months_of_work_experience) {
                                return $record->flexappData->successiveEmployership->number_of_months . ' / ' . $record->months_of_work_experience;
                            }

                            return $record->flexappData->successiveEmployership->number_of_months;
                        })
                        ->hidden(fn ($record) => !$record?->flexappData?->successiveEmployership?->successive_employership)
                        ->view('filament.components.view-field'),
                    Infolists\Components\ViewEntry::make('first_contract_start_date')
                        ->label('Eerst contract startdatum')
                        ->getStateUsing(fn ($record) => $record->first_contract_start_date ? Carbon::createFromFormat('Y-m-d', $record->first_contract_start_date)->format('d-m-Y') : 'Geen startdatum bekend')
                        ->view('filament.components.view-field'),
                ]),
        ];
    }

    private function jetInfo(): array
    {
        return [
            Infolists\Components\Fieldset::make('JET info')
                ->schema([
                    Infolists\Components\ViewEntry::make('pre_contract_start_date')
                        ->label('Startdatum')
                        ->getStateUsing(function ($record) {
                            $preContractStartDate = $record->pre_contract_start_date;
                            if ($record?->flexappData?->info?->start_date) {
                                $preContractStartDate = $record->flexappData->info->start_date;
                            }
                            return $preContractStartDate ? Carbon::parse($preContractStartDate)->format('d-m-Y') : 'Geen startdatum bekend';
                        })
                        ->view('filament.components.view-field'),
                    Infolists\Components\ViewEntry::make('pre_contract_end_date')
                        ->label('Einddatum')
                        ->default('Geen einddatum bekend')
                        ->getStateUsing(fn ($record) => empty($record?->flexappData?->info?->end_date)
                            ? 'Geen einddatum bekend'
                            : Carbon::createFromFormat('Y-m-d', $record?->flexappData?->info?->end_date)->format('d-m-Y'))
                        ->view('filament.components.view-field'),
                    Infolists\Components\ViewEntry::make('probation_end_date')
                        ->label('Proeftijd einddatum')
                        ->default('Geen einddatum bekend')
                        ->getStateUsing(fn ($record) => empty($record?->flexappData?->info?->probation_end_date)
                            ? 'Geen einddatum bekend'
                            : Carbon::createFromFormat('Y-m-d', $record?->flexappData?->info?->probation_end_date)->format('d-m-Y'))
                        ->view('filament.components.view-field'),
                    Infolists\Components\ViewEntry::make('pre_contract_minimum_hours')
                        ->getStateUsing(fn ($record) => (int)$record->pre_contract_minimum_hours)
                        ->label('Contract uren')
                        ->view('filament.components.view-field'),
                    Infolists\Components\ViewEntry::make('delivery_area')
                        ->label('Bezorggebied')
                        ->view('filament.components.view-field'),
                    Infolists\Components\ViewEntry::make('pre_contract_hourly_wage')
                        ->label('Uurloon')
                        ->view('filament.components.view-field'),
                    Infolists\Components\ViewEntry::make('average_hours')
                        ->label('Gemiddeld gewerkte uren')
                        ->getStateUsing(function ($record) {
                            $applicantId = $record->applicant_id;
                            $currentWeek = Carbon::now()->weekOfYear;
                            $currentYear = Carbon::now()->year;

                            $startWeek = $currentWeek - 13;
                            $startYear = $currentYear;

                            if ($startWeek <= 0) {
                                $startWeek += 52;
                                $startYear -= 1;
                            }

                            $results = WeeklyHours::selectRaw('COUNT(*) as weeks_worked, AVG(worked_minutes) as average_worked_minutes')
                                ->where('applicant_id', $applicantId)
                                ->where(function($query) use ($startWeek, $startYear, $currentWeek, $currentYear) {
                                    $query->where(function($q) use ($currentWeek, $currentYear,$startWeek) {
                                        $q->where('year', $currentYear)
                                            ->whereBetween('week_number', [$startWeek, $currentWeek]);
                                    })
                                        ->orWhere(function($q) use ($startWeek, $startYear, $currentWeek) {
                                            $q->where('year', $startYear)
                                                ->whereBetween('week_number', [$startWeek, $currentWeek]);
                                        });
                                })
                                ->first();
                            if($results->weeks_worked != 0 && $results->average_worked_minutes > 0){
                                $weeksWorkedInterval = CarbonInterval::weeks($results->weeks_worked);
                                $averageWorkedInterval = CarbonInterval::minutes($results->average_worked_minutes)->cascade();
                                return 'In '.$weeksWorkedInterval->forHumans().' gemiddeld '.$averageWorkedInterval->forHumans().' gewerkt';
                            }

                            return 'Nog niet gewerkt';

                        })
                        ->view('filament.components.view-field'),
                    Infolists\Components\ViewEntry::make('Voorkeurstaal')
                        ->getStateUsing(function ($record) {
                            if($record?->flexappData?->applicationProfile?->language) {
                                return $record->flexappData->applicationProfile->language;
                            }

                            return $record->preferred_language;
                        })
                        ->view('filament.components.view-field'),
                ]),
        ];
    }

    private function consentInfo(): array
    {
        return [
            Infolists\Components\Fieldset::make('Consent')
                ->relationship('flexappData.info')
                ->schema([
                    Infolists\Components\ViewEntry::make('whatsapp_consent')
                        ->columnSpan(2)
                        ->view('filament.components.view-field'),
                ]),
        ];
    }

    private function dataCheckActions(): array
    {
        return [
            Infolists\Components\Fieldset::make('Gegevens controleren')
                ->columns(1)
                ->schema([
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('Gegevens kloppen')
                            ->extraAttributes(['class' => 'fi-ac-btn-action--single-action fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->outlined(fn ($record) => !$record->flexappData->info->data_checked_by_recruiter)
                            ->requiresConfirmation()
                            ->form([
                                ...$this->contractForm()
                            ])
                            ->action(function (ContractController $contract, $record, EasyflexService $efService, array $data) {
                                $record->flexappData->info->data_checked_by_recruiter = true;
                                $record->flexappData->info->save();

                                $step5FinishedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 5, true);
                                JetApplicantEnrollmentActions::finishStep($record->applicant_id, 11, false, $step5FinishedAt);

                                $contract = $contract->generateContract($record, $data);

                                if($record->easyflex_id == null){
                                    $efService->storeEasyflexUser($record);
                                }
                                if($contract){
                                    $record->pre_contract_hourly_wage = $data['wage_per_hour'];
                                    $record->save();
                                    return Notification::make()
                                        ->title('Het contract is verstuurd.')
                                        ->persistent()
                                        ->success()
                                        ->send();
                                }else{
                                    return Notification::make()
                                        ->title('Het versturen van het contract is mislukt.')
                                        ->persistent()
                                        ->danger()
                                        ->send();
                                }
                            }),
                    ])
                ])
        ];
    }

    private function uitdienstForm(): array
    {
        $efService = new EasyflexService;
        $contracts = $efService->getContracts($this->record);
        $contractItems = [];
        if($contracts){
            $contractItems = [];
            foreach($contracts as $contract)
            {
                $formattedEndDate = '-';
                $startdate = $contract->fw_arbeidscontract_contractaanvang->date;
                $formattedStartDate = Carbon::parse($startdate)->format('d-m-y');
                if(isset($contract->fw_arbeidscontract_contracteinde->date)){
                    $enddate = $contract->fw_arbeidscontract_contracteinde->date;
                    $formattedEndDate = Carbon::parse($enddate)->format('d-m-y');
                }
                $contractItems = [$contract->fw_arbeidscontract_contract => 'Startdatum: '.$formattedStartDate.' / Einddatum: '.$formattedEndDate];
            }
        }

        return [
            Select::make('reason')
                ->label('Reden')
                ->options([
                    'termination_during_probation' => 'Opzegging proeftijd',
                    'confirmation_resignation' => 'Zelf ontslag',
                    'immediate_dismissal' => 'Ontslag op staande voet',
                    'residence_expired' => 'Verblijfsvergunning verlopen'
                ])
                ->required(),
            Select::make('contract_number')
                ->label('Contract')
                ->options($contractItems)
                ->required(),
            DatePicker::make('end_contract_per_date')
                ->label('Uitdienst per')
                ->default('today')
                ->required(),
            Checkbox::make('skip_email')
                ->label('Geen email versturen')
        ];
    }

    private function contractForm(bool $isConcept = false): array
    {
        return [
            Placeholder::make('')
                ->content(new HtmlString('<div class="mb-4 px-4 py-4 bg-danger-100 border-l-4 border-danger-500"><strong>Let op!:</strong> De Applicant had aangegeven meer dan 3 contracten te hebben gekregen. En heeft dus recht op een onbepaalde tijd contract.</div>'))
                ->hidden(fn ($record) => ($record->flexappData?->successiveEmployership?->number_of_contracts <= 2))
                ->hiddenLabel()
            ,

            Select::make('contract_type')
                ->label('Contract type')
                ->options([
                    'contract' => 'Eerste contract',
                    'contract-wijziging' => 'Contract wijziging',
                    'obt_contract' => 'Onbepaalde tijd contract'
                ])
                ->default(function ($record): string {
                    if ($record->flexappData?->successiveEmployership?->number_of_contracts >= 3) {
                        return 'obt_contract';
                    }

                    return 'contract';
                })
                ->required()
                ->live()
                ->afterStateUpdated(function ($set, $get, $record) {
                    $startDate = Carbon::parse($record->flexappData->info->start_date);
                    $today = Carbon::today();
                    $minimumDate = $startDate->isPast() ? $today : $startDate;
                    $startdate = $minimumDate->format('Y-m-d');

                    if ($get('contract_type') === 'obt_contract') {
                        $set('contract_end_date', null);
                        return;
                    }

                    // Randstad and numberOfContract < 3 = 12 months
                    if($record->is_import === true && $record->flexappData?->successiveEmployership?->number_of_contracts < 3) {
                        $set('contract_end_date', Carbon::parse($startdate)->addMonths(12)->subDay()->toDateString());
                        return;
                    }

                    // no experience = 7 months, experience = 4 months
                    if($record->previous_experience == true){
                        $set('contract_end_date', Carbon::parse($startdate)->addMonths(4)->subDay()->toDateString());
                        return;
                    }

                    $set('contract_end_date', Carbon::parse($startdate)->addMonths(7)->subDay()->toDateString());
                })
            ,

            Select::make('function')
                ->label('Functie')
                ->options([
                    'Courier' => 'Courier',
                    'Courier Captain' => 'Courier Captain'
                ])
                ->default(function ($record): string {
                    if($record->pre_contract_function){
                        return $record->pre_contract_function;
                    }
                    return 'Courier';
                })
                ->live()
                ->afterStateUpdated(function ($set, $get, $record) {
                    $startdate = $get('contract_start_date');
                    $function = $get('function');
                    $dob = $record->flexappData->personalData->date_of_birth;
                    $set('wage_per_hour', $this->getUurloon($startdate, $function, $dob));
                })
                ->required(),

            Placeholder::make('')
                ->content(new HtmlString('<div class="px-4 py-4 bg-info-100 border-l-4 border-blue-500 text-base">De kandidaat is een werkstudent en mag daarom maximaal 12 contract uren hebben</div>'))
                ->hidden(fn ($record) => !$record->flexappData->info->twv_is_student)
                ->hiddenLabel(),

            Select::make('contract_hours')
                ->label('Contract Uren')
                ->options(function ($record) use ($isConcept): array {
                    $options = [
                        6 => '6 uur',
                        9 => '9 uur',
                        12 => '12 uur',
                        16 => '16 uur',
                        24 => '24 uur',
                        32 => '32 uur',
                        40 => '40 uur'
                    ];
                    if ($record->flexappData->info->twv_is_student) {
                        $options = [
                            6 => '6 uur',
                            9 => '9 uur',
                            12 => '12 uur'
                        ];
                    }
                    if ($isConcept) {
                        $options[0] = '0 uur (voor Asielzoekers TWV)';
                    }
                    return $options;
                })
                ->default(function ($record): string {
                    return ($record->flexappData->info->twv_is_student && $record->pre_contract_minimum_hours > 12) ? 12 : (int)$record->pre_contract_minimum_hours;
                })
                ->required(),

            Toggle::make('is_wage_with_vacation_allowance')
                ->label('Vakantiegeld direct uitbetalen')
                ->default(function ($record): bool {
                    return (bool)$record->flexappData->financialDetails->is_wage_with_vacation_allowance;
                })
                ->onColor('success')
                ->offColor('danger'),

            Toggle::make('is_wage_with_vacation_days')
                ->label('Bovenwettelijke vakantiedagen uitbetalen')
                ->default(function ($record): bool {
                    return (bool)$record->flexappData->financialDetails->is_wage_with_vacation_days;
                })
                ->onColor('success')
                ->offColor('danger'),

            TextInput::make('delivery_area')
                ->formatStateUsing(function ($record): string {
                    return $record->delivery_area;
                })
                ->label('Bezorggebied'),

            Toggle::make('english')
                ->formatStateUsing(function ($record): string {
                    return $record?->flexappData?->applicationProfile?->language == "Engels";
                })
                ->label('Engelstalig')
                ->default(false)
                ->onColor('success')
                ->offColor('danger'),

            DatePicker::make('contract_start_date')
                ->label('Startdatum')
                ->minDate(function ($record): ?string {
                    if (auth()->user()->hasRole('super_admin')) { return null; }
                    $startDate = Carbon::parse($record->flexappData->info->start_date);
                    $today = Carbon::today();
                    $minimumDate = $startDate->isPast() ? $today : $startDate;
                    return $minimumDate->format('Y-m-d');
                })
                ->default(function ($record): string {
                    //check if start date is in the past, then today is minimum
                    $startDate = Carbon::parse($record->flexappData->info->start_date);

                    $today = Carbon::today();
                    $minimumDate = $startDate->isPast() ? $today : $startDate;
                    return $minimumDate->format('Y-m-d');
                })
                ->live()
                ->afterStateUpdated(function ($set, $get, $record) {
                    $startdate = $get('contract_start_date');
                    $function = $get('function');
                    $set('wage_per_hour', $this->getUurloon($startdate, $function, $record->flexappData->personalData->date_of_birth));
                    // no probation period = 4 months, probation period = 7 months, randstad (not obt) = 12 months
                    if ($record->is_import === true && $record->flexappData?->successiveEmployership?->number_of_contracts < 3) {
                        $set('contract_end_date', Carbon::parse($startdate)->addMonths(12)->subDay()->toDateString());
                    } else if ($get('probation_period') == true) {
                        $set('contract_end_date', Carbon::parse($startdate)->addMonths(7)->subDay()->toDateString());
                    } else {
                        $set('contract_end_date', Carbon::parse($startdate)->addMonths(4)->subDay()->toDateString());
                    }
                })
                ->required(),

            Toggle::make('probation_period')
                ->label('Proeftijd')
                ->formatStateUsing(function ($record): string {
                    if($record->previous_experience == true){
                        return false;
                    }
                    return true;
                })
                ->onColor('success')
                ->offColor('danger')
                ->live()
                ->afterStateUpdated(function ($set, $get, $record) {
                    $startdate = $get('contract_start_date');
                    // no probation period = 4 months, probation period = 7 months, Randstad (not obt) = 12 months
                    if ($record->is_import === true && $record->flexappData?->successiveEmployership?->number_of_contracts < 3) {
                        $set('contract_end_date', Carbon::parse($startdate)->addMonths(12)->subDay()->toDateString());
                    } else if ($get('probation_period') == true) {
                        $set('contract_end_date', Carbon::parse($startdate)->addMonths(7)->subDay()->toDateString());
                    }else{
                        $set('contract_end_date', Carbon::parse($startdate)->addMonths(4)->subDay()->toDateString());
                    }
                }),

            DatePicker::make('contract_end_date')
                ->helperText('Standaard 4 of 7 maanden na de startdatum afhankelijk van de proeftijd')
                ->label('Einddatum')
                ->default(function ($set, $get, $record) {
                    $startDate = Carbon::parse($record->flexappData->info->start_date);
                    $today = Carbon::today();
                    $minimumDate = $startDate->isPast() ? $today : $startDate;
                    $startdate = $minimumDate->format('Y-m-d');

                    if ($get('contract_type') === 'obt_contract') {
                        return null;
                    }

                    // Randstad (not obt) = 12 month
                    if ($record->is_import === true && $record->flexappData?->successiveEmployership?->number_of_contracts < 3){
                        return Carbon::parse($startdate)->addMonths(12)->subDay()->toDateString();
                    }

                    // no experience = 7 months, experience = 4 months
                    if($record->previous_experience == true){
                        return Carbon::parse($startdate)->addMonths(4)->subDay()->toDateString();
                    }

                    return Carbon::parse($startdate)->addMonths(7)->subDay()->toDateString();
                })
                ->live()
            ,

            TextInput::make('wage_per_hour')
                ->label('Uurloon')
                ->formatStateUsing(function ($record, $get): string {
                    return $this->getUurloon($get('contract_start_date'), $get('function'), $record->flexappData->personalData->date_of_birth);
                })
                ->helperText('Het uurloon wordt automatisch berekend op basis van de leeftijd van de kandidaat.')
                ->required(),

            CheckboxList::make('total_forms')
                ->label('Welke formulieren moeten worden verstuurd?')
                ->options([
                    'contract' => 'Contract',
                    'loonheffing' => 'Loonheffing',
                    'loan_agreement' => 'Loan agreement',
                ])
                ->default(['contract', 'loonheffing', 'loan_agreement']),
        ];
    }

    private function changeDeliveryAreaForm (): array
    {
        return [
            Placeholder::make('')
                ->content(new HtmlString('<div class="mb-4 px-4 py-4 bg-danger-100 border-l-4 border-danger-500"><strong>Let op!:</strong> Pas het bezorggebied alleen aan in overleg met Just Eat.</div>')),

            Select::make('delivery_area')
                ->label('Bezorggebied')
                ->options([
                    'Alkmaar' => 'Alkmaar',
                    'Almelo' => 'Almelo',
                    'Almere' => 'Almere',
                    'Alphen aan den Rijn' => 'Alphen aan den Rijn',
                    'Amersfoort' => 'Amersfoort',
                    'Amsterdam' => 'Amsterdam',
                    'Apeldoorn' => 'Apeldoorn',
                    'Arnhem' => 'Arnhem',
                    'Breda' => 'Breda',
                    'Delft' => 'Delft',
                    'Den Bosch' => 'Den Bosch',
                    'Den Haag' => 'Den Haag',
                    'Deventer' => 'Deventer',
                    'Dordrecht' => 'Dordrecht',
                    'Ede' => 'Ede',
                    'Eindhoven' => 'Eindhoven',
                    'Enschede' => 'Enschede',
                    'Gouda' => 'Gouda',
                    'Groningen' => 'Groningen',
                    'Haarlem' => 'Haarlem',
                    'Heerlen' => 'Heerlen',
                    'Helmond' => 'Helmond',
                    'Hengelo' => 'Hengelo',
                    'Hilversum' => 'Hilversum',
                    'Hoofddorp' => 'Hoofddorp',
                    'Leeuwarden' => 'Leeuwarden',
                    'Leiden' => 'Leiden',
                    'Maastricht' => 'Maastricht',
                    'Nijmegen' => 'Nijmegen',
                    'Rotterdam' => 'Rotterdam',
                    'Tilburg' => 'Tilburg',
                    'Utrecht' => 'Utrecht',
                    'Venlo' => 'Venlo',
                    'Waalwijk' => 'Waalwijk',
                    'Zaandam' => 'Zaandam',
                    'Zoetermeer' => 'Zoetermeer',
                    'Zwolle' => 'Zwolle',
                ])
                ->default(function ($record): string {
                    return $record->delivery_area;
                })
        ];
    }

    private function sendContractActions(): array
    {
        $record = $this->getRecord();
        return [
            Infolists\Components\Fieldset::make('Contracten')
                ->columns(1)
                ->schema([
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('send contract')
                            ->label('Contract verzenden')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->outlined()
                            ->requiresConfirmation()
                            ->form([
                                ...$this->contractForm()
                            ])
                            ->action(function (ContractController $contractController, $record, EasyflexService $efService, array $data) {
                                $contract = $contractController->generateContract($record, $data);

                                if($record->easyflex_id === null){
                                    $efService->storeEasyflexUser($record);
                                }

                                if($contract){
                                    $record->pre_contract_hourly_wage = $data['wage_per_hour'];
                                    $record->save();
                                    return Notification::make()
                                        ->title('Het contract is verstuurd.')
                                        ->persistent()
                                        ->success()
                                        ->send();
                                }else{
                                    return Notification::make()
                                        ->title('Het versturen van het contract is mislukt.')
                                        ->persistent()
                                        ->danger()
                                        ->send();
                                }
                            })
                    ]),
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('download concept')
                            ->label('Download concept')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->outlined()
                            ->requiresConfirmation()
                            ->form([
                                ...$this->contractForm(true)
                            ])
                            ->action(function (ContractController $contract, $record, array $data) {
                                $data['total_forms'] = ['contract'];
                                $concept_contract = $contract->generateContract($record, $data, true);
                                if($concept_contract){
                                    return response()->download(storage_path('/app/'.$concept_contract))->deleteFileAfterSend(true);
                                }
                            })
                    ]),
                    ...($record->renewalStatus?->step_in_process == 7 ? [ Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('Complete renewal')
                            ->label('Verlenging afronden')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('success')
                            ->outlined()
                            ->requiresConfirmation()
                            ->action(function ($record) {
                                JetActions::timeApplicantRenewalStep($record->applicant_id, 11, true);
                            })
                    ])] : [])
                ])
        ];
    }

    public function getUurloon($startdate, $function, $dob)
    {
        $dob = Carbon::parse($dob);
        $startdate = Carbon::parse($startdate);
        $requestedAge = (int) $dob->diffInYears($startdate);
        $format_startdate = Carbon::parse($startdate)->format('Y-m-d');

        $age = ($requestedAge > 21) ? 21 : $requestedAge;
        $loonInfo = LooncomponentPercentage::where('age', $age)
            ->where('function', $function)
            ->whereDate('start_date', '<=', $format_startdate)
            ->orderBy('start_date', 'desc')->first();

        if ($loonInfo) {
            return $loonInfo->wage_per_hour;
        } else {
            return "No applicable wage found.";
        }
    }

    private function applicantFunction(): array
    {
        return [
            Infolists\Components\Fieldset::make('Functie')
                ->columns(1)
                ->schema([
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('Courier')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->outlined(fn ($record) => $record->pre_contract_function !== 'Courier')
                            ->disabled(fn ($record) => $record->pre_contract_function === 'Courier')
                            ->requiresConfirmation()
                            ->action(function (array $data, $record) {
                                $record->pre_contract_function = 'Courier';
                                $record->save();
                            }),

                        Infolists\Components\Actions\Action::make('Courier captain')
                            ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                            ->color('info')
                            ->iconPosition('after')
                            ->outlined(fn ($record) => $record->pre_contract_function === 'Courier')
                            ->disabled(fn ($record) => $record->pre_contract_function !== 'Courier')
                            ->requiresConfirmation()
                            ->action(function (array $data, $record) {
                                $record->pre_contract_function = 'Courier Captain';
                                $record->save();
                            }),
                    ]),
                ]),


            ...((auth()->user()->hasRole('super_admin') && isset($this->getRecord()->flexappData->info)) ? [
            Infolists\Components\Fieldset::make('Bezorgen')
                ->columns(1)
                ->schema([
                    Infolists\Components\Fieldset::make('Bezorggebied')
                        ->columns(1)
                        ->schema([
                            Infolists\Components\Actions::make([
                                Infolists\Components\Actions\Action::make('change_delivery_area')
                                    ->label('Bezorggebied veranderen')
                                    ->extraAttributes(['class' => 'fi-ac-btn-action--single-action fi-ac-btn-action-chip'])
                                    ->iconPosition('after')
                                    ->color('info')
                                    ->outlined()
                                    ->requiresConfirmation()
                                    ->form([
                                        ...$this->changeDeliveryAreaForm()
                                    ])
                                    ->action(function ($record, array $data) {
                                            $record->delivery_area = $data['delivery_area'];
                                            $record->save();
                                            return Notification::make()
                                                ->title('Het bezorggebied is aangepast naar ' . $data['delivery_area'] . '.')
                                                ->persistent()
                                                ->success()
                                                ->send();
                                    })
                            ])
                        ]),
                    Infolists\Components\Fieldset::make('Rijbewijsvereiste')
                        ->columns(1)
                        ->schema([
                            Infolists\Components\Actions::make([
                                Infolists\Components\Actions\Action::make('driving_license_required_false')
                                    ->label('Nee')
                                    ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                    ->color('info')
                                    ->iconPosition('after')
                                    ->outlined(fn ($record) => $record->flexappData->info['driving_license_required'] === true)
                                    ->disabled(fn ($record) => $record->flexappData->info['driving_license_required'] === false)
                                    ->requiresConfirmation()
                                    ->modalHeading('Rijbewijsvereiste weghalen?')
                                    ->modalDescription(new HtmlString('<div class="mb-4 px-4 py-4 bg-danger-100 border-l-4 border-danger-500"><strong>Let op!:</strong> Pas de rijbewijsvereiste alleen aan in overleg met Just Eat.</div>'))
                                    ->action(function (array $data, $record) {
                                        $record->flexappData->info['driving_license_required'] = false;
                                        $userService = new UserService;
                                        $userService->updateUser($record->flexapp_id, ['info' => ['driving_license_required' => false]]);
                                    }),

                                Infolists\Components\Actions\Action::make('driving_license_required_true')
                                    ->label('Ja')
                                    ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                    ->color('info')
                                    ->iconPosition('after')
                                    ->outlined(fn ($record) => $record->flexappData->info['driving_license_required'] === false)
                                    ->disabled(fn ($record) => $record->flexappData->info['driving_license_required'] === true)
                                    ->requiresConfirmation()
                                    ->modalHeading('Rijbewijs vereist maken?')
                                    ->modalDescription(new HtmlString('<div class="mb-4 px-4 py-4 bg-danger-100 border-l-4 border-danger-500"><strong>Let op!:</strong> Pas de rijbewijsvereiste alleen aan in overleg met Just Eat.</div>'))
                                    ->action(function (array $data, $record) {
                                        $userService = new UserService;
                                        $userService->updateUser($record->flexapp_id, ['info' => ['driving_license_required' => true]]);
                                    }),
                                Infolists\Components\Actions\Action::make('upload_dl')
                                    ->label('Sync Rijbewijs EF')
                                    ->extraAttributes(['class' => 'fi-ac-btn-action-chip'])
                                    ->iconPosition('after')
                                    ->color('info')
                                    ->outlined()
                                    ->requiresConfirmation()
                                    ->disabled(fn(JetApplicant $record) => $record->easyflex_id === null)
                                    ->action(function ($record, EasyflexService $easyflexService) {
                                        $response = $easyflexService->storeDriverLicense($record);
                                        $failed = ! $response instanceof \Illuminate\Http\JsonResponse ||
                                            $response->getStatusCode() !== 200 ||
                                            ! data_get($response->getData(), 'success');
                                        if ($failed) {
                                            $message = data_get($response->getData(), 'message', 'Versturen van rijbewijs mislukt.');
                                            return Notification::make()
                                                ->title('Rijbewijs niet verstuurd')
                                                ->body($message)
                                                ->persistent()
                                                ->danger()
                                                ->send();
                                        }
                                        return Notification::make()
                                            ->body('Rijbewijs verstuurd')
                                            ->persistent()
                                            ->success()
                                            ->send();
                                    })
                            ]),
                        ])
                ]),
                ] : [])
        ];
    }

    private function storeEasyflexActions(): array
    {
        return [
            Infolists\Components\Fieldset::make('Easyflex')
                ->columns(1)
                ->schema([
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('Sync naar easyflex')
                            ->extraAttributes(['class' => 'fi-ac-btn-action--single-action fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->outlined()
                            ->requiresConfirmation()
                            ->action(function (ContractController $contract, $record, EasyflexService $efService) {
                                if($record->easyflex_id == null){
                                    $efService->storeEasyflexUser($record);
                                }
                            }),
                    ])
                ])
        ];
    }

    private function easyflexActions(): array
    {
        return [
            Infolists\Components\Fieldset::make('Easyflex')
                ->columns(1)
                ->schema([
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('Inschrijving easyflex afgerond')
                            ->extraAttributes(['class' => 'fi-ac-btn-action--single-action fi-ac-btn-action-chip'])
                            ->iconPosition('after')
                            ->color('info')
                            ->outlined()
                            ->requiresConfirmation()
                            ->action(function (ContractController $contract, $record, EasyflexService $efService) {
                                $step8FinishedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 8, true);
                                $step10StartedAt = JetActions::timeApplicantEnrollmentStep($record->applicant_id, 10);
                                JetApplicantEnrollmentActions::finishStep($record->applicant_id, 14, false, $step8FinishedAt);
                                JetApplicantEnrollmentActions::startStep($record->applicant_id, 16, false, $step10StartedAt);
                            }),
                    ])
                ])
        ];
    }

    private function notes(): array
    {
        $record = $this->getRecord();
        $notes = array_filter(explode('|SEP|', $record->enrollmentStatus->notes));

        $components = [];

        foreach($notes as $key => $note) {
            $components = [
                ...$components,

                Infolists\Components\Split::make([
                    Infolists\Components\TextEntry::make('')
                        ->extraAttributes(['class' => 'notes__item'])
                        ->getStateUsing(fn () => $note)
                        ->html(),

                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make($key)
                            ->label('Verwijder')
                            ->link()
                            ->color('danger')
                            ->requiresConfirmation()
                            ->action(function($record) use ($key) {
                                $notes = explode('|SEP|', $record->enrollmentStatus->notes);

                                unset($notes[$key]);

                                $record->enrollmentStatus->notes = implode('|SEP|', $notes);
                                $record->enrollmentStatus->save();
                                #region New
                                $record->enrollmentStatusNew->notes = implode('|SEP|', $notes);
                                $record->enrollmentStatusNew->save();
                                #endregion
                            })
                    ])->grow(false)
                ])
            ];
        }

        return [
            Infolists\Components\Fieldset::make('Notities')
                ->columns(1)
                ->extraAttributes(['class' => 'notes'])
                ->schema([
                    Infolists\Components\Actions::make([
                        Infolists\Components\Actions\Action::make('Notitie toevoegen')
                            ->color('info')
                            ->outlined()
                            ->extraAttributes(['class' => 'mb-6'])
                            ->form([
                                Textarea::make('note')
                                    ->label('Notitie')
                                    ->rows(4)
                                    ->required()
                            ])
                            ->requiresConfirmation()
                            ->modalDescription()
                            ->action(function(array $data, $record) {
                                $date = Carbon::now('Europe/Amsterdam')->format('H:i d-m-Y');

                                $record->enrollmentStatus->notes = implode('|SEP|', [
                                    '<p>' . $date . '</p>' . $data['note'],
                                    ...explode('|SEP|', $record->enrollmentStatus->notes)
                                ]);
                                $record->enrollmentStatus->save();
                                #region New
                                $record->enrollmentStatusNew->notes = implode('|SEP|', [
                                    '<p>' . $date . '</p>' . $data['note'],
                                    ...explode('|SEP|', $record->enrollmentStatus->notes)
                                ]);
                                $record->enrollmentStatusNew->save();
                                #endregion
                            })
                    ]),

                    ...$components,
                ])
        ];
    }
}
