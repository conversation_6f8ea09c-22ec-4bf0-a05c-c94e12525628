<?php

namespace App\Filament\Resources\JetApplicantResource\Pages;

use App\Filament\Resources\JetApplicantResource;
use App\Http\Controllers\ExportController;
use Filament\Actions\Action;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListJetApplicants extends ListRecords
{
    protected static string $resource = JetApplicantResource::class;

    protected static ?string $title = 'All applicants';

    protected function getHeaderActions(): array
    {
        return [
            Action::make('Download gegevens')
                ->extraAttributes(['class' => 'mb-4'])
                ->icon('heroicon-m-arrow-down-circle')
                ->color('gray')
                ->requiresConfirmation()
                ->action(function (ExportController $export) {
                    $file = $export->downloadData(null);
                    return response()->download($file);
                })
        ];
    }

    public function getTableQuery(): Builder
    {
        $query = JetApplicantResource::getEloquentQuery()->getModel()->newQuery();
        
        return $query->whereRaw('is_test IS NOT true');
    }
}
