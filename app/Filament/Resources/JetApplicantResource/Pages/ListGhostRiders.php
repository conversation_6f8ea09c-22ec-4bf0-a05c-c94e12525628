<?php

namespace App\Filament\Resources\JetApplicantResource\Pages;

use App\Filament\Resources\JetApplicantResource;
use Carbon\Carbon;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListGhostRiders extends ListRecords
{
    protected static string $resource = JetApplicantResource::class;

    protected static ?string $title = 'Ghost Riders';

    public function getTableQuery(): Builder
    {
        $query = JetApplicantResource::getEloquentQuery()->getModel()->newQuery();

        return $query
            ->whereHas('enrollmentStatus', function(Builder $query) {
                return $query
                    ->whereRaw('on_hold IS NOT true')
                    ->where([
                        ['agency_status', '=', 'Approved'],
                        ['step_in_process', '=', 10]
                    ])
                    ;
            })
            ->whereDate('pre_contract_start_date', '<=', Carbon::today()->subWeeks(2))
            ->leftJoin('urenbestand', 'applicants.scoober_id', '=', 'urenbestand.driver_id')
            ->whereNull('urenbestand.driver_id')
            ->whereDoesntHave('employeeOffboarding')
            ->select('applicants.*')
            ;
    }
}
