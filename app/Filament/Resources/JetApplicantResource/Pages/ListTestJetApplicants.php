<?php

namespace App\Filament\Resources\JetApplicantResource\Pages;

use App\Filament\Resources\JetApplicantResource;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListTestJetApplicants extends ListRecords
{
    protected static string $resource = JetApplicantResource::class;

    protected static ?string $title = 'Test applicants';

    public function getTableQuery(): Builder
    {
        $query = JetApplicantResource::getEloquentQuery()->getModel()->newQuery();

        return $query->where('is_test', true);
    }
}
