<?php

namespace App\Filament\Resources\JetApplicantResource\Pages;

use App\Filament\Resources\JetApplicantResource;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListOnHoldJetApplicants extends ListRecords
{
    protected static string $resource = JetApplicantResource::class;

    protected static ?string $title = 'On hold applicants';

    public function getTableQuery(): Builder
    {
        $query = JetApplicantResource::getEloquentQuery()->getModel()->newQuery();

        return $query
            ->whereRaw('is_test IS NOT true')
            ->whereHas('enrollmentStatus', function(Builder $query) {
                return $query->where('on_hold', true);
            })
        ;
    }

    public function getTabs(): array
    {
        $onHoldApplicantsQuery = $this->getTableQuery()->clone()->whereRaw('is_import IS NOT true');
        $onHoldApplicantsRandstadQuery = $this->getTableQuery()->clone()->where('is_import', true);
        return [
            'Alle' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $onHoldApplicantsQuery)
                ->badge($onHoldApplicantsQuery->count())
            ,
            'Randstad' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $onHoldApplicantsRandstadQuery)
                ->badge($onHoldApplicantsRandstadQuery->count())
            ,
        ];
    }
}
