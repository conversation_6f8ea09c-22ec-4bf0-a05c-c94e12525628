<?php

namespace App\Filament\Resources\JetApplicantResource\Pages;

use App\Filament\Resources\JetApplicantResource;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListRejectedJetApplicants extends ListRecords
{
    protected static string $resource = JetApplicantResource::class;

    protected static ?string $title = 'Rejected applicants';

    public function getTableQuery(): Builder
    {
        $query = JetApplicantResource::getEloquentQuery()->getModel()->newQuery();

        return $query
            ->whereRaw('is_test IS NOT true')
            ->whereHas('enrollmentStatus', function(Builder $query) {
                return $query
                    ->whereRaw('on_hold IS NOT true')
                    ->where('agency_status', '=', 'Rejected')
                ;
            })
        ;
    }

    public function getTabs(): array
    {
        $rejectedApplicantsQuery = $this->getTableQuery()->clone()->whereRaw('is_import IS NOT true');
        $rejectedApplicantsRandstadQuery = $this->getTableQuery()->clone()->where('is_import', true);
        return [
            'Alle' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $rejectedApplicantsQuery)
                ->badge($rejectedApplicantsQuery->count())
            ,
            'Randstad' => Tab::make()
                ->modifyQueryUsing(fn (Builder $query) => $rejectedApplicantsRandstadQuery)
                ->badge($rejectedApplicantsRandstadQuery->count())
            ,
        ];
    }
}
