<?php

namespace App\Filament\Resources\EmployeeOffboardingResource\Pages;

use App\Filament\Resources\EmployeeOffboardingResource;
use Filament\Actions;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class ListEmployeeOffboardings extends ListRecords
{
    protected static string $resource = EmployeeOffboardingResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }

    public function getTabs(): array
    {
        $tabs = [
            'alle' => Tab::make()->badge($this->getModel()::count()),
            'lopend' => Tab::make()
                ->modifyQueryUsing(function (Builder $query) {
                    $query->where('is_completed', false);
                })
                ->badge(function () {
                    $totalCount = DB::table('employee_offboarding')->where('is_completed', false)->count();
                    return $totalCount;
                }),
            'afgerond' => Tab::make()
                ->modifyQueryUsing(function (Builder $query) {
                    $query->where('is_completed', true);

                })
                ->badge(function () {
                    $totalCount = DB::table('employee_offboarding')->where('is_completed', true)->count();
                    return $totalCount;
                }),
        ];
        return $tabs;
    }
}
