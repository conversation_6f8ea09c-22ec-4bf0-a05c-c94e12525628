<?php

namespace App\Filament\Resources;

use App\Filament\Resources\UrenbestandResource\Pages;
use App\Filament\Resources\UrenbestandResource\RelationManagers;
use App\Models\Urenbestand;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class UrenbestandResource extends Resource
{
    protected static ?string $model = Urenbestand::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                //
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUrenbestand::route('/'),
            'create' => Pages\CreateUrenbestand::route('/create'),
            'edit' => Pages\EditUrenbestand::route('/{record}/edit'),
        ];
    }
}
