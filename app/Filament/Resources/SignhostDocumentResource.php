<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SignhostDocumentResource\Pages;
use App\Models\Filament\SignhostExpiredDocument;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class SignhostDocumentResource extends Resource
{
    protected static ?string $model = SignhostExpiredDocument::class;

    protected static ?string $navigationGroup = 'Controle';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('status_text'),
                Forms\Components\TextInput::make('transaction_id'),
                Forms\Components\TextInput::make('expired_at'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultGroup('transaction_id')
            ->columns([
                Tables\Columns\TextColumn::make('reference'),
                Tables\Columns\TextColumn::make('status_text'),
                Tables\Columns\TextColumn::make('transaction_id')
                    ->searchable()
                    ->copyable(),
                Tables\Columns\IconColumn::make('send_email_notifications')->boolean(),
                Tables\Columns\IconColumn::make('seal')->boolean(),
                Tables\Columns\TextColumn::make('Expired at')
                    ->getStateUsing(fn (SignhostExpiredDocument $record) => $record
                        ->created_at->addDays($record->days_to_expire)
                        ->format('d-m-Y H:i:s')
                    ),
            ])
            ->paginated()
            ->deferLoading()
            ->filters([
                Tables\Filters\Filter::make('status')
                    ->form([
                        Forms\Components\Select::make('status')
                            ->options([
                                30 => 'Getekend',
                                40 => 'Afgekeurd',
                                50 => 'Verlopen',
                                60 => 'Geannuleerd',
                                70 => 'Failed',
                            ])
                    ])
                    ->query(fn (Builder $query, array $data): Builder => $query
                        ->when($status = data_get($data, 'status'), fn (Builder $query, $data) => $query->where('status', $status))
                    ),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSignhostDocuments::route('/'),
            'view' => Pages\ViewSignhostDocument::route('/{record}'),
        ];
    }
}
