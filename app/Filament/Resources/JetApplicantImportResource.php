<?php

namespace App\Filament\Resources;

use App\Filament\Resources\JetApplicantImportResource\Pages;

use Filament\Actions\Imports\Models\Import;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Actions\Action;

class JetApplicantImportResource extends Resource
{
    protected static ?string $model = Import::class;
    protected static bool $shouldRegisterNavigation = false;

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('file_name')
                    ->label('Bestandsnaam')
                    ->searchable()
                    ->sortable()
                ,
                Tables\Columns\TextColumn::make('successful_rows')
                    ->label('Succesvolle rijen')
                ,
                Tables\Columns\TextColumn::make('')
                    ->label('Mislukte rijen')
                    ->getStateUsing(fn (Import $import): int => $import->getFailedRowsCount())
                ,
                Tables\Columns\TextColumn::make('total_rows')
                    ->label('Totaal aantal rijen')
                ,
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Geïmporteerd bij')
                    ->dateTime(null, 'Europe/Amsterdam')
                    ->sortable()
                ,
            ])
            ->actions([
                Action::make('Mislukte rijen downloaden')
                    ->url(fn (Import $import): string => route('filament.imports.failed-rows.download', ['import' => $import], absolute: false), true)
                    ->icon('heroicon-m-arrow-down-tray')
                    ->color('info')
                    ->disabled(fn (Import $import): bool => ($import->getFailedRowsCount() <= 0 || !$import->failedRows()->first()))
                ,
            ])
            ->recordUrl(null)
            ->poll('5s')
            ->defaultSort('created_at', 'desc')
        ;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListJetApplicantImports::route('/')
        ];
    }
}
