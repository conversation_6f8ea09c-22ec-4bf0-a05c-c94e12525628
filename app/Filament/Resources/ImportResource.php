<?php

namespace App\Filament\Resources;

use App\Actions\CsvActions;
use App\Filament\Resources\ImportResource\Pages;
use App\Filament\Resources\ImportResource\RelationManagers\UrenbestandRelationManager;
use App\Models\Export;
use App\Models\ImportExtended;
use App\Models\LoonComponent;
use Filament\Actions\Imports\Models\Import;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;

class ImportResource extends Resource
{
    protected static ?string $model = ImportExtended::class;
    protected static bool $shouldRegisterNavigation = false;

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('file_name')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('total_rows')
                    ->sortable(),
                Tables\Columns\TextColumn::make('exported')
                    ->getStateUsing(function (Import $record) {
                        $export = Export::where(function ($query) use ($record) {
                            $query->where(function ($query) use ($record) {
                                $query->where('import_id', $record->id)
                                ->orWhereJsonContains('import_id', $record->id);
                            })->where('file_name', 'NOT LIKE', 'plaatsingen/plaatsingen%');
                        })->first();

                        if($export){
                            return 'Exported';
                        }
                        return 'Not exported';
                    }),
                Tables\Columns\TextColumn::make('created_at')
                    ->sortable()
                    ->getStateUsing(function (Import $record) {
                        return $record->created_at->format('d-m-Y H:i:s');
                    })
            ])
            ->defaultSort('created_at', 'desc')
            ->filters([
                SelectFilter::make('Looncomponent')
                    ->options(fn() => LoonComponent::all()->pluck('name', 'id'))
                    ->modifyQueryUsing(function (Builder $query, $state) {
                        if (! $state['value']) {
                            return $query;
                        }
                        return $query->whereHas('urenbestand', fn($query) => $query->where('loon_component_id', $state['value']));
                    }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('Maak plaatsingen')
                        ->icon('heroicon-m-user')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->action(function (Collection $records, CsvActions $csvActions) {
                            return $csvActions->exportBulkAction($records, 99, 'plaatsingen');
                        }),
                    Tables\Actions\BulkAction::make('Export declaraties')
                        ->icon('heroicon-m-document-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function (Collection $records, CsvActions $csvActions) {
                            return $csvActions->exportBulkAction($records, 3);
                        }),
                    Tables\Actions\BulkAction::make('Export uren')
                        ->icon('heroicon-m-document-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function (Collection $records, CsvActions $csvActions) {
                            return $csvActions->exportBulkAction($records, 2);
                        }),
                    Tables\Actions\BulkAction::make('Export onbetaald verlof')
                        ->icon('heroicon-m-document-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function (Collection $records, CsvActions $csvActions) {
                            return $csvActions->exportBulkAction($records, 7);
                        }),
                    Tables\Actions\BulkAction::make('Export absentie')
                        ->icon('heroicon-m-document-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function (Collection $records, CsvActions $csvActions) {
                            return $csvActions->exportBulkAction($records, 1);
                        }),
                ]),
            ]);
    }


    public static function getRelations(): array
    {
        return [
            UrenbestandRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListImports::route('/'),
            'create' => Pages\CreateImport::route('/create'),
            'edit' => Pages\EditImport::route('/{record}/edit'),
            'arbeidstijdenwet' => Pages\Arbeidstijdenwet::route('/{record}/arbeidstijdenwet'),
        ];
    }
}
