<?php

namespace App\Filament\Resources;

use App\Filament\Resources\EmployeeOffboardingResource\Pages;
use App\Filament\Resources\EmployeeOffboardingResource\RelationManagers;
use App\Models\EmployeeOffboarding;
use App\Models\JetApplicant;
use Filament\Forms;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Carbon;

class EmployeeOffboardingResource extends Resource
{
    protected static ?string $model = EmployeeOffboarding::class;
    protected static ?string $navigationIcon = 'heroicon-o-inbox-stack';


    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Toggle::make('signalering'),
                Toggle::make('processed_reserveringen')
                ->label('Reserveringen verwerkt'),
                Toggle::make('processed_in_uwv')
                ->label('Verwerkt in UWV'),
                Toggle::make('is_completed')
                ->label('Afgerond'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('applicant_id')
                    ->searchable()
                    ->url(fn (EmployeeOffboarding $record): string => route('filament.admin.resources.jet-applicant-news.view-check', ['record' => $record->applicant_id])),
                Tables\Columns\TextColumn::make('applicant.name')
                    ->searchable(['first_name', 'last_name'])
                    ->getStateUsing(function ($record) {
                        if($record->applicant->flexapp_id && isset($record->applicant->flexappData->personalData)) {
                            return $record->applicant->flexappData->personalData->first_name . ' ' . $record->applicant->flexappData->personalData->last_name;
                        }
                        return $record->applicant->first_name . ' ' . $record->applicant->last_name;
                    }),
                Tables\Columns\TextColumn::make('offboarding_date')
                    ->getStateUsing(function ($record) {
                        return $record->offboarding_date ? Carbon::createFromFormat('Y-m-d', $record->offboarding_date)->format('d-m-Y') : null;
                    })
                    ->label('Offboarding datum')
                    ->sortable(),
                Tables\Columns\TextColumn::make('reason'),
                Tables\Columns\IconColumn::make('signalering')
                    ->boolean(),
                Tables\Columns\IconColumn::make('processed_reserveringen')
                    ->label('Reserveringen verwerkt')
                    ->boolean(),
                Tables\Columns\IconColumn::make('processed_in_uwv')
                    ->label('Verwerkt in UWV')
                    ->boolean(),
                Tables\Columns\IconColumn::make('is_completed')
                    ->label('Afgerond')
                    ->boolean(),
            ])
            ->filters([
                SelectFilter::make('offboarding_reason')
                    ->options([
                        'termination_during_probation' => 'Tijdens proeftijd',
                        'confirmation_resignation' => 'Zelf ontslag',
                        'immediate_dismissal' => 'Ontslag op staande voet',
                    ]),
                Filter::make('signalering')
                    ->query(fn (Builder $query): Builder => $query->where('signalering', true))
                    ->toggle(),
                Filter::make('processed_reserveringen')
                    ->label('Reserveringen verwerkt')
                    ->query(fn (Builder $query): Builder => $query->where('processed_reserveringen', true))
                    ->toggle(),
                Filter::make('processed_in_uwv')
                    ->label('Verwerkt in UWV')
                    ->query(fn (Builder $query): Builder => $query->where('processed_in_uwv', true))
                    ->toggle(),
                Filter::make('is_completed')
                    ->label('Afgerond')
                    ->query(fn (Builder $query): Builder => $query->where('is_completed', true))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListEmployeeOffboardings::route('/'),
            'create' => Pages\CreateEmployeeOffboarding::route('/create'),
            'edit' => Pages\EditEmployeeOffboarding::route('/{record}/edit'),
        ];
    }
}
