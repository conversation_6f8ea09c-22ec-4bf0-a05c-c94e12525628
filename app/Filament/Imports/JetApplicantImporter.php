<?php

namespace App\Filament\Imports;

use Carbon\CarbonInterface;
use App\Actions\JetActions;
use App\Models\JetApplicant;
use Filament\Actions\Imports\Exceptions\RowImportFailedException;
use Filament\Actions\Imports\ImportColumn;
use Filament\Actions\Imports\Importer;
use Filament\Actions\Imports\Models\Import;
use Illuminate\Support\Carbon;
use Illuminate\Support\Str;

class JetApplicantImporter extends Importer
{
    protected static ?string $model = null;

    public function getJobConnection(): ?string
    {
        return 'database';
    }

    public function getJobQueue(): ?string
    {
        return 'jet-applicant-imports';
    }

    public static function getColumns(): array
    {
        return [
            ImportColumn::make('contact_id')
                ->guess(['Contact: Contact ID'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,
            ImportColumn::make('scoober_id')
                ->guess(['Contact: Scoober ID'])
                ->rules(['required','max:255'])
                ->requiredMapping()
            ,
            ImportColumn::make('first_name')
                ->guess(['Contact: First Name'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,
            ImportColumn::make('last_name')
                ->guess(['Contact: Last Name'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,
            ImportColumn::make('email')
                ->guess(['Contact: Email'])
                ->rules(['required', 'email', 'max:255'])
                ->requiredMapping()
            ,
            ImportColumn::make('mobile_phone')
                ->guess(['Contact: Mobile'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,
            ImportColumn::make('phone')
                ->guess(['Contact: Phone'])
                ->rules(['max:255'])
            ,

            ImportColumn::make('emergency_contact_name')
                ->guess(['Contact: Emergency contact name'])
                ->rules(['max:255'])
            ,
            ImportColumn::make('emergency_contact_phone')
                ->guess(['Contact: Emergency contact phone'])
                ->rules(['max:255'])
            ,

            ImportColumn::make('date_of_birth')
                ->guess(['Contact: Birthdate'])
                ->rules(['max:255'])
            ,
            ImportColumn::make('gender')
                ->guess(['Contact: Gender'])
                ->rules(['max:255'])
            ,
            ImportColumn::make('country_of_nationality')
                ->guess(['Contact: Nationality'])
                ->rules(['max:255'])
            ,

            ImportColumn::make('street')
                ->guess(['Contact: Street'])
                ->rules(['max:255'])
            ,
            ImportColumn::make('house_number')
                ->guess(['Contact: House number'])
                ->rules(['max:255'])
            ,
            ImportColumn::make('house_number_addition')
                ->guess(['Additional CO Domicile'])
                ->rules(['max:255'])
            ,
            ImportColumn::make('postcode')
                ->guess(['Contact: Postal code'])
                ->rules(['max:255'])
            ,
            ImportColumn::make('city')
                ->guess(['Contact: City'])
                ->rules(['max:255'])
            ,
            ImportColumn::make('country')
                ->guess([''])
                ->rules(['max:255'])
            ,

            ImportColumn::make('preferred_language')
                ->guess(['Contact: Pref Language Code'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,
            ImportColumn::make('preferred_communication_method')
                ->guess(['Contact: Preferred communication method'])
                ->rules(['max:255'])
            ,
            ImportColumn::make('active_transportation_type')
                ->guess(['Contact: Transportation type'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,
            ImportColumn::make('active_vehicle_ownership')
                ->guess(['Contact: vehicle Ownership'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,
            ImportColumn::make('delivery_area')
                ->guess(['Contact: Delivery Area'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,

            ImportColumn::make('is_student')
                ->guess(['Is Student'])
                ->rules(['max:255'])
            ,

            ImportColumn::make('residence_permit_type')
                ->guess(['Contact: Work/Residence Permit Type', 'Contact: Work Residence Permit Type'])
                ->rules(['max:255'])
            ,
            ImportColumn::make('residence_permit_expiration_date')
                ->guess(['Contact: Residence title until'])
                ->rules(['max:255'])
            ,

            ImportColumn::make('driving_license_expiration_date')
                ->guess(['Contact: Driving license expiration'])
                ->rules(['max:255'])
            ,
            ImportColumn::make('pre_contract_minimum_hours')
                ->guess(['min hours worked per week'])
                ->numeric()
                ->rules(['required', 'integer'])
                ->requiredMapping()
            ,
            ImportColumn::make('pre_contract_function')
                ->guess(['Type'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,

            ImportColumn::make('contract_id')
                ->guess(['Courier Contract: ID'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,
            ImportColumn::make('contract_end_date')
                ->guess(['End Date'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,
            ImportColumn::make('employee_since')
                ->guess(['Contact: employee since (all contracts)', 'Contact: employee since _all contracts_', 'Contact: employee since all contracts'])
                ->rules(['required', 'max:255'])
                ->requiredMapping()
            ,

            // ImportColumn::make('lead_id'])->guess([''])->make('')->requiredMapping()->rules(['required', 'max:255']),
            // ImportColumn::make('pre_contract_id')->guess([''])->requiredMapping()->rules(['required', 'max:255']),
            // ImportColumn::make('agency_status')->guess([''])->requiredMapping()->rules(['required', 'max:255']),
            // ImportColumn::make('previous_experience')->guess([''])->boolean()->rules(['boolean']),
            // ImportColumn::make('driving_license_number')->guess([''])->rules(['max:255']),
            // ImportColumn::make('lead_created_date')->guess([''])->requiredMapping()->rules(['required', 'datetime']),
            // ImportColumn::make('pre_contract_start_date')->guess(['Start date'])->requiredMapping()->rules(['required', 'date']),
            // ImportColumn::make('pre_contract_created_date')->guess([''])->requiredMapping()->rules(['required', 'datetime']),
            // ImportColumn::make('first_contract_start_date')->guess(['Contact: employee since (all contracts)'])->requiredMapping()->rules(['required', 'date']),
            // ImportColumn::make('months_of_work_experience')->guess([''])->numeric()->rules(['required', 'integer']),
            // ImportColumn::make('number_of_previous_contracts')->guess([''])->numeric()->rules(['required', 'integer']),
            // ImportColumn::make('pre_contract_is_indefinite')->guess([''])->boolean()->rules(['boolean']),
        ];
    }

    public function remapData(): void
    {
        $data = [];

        foreach ($this->getCachedColumns() as $column) {
            $columnName = $column->getName();

            if (blank($this->columnMap[$columnName] ?? null)) {
                continue;
            }

            $rowColumnName = $this->columnMap[$columnName];

            if (! array_key_exists($rowColumnName, $this->data)) {
                continue;
            }

            $data[$columnName] = $this->data[$rowColumnName];
        }

        $this->data = $data;
    }

    public function resolveRecord(): ?JetApplicant
    {
        $to18CharId = function (string $id): string {
            // Formula & requirements from Documentation: https://help.salesforce.com/s/articleView?id=000385585&type=1
            if (strlen($id) !== 15) {
                throw new \Exception('Invalid id. The id must be 15 characters long.', 400);
            }
            if (preg_match('/[^0-9A-Za-z]/', $id)) {
                throw new \Exception('Invalid id. The id must be base62 encoded.', 400);
            }

            $charSetSmall = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            $charSetFull = $charSetSmall . '012345';
            $extraChars = '';
            foreach (str_split($id, 5) as $idCharsChunk) {
                $extraCharIndex = 0;
                foreach (str_split($idCharsChunk) as $idCharIndex => $idChar) {
                    if (str_contains($charSetSmall, $idChar)) {
                        $extraCharIndex += 2 ** $idCharIndex;
                    }
                }
                $extraChars .= $charSetFull[$extraCharIndex];
            }

            return $id . $extraChars;
        };
        $formatGender = function (?string $gender): ?string {
            return [
                'Male' => 'Male',
                'Female' => 'Female',
                'Diverse' => 'Diverse',
                'männlich' => 'Male',
                'weiblich' => 'Female',
                'diverse' => 'Diverse'
            ][$gender] ?? null;
        };
        $formatPreferredLanguage = function (?string $preferredLanguage): string {
            return ['nl' => 'nl_NL', 'en' => 'en_US'][$preferredLanguage] ?? 'en_US';
        };
        $formatDate = function (?string $date): ?string {
            return !is_null($date) ? Carbon::createFromFormat('m/d/Y', $date)->toDateString() : $date;
        };
        $formatPreConctractFunction = function (string $preConctractFunction): string {
            return Str::startsWith($preConctractFunction, 'Courier Captain') ? 'Courier Captain' : 'Courier';
        };
        $formatPhone = function (?string $phone): ?string {
            return !is_null($phone) ? Str::start(Str::ltrim($phone,'0'), '+') : $phone;
        };

        #region Calculate the pre_contract_start_date
        $currentContractEndDate = $formatDate($this->data['contract_end_date']);
        $preContractStartDate = Carbon::parse($currentContractEndDate)->addDays(1)->toDateString();
        #endregion

        #region Calculate the number_of_previous_contracts
        $firstContractStartDate = $formatDate($this->data['employee_since']);
        $monthsFromFirstContract = (int)Carbon::parse($firstContractStartDate)->diffInMonths($currentContractEndDate);
        $numberOfContracts = 0;
        $monthsFromFirstContractTemp = $monthsFromFirstContract;
        while ($monthsFromFirstContractTemp >= 1) {
            $monthsFromFirstContractTemp -= 12;
            $numberOfContracts++;
        }
        if ($numberOfContracts >= 4) { $numberOfContracts = 3; }
        #endregion

        #region Calculate the pre_contract_end_date
        $preContractEndDate = Carbon::parse($preContractStartDate)->addMonths(12)->subDay()->toDateString();
        if ($numberOfContracts === 3) { $preContractEndDate = null; }
        #endregion

        // dd([
        //     $this->getImport(),
        //     $this,
        //     'record' => $this->record,
        //     'data' => $this->data,
        //     'originalData' => $this->originalData
        // ]);

        $newApplicant = [
            ...$this->data,
            'contact_id' => (strlen($this->data['contact_id']) === 15) ? $to18CharId($this->data['contact_id']) : $this->data['contact_id'],
            'contract_id' => (strlen($this->data['contract_id']) === 15) ? $to18CharId($this->data['contract_id']) : $this->data['contract_id'],
            'agency_status' => 'New',
            'agency_status_reason' => null,
            'last_name' => Str::rtrim(Str::before($this->data['last_name'], '[')),
            'email' => Str::trim(Str::lower($this->data['email'])),
            'mobile_phone' => $formatPhone($this->data['mobile_phone']),
            'phone' => $formatPhone($this->data['phone'] ?? null) ?? $formatPhone($this->data['mobile_phone']),
            'gender' => $formatGender($this->data['gender'] ?? null),
            'date_of_birth' => $formatDate($this->data['date_of_birth'] ?? null),
            'country_of_nationality' => ($this->data['country_of_nationality'] ?? null) ? Str::after($this->data['country_of_nationality'], ' - ') : null,
            'preferred_language' => $formatPreferredLanguage($this->data['preferred_language']),
            'driving_license_expiration_date' => $formatDate($this->data['driving_license_expiration_date'] ?? null),
            'lead_created_date' => Carbon::now()->toDateTimeString(),
            'previous_experience' => true,
            'is_student' => (bool)($this->data['is_student'] ?? null),
            'pre_contract_minimum_hours' => (int)$this->data['pre_contract_minimum_hours'],
            'pre_contract_function' => $formatPreConctractFunction($this->data['pre_contract_function']),
            'pre_contract_start_date' => $preContractStartDate,
            'pre_contract_end_date' => $preContractEndDate,
            'pre_contract_created_date' => Carbon::now()->toDateTimeString(),
            'first_contract_start_date' => $firstContractStartDate,
            'months_of_work_experience' => $monthsFromFirstContract,
            'number_of_previous_contracts' => $numberOfContracts,
            'is_import' => true
        ];
        unset($newApplicant['contract_end_date'], $newApplicant['employee_since']);

        if (JetApplicant::where('contact_id', $newApplicant['contact_id'])->orWhere('email', $newApplicant['email'])->exists()) {
            throw new RowImportFailedException("Duplicate applicant. The contact_id or email already exist.");
        } else {
            JetActions::syncNewApplicant($newApplicant);
            return null;
        }
    }

    public static function getCompletedNotificationBody(Import $import): string
    {
        $body = 'Your jet applicant import has completed and ' . number_format($import->successful_rows) . ' ' . str('row')->plural($import->successful_rows) . ' imported.';

        if ($failedRowsCount = $import->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to import.';
        }

        return $body;
    }
}
