<?php

namespace App\Filament\Imports;

use Carbon\CarbonInterface;
use Filament\Actions\Imports\Jobs\ImportCsv;

class JetApplicantImporterJob extends ImportCsv
{
    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 300;

    /**
     * Indicate if the job should be marked as failed on timeout.
     *
     * @var bool
     */
    public $failOnTimeout = true;

    /**
     * Determine number of times the job may be attempted.
     */
    public function tries(): int
    {
        return 1;
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): ?CarbonInterface
    {
        return null;
    }
}
