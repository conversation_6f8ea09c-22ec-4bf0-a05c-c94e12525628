<?php

namespace App\Filament\Exports;

use App\Models\Urenbestand;
use Carbon\Carbon;
use Filament\Actions\Exports\ExportColumn;
use Filament\Actions\Exports\Exporter;
use Filament\Actions\Exports\Models\Export;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Facades\DB;

class UrenbestandExporter extends Exporter
{
    protected static ?string $model = Urenbestand::class;

    public static function modifyQuery(Builder $query): Builder
    {
        $test = $query->select('id', 'driver_id', 'city', 'date', 'loon_component_id', DB::raw('SUM(hours) as total_hours'))
            ->where('import_id', '=', '40')
            ->where('driver_id', '=', '282218')
            ->groupBy('driver_id', 'city', 'date', 'loon_component_id')
            ->distinct();
        return $test;
    }

    public static function getColumns(): array
    {
        return [
            ExportColumn::make('reg')
                ->label('reg')
                ->state(function(Urenbestand $urenbestand) {
                    return $urenbestand->driver_id;
                }),
            ExportColumn::make('pltsnr')
                ->label('pltsnr')
                ->state(function() {
                    return '';
                }),
            ExportColumn::make('loon_component_id')
                ->label('looncomponent')
                ->state(function(Urenbestand $urenbestand) {
                    return $urenbestand->loonComponentType->name;
                }),
            ExportColumn::make('perc')
                ->label('perc')
                ->state(function() {
                    return 100;
                }),
            ExportColumn::make('date')
                ->label('datum')
                ->state(function(Urenbestand $urenbestand) {
                    $date = Carbon::parse($urenbestand->date);
                    return $date->format('d-m-y');
                }),
            ExportColumn::make('hours')
                ->label('uren')
                ->state(function(Urenbestand $urenbestand) {
                    return $urenbestand->total_hours;
                }),
            ExportColumn::make('relatienr')
                ->state(function() {
                    return 3130739;
                }),
            ExportColumn::make('city')
                ->label('kpcode'),
            ExportColumn::make('functie')
                ->label('functie')
                ->state(function() {
                    return '';
                }),
            ExportColumn::make('cao schaal')
                ->label('cao schaal')
                ->state(function() {
                    return 1;
                }),
            ExportColumn::make('tarief')
                ->label('tarief')
                ->state(function() {
                    return 'JUST EAT';
                }),
            ExportColumn::make('loonschema')
                ->label('loonschema')
                ->state(function() {
                    return 'JUST EAT';
                })
        ];
    }

    public static function getCompletedNotificationBody(Export $export): string
    {
        $body = 'Your urenbestand export has completed and ' . number_format($export->successful_rows) . ' ' . str('row')->plural($export->successful_rows) . ' exported.';

        if ($failedRowsCount = $export->getFailedRowsCount()) {
            $body .= ' ' . number_format($failedRowsCount) . ' ' . str('row')->plural($failedRowsCount) . ' failed to export.';
        }

        return $body;
    }
}
