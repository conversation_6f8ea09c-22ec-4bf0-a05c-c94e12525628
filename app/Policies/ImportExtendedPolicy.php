<?php

namespace App\Policies;

use App\Models\User;
use App\Models\ImportExtended;
use Illuminate\Auth\Access\HandlesAuthorization;

class ImportExtendedPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->can('view_any_import');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, ImportExtended $importExtended): bool
    {
        return $user->can('view_import');
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create_import');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, ImportExtended $importExtended): bool
    {
        return $user->can('update_import');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, ImportExtended $importExtended): bool
    {
        return $user->can('delete_import');
    }

    /**
     * Determine whether the user can bulk delete.
     */
    public function deleteAny(User $user): bool
    {
        return $user->can('delete_any_import');
    }

    /**
     * Determine whether the user can permanently delete.
     */
    public function forceDelete(User $user, ImportExtended $importExtended): bool
    {
        return $user->can('force_delete_import');
    }

    /**
     * Determine whether the user can permanently bulk delete.
     */
    public function forceDeleteAny(User $user): bool
    {
        return $user->can('force_delete_any_import');
    }

    /**
     * Determine whether the user can restore.
     */
    public function restore(User $user, ImportExtended $importExtended): bool
    {
        return $user->can('restore_import');
    }

    /**
     * Determine whether the user can bulk restore.
     */
    public function restoreAny(User $user): bool
    {
        return $user->can('restore_any_import');
    }

    /**
     * Determine whether the user can replicate.
     */
    public function replicate(User $user, ImportExtended $importExtended): bool
    {
        return $user->can('replicate_import');
    }

    /**
     * Determine whether the user can reorder.
     */
    public function reorder(User $user): bool
    {
        return $user->can('reorder_import');
    }
}
