<?php

namespace App\Services;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class FusionauthService {
    public static function fetchAccessToken(?string $ccgId): string {
        $tokenExpiryGracePeriod = 15;
        if (App::environment('production') && $ccgId) {
            $cacheTokenKey = 'fusionauth_access_token.' . $ccgId;
            $cachedToken = Cache::get($cacheTokenKey);
            if ($cachedToken) { return $cachedToken; }

            $basicToken = base64_encode(config('services.fusionauth.ccg_id') . ':' . config('services.fusionauth.ccg_secret'));
            $ccgResponse = Http::withToken($basicToken, 'Basic')
                ->asForm()
                ->baseUrl(config('services.fusionauth.url'))
                ->throw()
                ->post('/oauth2/token', [
                    'grant_type' => 'client_credentials',
                    'scope' => "target-entity:$ccgId:read,write"
                ])
            ;
            $tokenExpiresInSeconds = $ccgResponse['expires_in'] - $tokenExpiryGracePeriod;
            Cache::put($cacheTokenKey, $ccgResponse['access_token'], $tokenExpiresInSeconds);

            return $ccgResponse['access_token'];
        }

        $cachedToken = Cache::get('fusionauth_access_token');
        if ($cachedToken) { return $cachedToken; }

        $passwordlessStartResponse = Http::withHeaders([
                'Authorization' => config('services.fusionauth.authorization'),
                'Content-Type' => 'application/json',
            ])
            ->baseUrl(config('services.fusionauth.url'))
            ->throw()
            ->post('/api/passwordless/start', [
                "applicationId" => config('services.fusionauth.application_id'),
                "loginId" => config('services.fusionauth.email')
            ])
        ;
        $passwordlessLoginResponse = Http::withHeaders([
                'Content-Type' => 'application/json'
            ])
            ->baseUrl(config('services.fusionauth.url'))
            ->throw()
            ->post('/api/passwordless/login', [
                'code' => $passwordlessStartResponse['code']
            ])
        ;
        $tokenExpiresInSeconds = (int)Carbon::createFromTimestampMs($passwordlessLoginResponse['tokenExpirationInstant'])->diffInSeconds(Carbon::now(), true);
        $tokenExpiresInSeconds -= $tokenExpiryGracePeriod;
        Cache::put('fusionauth_access_token', $passwordlessLoginResponse['token'], $tokenExpiresInSeconds);

        return $passwordlessLoginResponse['token'];
    }
}
