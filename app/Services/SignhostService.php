<?php

namespace App\Services;

use App\Models\Filament\RemoteModel;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class SignhostService
{
    private PendingRequest $signhostClient;

    private $statuses = [
        5 => 'Waiting for document',
        10 => 'Waiting for signer',
        20 => 'In progress',
        30 => 'Signed',
        40 => 'Rejected',
        50 => 'Expired',
        60 => 'Cancelled',
        70 => 'Failed'
    ];

    public function __construct()
    {
        $this->signhostClient = Http::withHeaders([
            'Accept' => 'application/vnd.api+json',
            'Content-Type' => 'application/vnd.api+json',
        ])
            ->baseUrl(config('services.signhost_service.url'))
            ->throw();
    }

    public function getContracts($flexapp_id)
    {
        try {
            $response = $this->signhostClient
                ->get(config('services.signhost_service.url') . '/get_local_transaction/' . $flexapp_id)
                ->json();
        } catch (ConnectionException|RequestException) {
            return [];
        }

        return $response['data'] ?? [];
    }

    public function cancelAllContracts($flexapp_id)
    {
        $contracts = $this->getContracts($flexapp_id);
        foreach($contracts as $contract) {
            if ($contract['attributes']['status_code'] > 30 && ! str_starts_with(data_get($contract, 'attributes.reference'), 'JET')) {
                continue;
            }
            $this->cancelContract($contract['attributes']['transaction_id']);
        }
    }

    public function cancelContract($transactionId): void
    {
        Http::withHeaders([
            'Accept' => 'application/vnd.api+json',
            'Content-Type' => 'application/vnd.api+json',
        ])->delete(config('services.signhost_service.url') . '/delete_transaction/' . $transactionId);
    }

    public function modelRequest(RemoteModel $remoteModel, array $query = [], ?array $fields = []): Collection
    {
        $url = $remoteModel->remoteUrl();
        $key = json_encode(compact('url', 'query', 'fields'));

        // Use a shorter cache duration (30 seconds) to allow for pagination updates
        // while still providing some performance benefit
        return Cache::remember($key, now()->addSeconds(30), function () use ($remoteModel, $query, $fields) {
            try {
                return $this->signhostClient
                    ->get($remoteModel->remoteUrl(), $query)
                    ->collect($fields);
            } catch (ConnectionException|RequestException) {
                return collect();
            }
        });
    }
}
