<?php
namespace App\Services;

use App\Models\JetApplicant;
use App\Services\FusionauthService;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DataCheckerService {

    private $dataCheckerClient;

    function __construct() {
        $this->dataCheckerClient = Http::withHeaders([
            'Accept' => 'application/vnd.api+json',
            'Content-Type' => 'application/vnd.api+json',
        ])
            ->withToken(FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.datachecker_service')))
            ->baseUrl(config('services.datachecker_service.url'))
            ->throw()
        ;
    }

    public function getMostRecentDocuments ($uuid): array {
        return $this->dataCheckerClient->get("/results/recent/documents/".$uuid)->json();
    }

    public function getMostRecentDriverLicense ($uuid): array {
        return $this->dataCheckerClient->get("/results/recent/driversLicense")->json();
    }

    public function addTransaction (array $transactionData): array {
        return $this->dataCheckerClient->post("/datachecker/add-transaction", $transactionData)->json();
    }

    public function getResidentPermitForApplicant(JetApplicant $applicant): Response
    {
        return $this->dataCheckerClient->get('results/document/residence_permit/' . $applicant->flexapp_id);
    }

    public function getMostRecentTransactionImages(string $uuid): Response|null
    {
        try {
            return $this->dataCheckerClient->get('results/recent/id_transaction/' . $uuid);
        } catch (\Throwable $th) {
            Log::error($th->getMessage());

            return null;
        }
    }
}
