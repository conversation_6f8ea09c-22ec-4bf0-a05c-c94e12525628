<?php
namespace App\Services;

use App\Services\FusionauthService;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\Http;

class UserService {

    private $userServiceClient;

    function __construct() {
        $this->userServiceClient = Http::withHeaders([
            'Accept' => 'application/vnd.api+json',
            'Content-Type' => 'application/vnd.api+json',
            ])
            ->withToken(FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.user_service')))
            ->baseUrl(config('services.user_service.url'))
            ->throw()
        ;
    }

    public function createUser (array $userAttributes, ?array $userRelationships): array {
        $userData = [ 'isQuiet' => 'false', 'data' => [
            'type' => 'user',
            'attributes' => $userAttributes
        ]];

        if ($userRelationships) {
            $userData['data']['relationships'] = $userRelationships;
        }

        return $this->userServiceClient->post("/users", $userData)->json();
    }

    public function updateUser (string $flexappId, array $userAttributes): array {
        $userData = [ 'data' => [
            'type' => 'kandidaat',
            'id' => $flexappId,
            'attributes' => $userAttributes
        ]];

        // TODO: Correct the jsonapi 'relationships' implementation.
        // if ($userRelationships) {
        //     $userData['data']['relationships'] = $userRelationships;
        // }

        return $this->userServiceClient->patch("/kandidaat/$flexappId", $userData)->json();
    }

    public function updateUserRole (string $flexappId, string $role): ?array {
        return $this->userServiceClient->get("/kandidaat/$flexappId/change-role?role=$role")->json();
    }

    public function sendJETRegistrationReminder (string $flexappId) {
        return $this->userServiceClient
            ->post("auth/send-jet-registration-reminder/$flexappId")
            ->json();
    }

    public function getIdentificationImages(string $flexappId): array {

        return $this->userServiceClient
            ->get("media/user/$flexappId/most-recent-types")
            ->json();
    }

    public function updateUserEmailAddress (string $flexappId, string $newEmailAddress) {
        // Use own request handler so we do not have to throw on error
        $result = Http::withHeaders([
            'Accept' => 'application/vnd.api+json',
            'Content-Type' => 'application/vnd.api+json',
            ])
            ->withToken(FusionauthService::fetchAccessToken(config('services.fusionauth.ccg_target_id.user_service')))
            ->baseUrl(config('services.user_service.url'))
            ->patch("kandidaat/change-email/$flexappId", [
                'new_email_address' => $newEmailAddress,
            ]);

        if ($result->failed()) {
            return Notification::make()
                ->title($result->json('message') . ': ' . $result->json('details'))
                ->body(json_encode($result->json('additional')))
                ->persistent()
                ->danger()
                ->send();
        }

        return Notification::make()
            ->title($result->json('message') . ': ' . $result->json('details'))
            ->body(json_encode($result->json('additional')))
            ->persistent()
            ->success()
            ->send();
    }
}
