<?php

namespace App\Services;

use Illuminate\Support\Traits\Conditionable;
use Sentry\Breadcrumb;
use Sentry\EventId;
use Sentry\Severity;
use Sentry\State\Scope;

use function Sentry\captureMessage as captureSentryMessage;
use function Sentry\configureScope as configureSentryScope;

class SentryLogger
{
    use Conditionable;

    private array $breadcrumbs = [];

    private array $tags = [];

    /** @var array{name: string, value: string} */
    private array $contexts = [];

    public static function make(): self
    {
        return new self();
    }

    public function breadcrumb(string $message, string $category, array $metadata = []): self
    {
        $this->breadcrumbs[] = new Breadcrumb(
            level: Breadcrumb::LEVEL_ERROR,
            type: Breadcrumb::TYPE_ERROR,
            category: $category,
            message: $message,
            metadata: $metadata,
        );

        return $this;
    }

    public function tag(string $name, mixed $value): self
    {
        $this->tags[$name] = $value;

        return $this;
    }

    public function context(string $name, array $context): self
    {
        $this->contexts[$name] = $context;

        return $this;
    }

    private function configureScope(): void
    {
        configureSentryScope(function (Scope $scope) {
            foreach ($this->breadcrumbs as $breadcrumb) {
                $scope->addBreadcrumb($breadcrumb);
            }

            if ($this->tags) {
                $scope->setTags($this->tags);
            }

            foreach ($this->contexts as $name => $context) {
                $scope->setContext($name, $context);
            }
        });
    }

    public function captureMessage(string $message, ?Severity $level = null): ?EventId
    {
        if (! config('sentry.dsn')) {
            return null;
        }

        $this->configureScope();

        return captureSentryMessage($message, $level ?? Severity::error());
    }
}
