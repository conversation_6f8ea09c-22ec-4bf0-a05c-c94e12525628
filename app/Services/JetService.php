<?php
#region Jet DTOs
namespace App\DTO;

use <PERSON><PERSON>\LaravelData\Data;
use <PERSON><PERSON>\LaravelData\Attributes\MapInputName;

#region Applicant DTOs
class JetApplicantDTO extends Data {
    #[MapInputName('Id')]
    public string $lead_id;
    #[MapInputName('pre_contract_id')]
    public ?string $pre_contract_id;
    #[MapInputName('ConvertedContactId')]
    public ?string $contact_id;
    #[MapInputName('mylorryid__c')]
    public ?string $scoober_id;

    #[MapInputName('Agency_Status__c')]
    public string $agency_status;
    #[MapInputName('Agency_Reason_for_Status__c')]
    public ?string $agency_status_reason;

    #[MapInputName('FirstName')]
    public ?string $first_name;
    #[MapInputName('LastName')]
    public ?string $last_name;
    #[MapInputName('Email')]
    public ?string $email;
    #[MapInputName('Phone')]
    public ?string $phone;
    #[MapInputName('MobilePhone')]
    public ?string $mobile_phone;

    #[MapInputName('Emergency_Contact_Name__c')]
    public ?string $emergency_contact_name;
    #[MapInputName('Emergency_Contact_Phone__c')]
    public ?string $emergency_contact_phone;

    #[MapInputName('Date_of_Birth__c')]
    public ?string $date_of_birth;
    #[MapInputName('Gender__c')]
    public ?string $gender;
    #[MapInputName('Country_of_Nationality__c')]
    public ?string $country_of_nationality;

    #[MapInputName('Street__c')]
    public ?string $street;
    #[MapInputName('House_Number_del__c')]
    public ?string $house_number;
    #[MapInputName('Additional_CO_Domicile__c')]
    public ?string $house_number_addition;
    #[MapInputName('Postcode__c')]
    public ?string $postcode;
    #[MapInputName('Town_City__c')]
    public ?string $city;
    #[MapInputName('Country__c')]
    public ?string $country;

    #[MapInputName('Preferred_Language__c')]
    public ?string $preferred_language;
    #[MapInputName('Preferred_communication_method__c')]
    public ?string $preferred_communication_method;
    #[MapInputName('Whatsapp_Consent__c')]
    public ?bool $whatsapp_consent;
    #[MapInputName('Active_Transportation_Type__c')]
    public ?string $active_transportation_type;
    #[MapInputName('Active_Vehicle_Ownership__c')]
    public ?string $active_vehicle_ownership;
    #[MapInputName('Delivery_Area__c')]
    public ?string $delivery_area;

    #[MapInputName('Previous_experience__c')]
    public ?bool $previous_experience;
    #[MapInputName('Is_Student__c')]
    public ?bool $is_student;

    #[MapInputName('Work_Residence_Permit_Type__c')]
    public ?string $residence_permit_type;
    #[MapInputName('Work_Permit_Expiration__c')]
    public ?string $residence_permit_expiration_date;

    #[MapInputName('Driver_License_Number__c')]
    public ?string $driving_license_number;
    #[MapInputName('Expiration_Date_Driving_License__c')]
    public ?string $driving_license_expiration_date;
    #[MapInputName('CreatedDate')]
    public ?string $lead_created_date;

    #[MapInputName('Start_Date__c')]
    public ?string $pre_contract_start_date;
    #[MapInputName('Minimum_Hours__c')]
    public ?float $pre_contract_minimum_hours;
    #[MapInputName('pre_contract_created_date')]
    public ?string $pre_contract_created_date;
}
#endregion

#endregion

namespace App\Services;

use App\DTO\JetOauthDTO;
use App\DTO\JetOauthTokenDTO;
use App\DTO\JetApplicantDTO;

use Illuminate\Http\Client\ConnectionException;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Sentry\Severity;

class JetService {

    private PendingRequest $jetClient;

    #region Service Methods
    function __construct() {
        $this->jetClient = Http::withHeaders([
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            ])
            ->withToken($this->fetchAccessToken(
                config('services.jet.oauth_url'),
                JetOauthDTO::from(config('services.jet.oauth'))
            ))
            ->baseUrl(config('services.jet.api_url'))
            ->throw()
        ;
    }

    private function fetchAccessToken(string $oauthUrl, JetOauthDTO $oauthDTO): string {
        $response = Http::withHeaders(['Accept' => 'application/json'])
            ->throw()
            ->asForm()
            ->post($oauthUrl, $oauthDTO->all())
        ;
        return (JetOauthTokenDTO::from($response->json()))->access_token;
    }

    private function fetchReport(string $reportId, array $reportData): ?array
    {
        try {
            return $this->jetClient
                ->post("/services/data/v60.0/analytics/reports/$reportId", $reportData)
                ->json();
        } catch (ConnectionException $exception) {
            SentryLogger::make()
                ->tag('report_id', $reportId)
                ->context('Salesforce', [
                    'HTTP method' => 'POST',
                    'Entity' => 'Report',
                    'EntityId' => $reportId,
                ])
                ->captureMessage('[fetch report] ' . $exception->getMessage(), Severity::warning());

            return null;
        }
    }

    private function fetchObject(string $objectName, string $objectId, array $objectFields): ?array
    {
        $objectFieldsString = collect($objectFields)->ensure('string')->implode(',');
        if ($objectFieldsString) {
            $objectFieldsString = "?fields=$objectFieldsString";
        }

        try {
            return $this->jetClient
                ->get(sprintf("/services/data/v60.0/sobjects/%s/%s%s", $objectName, $objectId, $objectFieldsString))
                ->json();
        } catch (ConnectionException $exception) {
            SentryLogger::make()
                ->context('Salesforce', [
                    'HTTP method' => 'GET',
                    'Entity' => $objectName,
                    'EntityId' => $objectId,
                ])
                ->tag(strtolower($objectName) . '_id', $objectId)
                ->context('Salesforce data', $objectFields)
                ->captureMessage('[fetch object] ' . $exception->getMessage(), Severity::warning());

            return null;
        }
    }

    private function updateObject(string $objectName, string $objectId, array $objectData): ?array
    {
        try {
            return $this->jetClient
                ->patch("/services/data/v60.0/sobjects/$objectName/$objectId", $objectData)
                ->json();
        } catch (ConnectionException $exception) {
            SentryLogger::make()
                ->context('Salesforce', [
                    'HTTP method' => 'PATCH',
                    'Entity' => $objectName,
                    'EntityId' => $objectId,
                ])
                ->tag(strtolower($objectName) . '_id', $objectId)
                ->context('Salesforce data', $objectData)
                ->captureMessage('[update object] ' . $exception->getMessage(), Severity::warning());

            return null;
        }
    }

    private function createObject(string $objectName, array $objectData): ?array
    {
        try {
            return $this->jetClient
                ->post("/services/data/v60.0/sobjects/$objectName", $objectData)
                ->json();
        } catch (ConnectionException $exception) {
            SentryLogger::make()
                ->context('Salesforce', [
                    'HTTP method' => 'POST',
                    'Entity' => $objectName,
                ])
                ->context('Salesforce data', $objectData)
                ->captureMessage('[create object] ' . $exception->getMessage(), Severity::warning());

            return null;
        }
    }
    #endregion

    #region Applicant Methods
    public function fetchApplicant(string $leadId, ?string $preContractId = null): JetApplicantDTO
    {
        $leadObject = $this->fetchObject('Lead', $leadId, [
            'Id',
            'ConvertedContactId',
            'Agency_Status__c',
            'Agency_Reason_for_Status__c',
            'FirstName',
            'LastName',
            'Email',
            'Phone',
            'MobilePhone',
            'Emergency_Contact_Name__c',
            'Emergency_Contact_Phone__c',
            'Date_of_Birth__c',
            'Gender__c',
            'Country_of_Nationality__c',
            'Street__c',
            'House_Number_del__c',
            'Additional_CO_Domicile__c',
            'Postcode__c',
            'Town_City__c',
            'Country__c',
            'Preferred_Language__c',
            'Preferred_communication_method__c',
            'Whatsapp_Consent__c',
            'Active_Transportation_Type__c',
            'Active_Vehicle_Ownership__c',
            'Delivery_Area__c',
            'Previous_experience__c',
            'Is_Student__c',
            'Work_Residence_Permit_Type__c',
            'Work_Permit_Expiration__c',
            'Driver_License_Number__c',
            'Expiration_Date_Driving_License__c',
            'CreatedDate'
        ]) ?? [];

        $leadPreContractObject = [];
        if ($preContractId) {
            $leadPreContractObject = $this->fetchObject('Pre_Contract__c', $preContractId, [
                'Id',
                'Start_Date__c',
                'Minimum_Hours__c',
                'CreatedDate'
            ]) ?? [];

            if ($leadPreContractId = data_get($leadPreContractObject, 'Id')) {
                $leadPreContractObject['pre_contract_id'] = $leadPreContractId;
            }

            if ($leadPreContractCreatedDate = data_get($leadPreContractObject, 'CreatedDate')) {
                $leadPreContractObject['pre_contract_created_date'] = $leadPreContractCreatedDate;
            }
        }

        $leadContactObject = [];
        if ($convertedContactId = data_get($leadObject, 'ConvertedContactId')) {
            $leadContactObject = $this->fetchObject('Contact', $convertedContactId, [
                'Id',
                'mylorryid__c'
            ]) ?? [];
        }

        return JetApplicantDTO::from([...$leadObject, ...Arr::except($leadPreContractObject, ['Id', 'CreatedDate']), ...Arr::except($leadContactObject, ['Id'])]);
    }

    public function fetchApplicantsFromReport(?string $fromDate = null, ?array $leadIdsToExclude = null): array {
        $reportRequestData = [ "reportMetadata" => [
            "aggregates" => [],
            "chart" => null,
            "crossFilters" => [],
            "currency" => "EUR",
            "customDetailFormula" => [
                "CDF1" => [
                    "dataType" => "datetime",
                    "description" => null,
                    "formula" => "Pre_Contract__c.CreatedDate",
                    "formulaType" => "datetime",
                    "label" => "Created Datetime"
                ]
            ],
            "dashboardSetting" => null,
            "description" => null,
            "detailColumns" => [
                "Lead.Id",
                "Pre_Contract__c.Id",
                "Pre_Contract__c.CreatedDate",
                "CDF1"
            ],
            "division" => null,
            "groupingsAcross" => [],
            "groupingsDown" => [],
            "hasDetailRows" => true,
            "hasRecordCount" => true,
            "historicalSnapshotDates" => [],
            "presentationOptions" => [
                "hasStackedSummaries" => true
            ],
            "reportBooleanFilter" => null,
            "reportFilters" => [
                [
                    "column" => "Lead.Agency__c",
                    "filterType" => "fieldValue",
                    "isRunPageEditable" => true,
                    "operator" => "equals",
                    "value" => "365Werk Payroll"
                ],
                [
                    "column" => "Lead.RecordType",
                    "filterType" => "fieldValue",
                    "isRunPageEditable" => true,
                    "operator" => "equals",
                    "value" => "Applicant Record Type,Talent Pool"
                ],
                [
                    "column" => "Pre_Contract__c.Status__c",
                    "filterType" => "fieldValue",
                    "isRunPageEditable" => true,
                    "operator" => "equals",
                    "value" => "Open"
                ]
            ],
            "reportFormat" => "TABULAR",
            "reportType" => [
                "label" => "Leads with Pre-contracts",
                "type" => "Leads_with_Pre_contracts__c"
            ],
            "scope" => "organization",
            "showGrandTotal" => true,
            "showSubtotals" => true,
            "sortBy" => [[ "sortColumn" => "CDF1", "sortOrder" => "Asc" ]],
            "standardDateFilter" => [
                "column" => "Lead.ConvertedDate",
                "durationValue" => "CUSTOM",
                "endDate" => null,
                "startDate" => null
            ],
            "standardFilters" => null,
            "supportsRoleHierarchy" => false,
            "userOrHierarchyFilterId" => null
        ]];

        if ($fromDate) {
            $reportRequestData['reportMetadata']['reportFilters'][] = [
                "column" => "Pre_Contract__c.CreatedDate",
                "filterType" => "fieldValue",
                "isRunPageEditable" => true,
                "operator" => "greaterOrEqual",
                "value" => $fromDate
            ];
        }

        $reportResponseJsonArray = $this->fetchReport(config('services.jet.report_id'), $reportRequestData);
        $reportApplicantsCollection = collect(data_get($reportResponseJsonArray, 'factMap.T!T.rows'))
            ->map(fn(array $rows, int $key) => collect($rows['dataCells'])
                ->reduce(function (mixed $carry, array $value, int $key) use ($reportRequestData) {
                    $carry[$reportRequestData['reportMetadata']['detailColumns'][$key]] = $value['value'];

                    return $carry;
                }, initial: [])
            );

        if ($leadIdsToExclude) {
            $reportApplicantsCollection = collect($reportApplicantsCollection->reduce(function (mixed $carry, array $value, int $key) use ($leadIdsToExclude) {
                if (!$carry) { $carry = []; }
                if (!in_array($value['Lead.Id'], $leadIdsToExclude)) {
                    $carry[] = $value;
                }
                return $carry;
            }));
        }

        return $reportApplicantsCollection
            ->map(fn (array $applicant, int $key) => $this->fetchApplicant($applicant['Lead.Id'], $applicant['Pre_Contract__c.Id'])->toArray())
            ->toArray();
    }

    public function updateApplicantAgencyStatus(string $leadId, string $agencyStatus, ?string $agencyStatusReason = null): void {
        $this->updateObject('Lead', $leadId, [
            "Agency_Status__c" => $agencyStatus,
            "Agency_Reason_for_Status__c" => $agencyStatusReason
        ]);
    }

    public function updateApplicantDrivingLicense(string $leadId, string $drivingLicenseNumber, string $drivingLicenseExpirationDate): void {
        $this->updateObject('Lead', $leadId, [
            'Driver_License_Number__c' => $drivingLicenseNumber,
            'Expiration_Date_Driving_License__c' => $drivingLicenseExpirationDate
        ]);
    }

    public function updateApplicantIsStudent(string $leadId, bool $isStudent): void {
        $this->updateObject('Lead', $leadId, [
            'Is_Student__c' => $isStudent
        ]);
    }

    public function updateApplicantWorkPermit(string $leadId, string $workingPermitNumber, string $workPermitExpiration, string $workResidencePermitType): void {
        $this->updateObject('Lead', $leadId, [
            'Working_Permit_Number__c' => $workingPermitNumber,
            'Work_Permit_Expiration__c' => $workPermitExpiration,
            'Work_Residence_Permit_Type__c' => $workResidencePermitType
        ]);
    }

    public function updatePreContractDates(string $preContractId, string $preContractStartDate, ?string $preContractEndDate) {
        $this->updateObject('Pre_Contract__c', $preContractId, [
            'Start_Date__c' => $preContractStartDate,
            'End_Date__c' => $preContractEndDate
        ]);
    }

    public function updatePreContractType(string $preContractId, int $preContractMinimunHours, bool $isStudent = false){
        $contractTypes = [
            6 => 'Part time - 6H',
            9 => 'Part time 9h',
            12 => 'Part time 12h',
            16 => 'Part time 16h',
            24 => 'Part-time 24h',
            32 => 'Part time 32h',
            40 => 'Vollzeit'
        ];
        if ($isStudent) {
            $contractTypes = [
                6 => 'Working student 6H',
                9 => 'Working Student 9h',
                12 => 'Working Student 12h'
            ];
        }

        $contractType = $contractTypes[$preContractMinimunHours] ?? null;
        if (!$contractType) {
            throw new \Exception('The preContractMinimunHours is not valid.', 400);
        }

        $this->updateObject('Pre_Contract__c', $preContractId, [
            'Contract_Type__c' => $contractType
        ]);
    }
    #endregion
}
