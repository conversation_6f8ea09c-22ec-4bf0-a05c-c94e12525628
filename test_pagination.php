<?php

require_once 'vendor/autoload.php';

use App\Models\Filament\SignhostExpiredDocument;
use Illuminate\Pagination\Paginator;

// Test pagination functionality
echo "Testing Sushi Model Pagination\n";
echo "==============================\n\n";

// Test page 1
echo "Testing Page 1:\n";
Paginator::currentPageResolver(function () {
    return 1;
});

$page1Results = SignhostExpiredDocument::paginate(5);
echo "Page 1 - Total items: " . $page1Results->total() . "\n";
echo "Page 1 - Current page: " . $page1Results->currentPage() . "\n";
echo "Page 1 - Items count: " . $page1Results->count() . "\n";
echo "Page 1 - First item ID: " . ($page1Results->first()->id ?? 'N/A') . "\n\n";

// Test page 2
echo "Testing Page 2:\n";
Paginator::currentPageResolver(function () {
    return 2;
});

$page2Results = SignhostExpiredDocument::paginate(5);
echo "Page 2 - Total items: " . $page2Results->total() . "\n";
echo "Page 2 - Current page: " . $page2Results->currentPage() . "\n";
echo "Page 2 - Items count: " . $page2Results->count() . "\n";
echo "Page 2 - First item ID: " . ($page2Results->first()->id ?? 'N/A') . "\n\n";

// Compare results
if ($page1Results->first() && $page2Results->first()) {
    $sameData = $page1Results->first()->id === $page2Results->first()->id;
    echo "Same data returned: " . ($sameData ? "YES (PROBLEM)" : "NO (GOOD)") . "\n";
} else {
    echo "One or both pages returned no data\n";
}

echo "\nTest completed.\n";
