<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

use App\Actions\JetActions;
use App\Actions\JetApplicantEnrollmentActions;
use App\Actions\ReminderActions;

#region Artisan Commands
Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');
#endregion

#region Schedule Tasks
Schedule::command('sync:new-applicants')->everyMinute()->withoutOverlapping();
Schedule::command('app:update-contract-renewal-status')->everyFiveMinutes()->withoutOverlapping();
Schedule::command('app:get-weekly-hours')->weeklyOn(6, '12:00');
Schedule::command('app:update-flexwerker-status-easyflex')->dailyAt('07:00');
Schedule::command('app:check-renewal-status')->dailyAt('08:00');
Schedule::command('app:update-contract-end-date')->dailyAt('03:00');

Schedule::call(function () {
    JetApplicantEnrollmentActions::syncActivateAccountStep();
    JetActions::updateApplicantsStepInProgress(3);
    JetApplicantEnrollmentActions::syncFinalizeProfileStep();
    JetActions::updateApplicantsWenoteId();
    JetApplicantEnrollmentActions::syncDataCheckerSteps();
    JetActions::updateApplicantsStepInProgress(6);
    JetActions::updateApplicantsStepInProgress(7);
    JetActions::updateApplicantsStepInProgress(9);
    JetActions::syncApplicantsDrivingLicense();
})->name("Update Applicants' Step in Progress")->everyMinute()->withoutOverlapping();

// TODO: Change to this Task when disabling the old enrollment flow.
// Schedule::call(function () {
//     JetApplicantEnrollmentActions::syncActivateAccountStep();
//     JetApplicantEnrollmentActions::syncFinalizeProfileStep();
//     JetActions::updateApplicantsWenoteId();
//     JetApplicantEnrollmentActions::syncDataCheckerSteps();
//     JetApplicantEnrollmentActions::syncSignContractStep();
//     JetApplicantEnrollmentActions::syncSendRegistrationToJetStep();
//     JetApplicantEnrollmentActions::syncFetchScooberIdStep();
//     JetApplicantEnrollmentActions::syncApplicantsDrivingLicense();
// })->name('Update Applicants Enrollment Progress')->everyMinute()->withoutOverlapping();

Schedule::call(function () {
    ReminderActions::sendRegistrationReminders();
})
    ->name('Send reminders about incomplete registrations')
    ->dailyAt('11:00')
    ->withoutOverlapping();
#endregion
