<?php

use Illuminate\Support\Str;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for database operations. This is
    | the connection which will be utilized unless another connection
    | is explicitly specified when you execute a query / statement.
    |
    */

    'default' => env('DB_CONNECTION', 'sqlite'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Below are all of the database connections defined for your application.
    | An example configuration is provided for each database system which
    | is supported by Laravel. You're free to add / remove connections.
    |
    */

    'connections' => [

        'pgsql' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST'),
            'port' => env('DB_PORT'),
            'database' => env('DB_DATABASE'),
            'username' => env('DB_USERNAME'),
            'password' => env('DB_PASSWORD'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'datachecker' => [
            'driver' => 'pgsql',
            'host' => env('DATACHECKER_SERVICE_DB_HOST'),
            'port' => env('DATACHECKER_SERVICE_DB_PORT'),
            'database' => env('DATACHECKER_SERVICE_DB_DATABASE'),
            'username' => env('DATACHECKER_SERVICE_DB_USERNAME'),
            'password' => env('DATACHECKER_SERVICE_DB_PASSWORD'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],

        'easyflex' => [
            'driver' => 'pgsql',
            'host' => env('EASYFLEX_SERVICE_DB_HOST'),
            'port' => env('EASYFLEX_SERVICE_DB_PORT'),
            'database' => env('EASYFLEX_SERVICE_DB_DATABASE'),
            'username' => env('EASYFLEX_SERVICE_DB_USERNAME'),
            'password' => env('EASYFLEX_SERVICE_DB_PASSWORD'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],

        'userservice' => [
            'driver' => 'pgsql',
            'host' => env('USER_SERVICE_DB_HOST'),
            'port' => env('USER_SERVICE_DB_PORT'),
            'database' => env('USER_SERVICE_DB_DATABASE'),
            'username' => env('USER_SERVICE_DB_USERNAME'),
            'password' => env('USER_SERVICE_DB_PASSWORD'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],

        'verifai' => [
            'driver' => 'pgsql',
            'host' => env('VERIFAI_SERVICE_DB_HOST'),
            'port' => env('VERIFAI_SERVICE_DB_PORT'),
            'database' => env('VERIFAI_SERVICE_DB_DATABASE'),
            'username' => env('VERIFAI_SERVICE_DB_USERNAME'),
            'password' => env('VERIFAI_SERVICE_DB_PASSWORD'),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'search_path' => 'public',
            'sslmode' => 'prefer',
        ],

        'wenote' => [
            'driver' => 'mysql',
            'host' => env('WENOTE_DB_HOST'),
            'port' => env('WENOTE_DB_PORT'),
            'database' => env('WENOTE_DB_DATABASE'),
            'username' => env('WENOTE_DB_USERNAME'),
            'password' => env('WENOTE_DB_PASSWORD'),
            'unix_socket' => '',
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'prefix_indexes' => true,
            'strict' => false,
            'engine' => null,
            'options' => extension_loaded('pdo_mysql') ? array_filter([
                PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
            ]) : [],
        ],

        // 'sqlite' => [
        //     'driver' => 'sqlite',
        //     'url' => env('DATABASE_URL'),
        //     'database' => env('DB_DATABASE', database_path('database.sqlite')),
        //     'prefix' => '',
        //     'foreign_key_constraints' => env('DB_FOREIGN_KEYS', true),
        // ],

        // 'mysql' => [
        //     'driver' => 'mysql',
        //     'url' => env('DATABASE_URL'),
        //     'host' => env('DB_HOST', '127.0.0.1'),
        //     'port' => env('DB_PORT', '3306'),
        //     'database' => env('DB_DATABASE', 'forge'),
        //     'username' => env('DB_USERNAME', 'forge'),
        //     'password' => env('DB_PASSWORD', ''),
        //     'unix_socket' => env('DB_SOCKET', ''),
        //     'charset' => 'utf8mb4',
        //     'collation' => 'utf8mb4_unicode_ci',
        //     'prefix' => '',
        //     'prefix_indexes' => true,
        //     'strict' => false,
        //     'engine' => null,
        //     'options' => extension_loaded('pdo_mysql') ? array_filter([
        //         PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
        //     ]) : [],
        // ],

        // 'sqlsrv' => [
        //     'driver' => 'sqlsrv',
        //     'url' => env('DATABASE_URL'),
        //     'host' => env('DB_HOST', 'localhost'),
        //     'port' => env('DB_PORT', '1433'),
        //     'database' => env('DB_DATABASE', 'forge'),
        //     'username' => env('DB_USERNAME', 'forge'),
        //     'password' => env('DB_PASSWORD', ''),
        //     'charset' => 'utf8',
        //     'prefix' => '',
        //     'prefix_indexes' => true,
        // ],

    ],
    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run on the database.
    |
    */

    'migrations' => [
        'table' => 'migrations',
        'update_date_on_publish' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer body of commands than a typical key-value system
    | such as Memcached. You may define your connection settings here.
    |
    */

    // 'redis' => [

    //     'client' => env('REDIS_CLIENT', 'phpredis'),

    //     'options' => [
    //         'cluster' => env('REDIS_CLUSTER', 'redis'),
    //         'prefix' => env('REDIS_PREFIX', Str::slug(env('APP_NAME', 'laravel'), '_').'_database_'),
    //     ],

    //     'default' => [
    //         'url' => env('REDIS_URL'),
    //         'host' => env('REDIS_HOST', '127.0.0.1'),
    //         'username' => env('REDIS_USERNAME'),
    //         'password' => env('REDIS_PASSWORD'),
    //         'port' => env('REDIS_PORT', '6379'),
    //         'database' => env('REDIS_DB', '0'),
    //     ],

    //     'cache' => [
    //         'url' => env('REDIS_URL'),
    //         'host' => env('REDIS_HOST', '127.0.0.1'),
    //         'username' => env('REDIS_USERNAME'),
    //         'password' => env('REDIS_PASSWORD'),
    //         'port' => env('REDIS_PORT', '6379'),
    //         'database' => env('REDIS_CACHE_DB', '1'),
    //     ],

    // ],

];
