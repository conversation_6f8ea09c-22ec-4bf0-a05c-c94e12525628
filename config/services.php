<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'communicatie_service' => [
        'url' => env('COMMUNICATIE_SERVICE_API_URL'),
    ],

    'easyflex' => [
        'url' => env('EASYFLEX_URL'),
        'wsdl_url' => env('EASYFLEX_WSDL_URL'),
        'license' => env('EASYFLEX_LICENSE'),
        'relatienummer' => env('EASYFLEX_JET_RELATIENUMMER'),
    ],

    'easyflex_service' => [
        'url' => env('EASYFLEX_SERVICE_API_URL'),
    ],

    'fusionauth' => [
        'url' => env('FUSIONAUTH_URL'),
        'key' => env('FUSIONAUTH_KEY_GENERAL'),
        'key_check_user' => env('FUSIONAUTH_KEY_CHECK_USER'),
        'application_id' => env('FUSIONAUTH_APPLICATION_ID'),
        'authorization' => env('FUSIONAUTH_AUTHORIZATION'),
        'email' => env('FUSIONAUTH_ADMIN_USER_EMAIL'),

        'ccg_id' => env('FUSIONAUTH_CCG_ID'),
        'ccg_secret' => env('FUSIONAUTH_CCG_SECRET'),
        'ccg_target_id' => [
            'communicatie_service' => env('FUSIONAUTH_CCG_TARGET_ID_COMMUNICATIE_SERVICE'),
            'easyflex_service' => env('FUSIONAUTH_CCG_TARGET_ID_EASYFLEX_SERVICE'),
            'signhost_service' => env('FUSIONAUTH_CCG_TARGET_ID_SIGNHOST_SERVICE'),
            'user_service' => env('FUSIONAUTH_CCG_TARGET_ID_USER_SERVICE'),
            'datachecker_service' => env('FUSIONAUTH_CCG_TARGET_ID_DATACHECKER_SERVICE'),
            'verifai_service' => env('FUSIONAUTH_CCG_TARGET_ID_VERIFAI_SERVICE')
        ]
    ],

    'jet' => [
        'api_url' => env('JET_API_URL'),
        'oauth_url' => env('JET_OAUTH_URL'),
        'oauth' => [
            'grant_type' => 'password',
            'client_id' => env('JET_OAUTH_CLIENT_ID'),
            'client_secret' => env('JET_OAUTH_CLIENT_SECRET'),
            'username' => env('JET_OAUTH_USERNAME'),
            'password' => env('JET_OAUTH_PASSWORD'),
        ],
        'report_id' => env('JET_REPORT_ID')
    ],

    'recruitnow_service' => [
        'url' => env('RECRUITNOW_SERVICE_API_URL'),
    ],

    'signhost_service' => [
        'url' => env('SIGNHOST_SERVICE_API_URL'),
        'postback_url' => env('SIGNHOST_SERVICE_POSTBACK_URL'),
        'access_token' => env('SIGNHOST_SERVICE_ACCESS_TOKEN')
    ],

    'user_service' => [
        'url' => env('USER_SERVICE_API_URL')
    ],

    'verifai_service' => [
        'url' => env('VERIFAI_SERVICE_API_URL')
    ],

    'datachecker_service' => [
        'url' => env('DATACHECKER_SERVICE_API_URL')
    ],

    'wenote' => [
        'app_url' => env('WENOTE_APP_URL'),
    ],

    // 'postmark' => [
    //     'token' => env('POSTMARK_TOKEN'),
    // ],

    // 'ses' => [
    //     'key' => env('AWS_ACCESS_KEY_ID'),
    //     'secret' => env('AWS_SECRET_ACCESS_KEY'),
    //     'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    // ],

    // 'slack' => [
    //     'notifications' => [
    //         'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
    //         'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
    //     ],
    // ],

];
