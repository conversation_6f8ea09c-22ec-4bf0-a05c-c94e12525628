import preset from '../../../../vendor/filament/filament/tailwind.config.preset'

export default {
    presets: [preset],
    content: [
        './app/Filament/**/*.php',
        './resources/views/filament/**/*.blade.php',
        './vendor/filament/**/*.blade.php',
    ],
    theme: {
        colors: {
            transparent: 'transparent',
            current: 'currentColor',
            black: '#000000',
            white: '#ffffff',
            aqua: {
                100: '#e0f5ec',
                200: '#c2ebd9',
                300: '#a3e0c6',
                400: '#85d6b3',
                500: '#66cca0',
                600: '#52a380',
                700: '#3d7a60',
                800: '#295240',
                900: '#142920'
            },
            blue: {
                100: '#d5e3f5',
                200: '#aac7eb',
                300: '#80abe0',
                400: '#558fd6',
                500: '#2b73cc',
                600: '#225ca3',
                700: '#1a457a',
                800: '#112e52',
                900: '#091729'
            },
            'cool-gray': {
                100: '#f3f4f6',
                200: '#e5e7eb',
                300: '#d1d5db',
                400: '#9ca3af',
                500: '#6b7280',
                600: '#4b5563',
                700: '#374151',
                800: '#1f2937',
                900: '#111827'
            },
            gray: {
                100: '#e4e4e4',
                200: '#c8c8c8',
                300: '#adadad',
                400: '#919191',
                500: '#767676',
                600: '#5e5e5e',
                700: '#474747',
                800: '#2f2f2f',
                900: '#181818'
            },
            'warm-gray': {
                100: '#f5f5f4',
                200: '#e7e5e4',
                300: '#d6d3d1',
                400: '#a8a29e',
                500: '#78716c',
                600: '#57534e',
                700: '#44403c',
                800: '#292524',
                900: '#1c1917'
            },
            green: {
                100: '#cde1d8',
                200: '#9bc2b1',
                300: '#69a48a',
                400: '#378563',
                500: '#05673c',
                600: '#045230',
                700: '#033e24',
                800: '#022918',
                900: '#01150c'
            },
            navy: {
                100: '#F7F9FC',
                200: '#E9EFF7',
                300: '#71859d',
                400: '#4C5A6B',
                500: '#13335b',
                600: '#0E2542',
                700: '#0b1f37',
                800: '#081424',
                900: '#040a12'
            },
            orange: {
                100: '#fbe1cc',
                200: '#f8c499',
                300: '#f4a666',
                400: '#f18933',
                500: '#ed6b00',
                600: '#be5600',
                700: '#8e4000',
                800: '#5f2b00',
                900: '#2f1500'
            },
            purple: {
                100: '#d8d8ea',
                200: '#b0b1d5',
                300: '#898abf',
                400: '#6163aa',
                500: '#3a3c95',
                600: '#2e3077',
                700: '#232459',
                800: '#17183c',
                900: '#0c0c1e'
            },
            red: {
                100: '#f1d3d8',
                200: '#e3a7b1',
                300: '#d47c8a',
                400: '#c65063',
                500: '#b8243c',
                600: '#931d30',
                700: '#6e1624',
                800: '#4a0e18',
                900: '#25070c'
            },
            yellow: {
                100: '#fef1cd',
                200: '#fde39b',
                300: '#fbd469',
                400: '#fac637',
                500: '#f9b805',
                600: '#c79304',
                700: '#956e03',
                800: '#644a02',
                900: '#322501'
            }
        },
        fontFamily: {
            sans: [
                'ClearSans',
                'system-ui',
                '-apple-system',
                'BlinkMacSystemFont',
                '"Segoe UI"',
                'Roboto',
                'Ubuntu',
                '"Helvetica Neue"',
                'Oxygen',
                'Cantarell',
                'Arial',
                '"Noto Sans"',
                'sans-serif',
                '"Apple Color Emoji"',
                '"Segoe UI Emoji"',
                '"Segoe UI Symbol"',
                '"Noto Color Emoji"'
            ],
            cocogoose: [
                'CocogooseCondensed',
                'system-ui',
                '-apple-system',
                'BlinkMacSystemFont',
                '"Segoe UI"',
                'Roboto',
                'Ubuntu',
                '"Helvetica Neue"',
                'Oxygen',
                'Cantarell',
                'Arial',
                '"Noto Sans"',
                'sans-serif',
                '"Apple Color Emoji"',
                '"Segoe UI Emoji"',
                '"Segoe UI Symbol"',
                '"Noto Color Emoji"'
            ],
        },
        fontSize: {
            xs: '0.75rem',
            sm: '0.875rem',
            base: '1rem',
            lg: '1.125rem',
            xl: '1.25rem',
            '2xl': '1.5rem',
            '3xl': '1.875rem',
            '4xl': '2.25rem',
            '5xl': '3rem',
            '6xl': '4rem'
        },
        fontWeight: {
            normal: 400,
            bold: 700
        }
    },
    extend: {
        typography: require('./tailwind-typography.config')
    },
    plugins: [
        require('@tailwindcss/typography')
    ],
}
