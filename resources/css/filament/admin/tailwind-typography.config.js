module.exports = theme => ({
  DEFAULT: {
    css: [
      {
        color: theme('colors.navy.400'),
        strong: {
          color: theme('colors.navy.500'),
          fontWeight: '700'
        },
        ol: {
          'list-style': 'none',
          'counter-reset': 'list-counter'
        },
        'ol > li': {
          position: 'relative',
          'counter-increment': 'list-counter'
        },
        'ol > li::before': {
          alignItems: 'center',
          color: theme('colors.navy.500'),
          content: 'counter(list-counter) ""',
          display: 'inline-flex',
          fontSize: theme('fontSize.sm'),
          fontWeight: '700',
          height: theme('spacing.5'),
          justifyContent: 'center',
          left: `${theme('spacing.2')}`,
          position: 'absolute',
          top: theme('spacing[1]'),
          width: theme('spacing.5')
        },
        'ol > li::after': {
          backgroundColor: theme('colors.aqua.500') + '33',
          content: '""',
          display: 'inline-block',
          height: theme('spacing.5'),
          left: `${theme('spacing.1')}`,
          position: 'absolute',
          top: theme('spacing[1]'),
          transform: 'skewY(-7deg)',
          width: theme('spacing.5')
        },
        'ol > li:nth-child(odd)::after': {
          transform: 'skewY(7deg)'
        },
        ul: {
          'list-style': 'none'
        },
        'ul > li': {
          position: 'relative'
        },
        'ul > li::before': {
          alignItems: 'center',
          backgroundColor: 'initial',
          color: theme('colors.navy.500'),
          content: '"\u2022"',
          display: 'inline-flex',
          fontSize: theme('fontSize.lg'),
          fontWeight: '700',
          height: theme('spacing.7'),
          justifyContent: 'center',
          left: `-${theme('spacing.6')}`,
          position: 'absolute',
          top: theme('spacing[0.5]'),
          width: theme('spacing.5')
        },
        'ul > li::after': {
          backgroundColor: theme('colors.aqua.500') + '33',
          content: '""',
          display: 'inline-block',
          height: theme('spacing.5'),
          left: `-${theme('spacing.6')}`,
          position: 'absolute',
          top: theme('spacing[1]'),
          transform: 'skewY(-7deg)',
          width: theme('spacing.5')
        },
        'ul > li:nth-child(odd)::after': {
          transform: 'skewY(7deg)'
        },
        hr: {
          // borderColor: defaultTheme.colors.gray[300]
        },
        blockquote: {
          fontWeight: theme('font.bold'),
          color: theme('colors.navy.500')
          // borderLeftColor: defaultTheme.colors.gray[300]
        },
        h1: {
          color: theme('colors.navy.500'),
          fontFamily: theme('fontFamily.cocogoose').toString(),
          fontSize: theme('fontSize.6xl'),
          fontWeight: theme('fontWeight.normal'),
          lineHeight: 0.875,
          marginBottom: theme('margin.4'),
          marginTop: theme('fontSize.6xl'),
          textTransform: 'uppercase'
        },
        h2: {
          color: theme('colors.navy.500'),
          fontSize: theme('fontSize.3xl'),
          fontWeight: theme('fontWeight.bold'),
          lineHeight: theme('lineHeight.leading.tight'),
          marginBottom: theme('margin.3'),
          marginTop: theme('fontSize.3xl')
        },
        h3: {
          color: theme('colors.navy.500'),
          fontSize: theme('fontSize.2xl'),
          fontWeight: theme('fontWeight.bold'),
          lineHeight: theme('lineHeight.leading.tight'),
          marginBottom: theme('margin.2'),
          marginTop: theme('fontSize.2xl')
        },
        h4: {
          color: theme('colors.navy.500'),
          fontSize: theme('fontSize.xl'),
          fontWeight: theme('fontWeight.bold'),
          lineHeight: theme('lineHeight.leading.snug'),
          marginBottom: theme('margin.1'),
          marginTop: theme('fontSize.xl')
        },
        'figure figcaption': {
          // color: defaultTheme.colors.gray[600]
        },
        code: {
          color: theme('colors.navy.500'),
          fontWeight: theme('fontWeight.bold')
        },
        pre: {
          // color: defaultTheme.colors.gray[300],
          // backgroundColor: defaultTheme.colors.gray[800]
        },
        thead: {
          color: theme('colors.navy.500'),
          fontWeight: theme('fontWeight.bold')
          // borderBottomColor: defaultTheme.colors.gray[400],
        },
        'tbody tr': {
          // borderBottomColor: defaultTheme.colors.gray[300],
        }
      }
    ]
  },
  sm: {
    css: {
      'ol > li::before': {
        fontSize: theme('fontSize.xs'),
        height: theme('spacing[5]'),
        left: `-${theme('spacing[5.5]')}`,
        top: theme('spacing[0.]'),
        width: theme('spacing[4.5]')
      },
      'ul > li::before': {
        fontSize: theme('fontSize.md'),
        height: theme('spacing[6.5]'),
        left: `-${theme('spacing[5.5]')}`,
        top: theme('spacing[0]'),
        width: theme('spacing[4.5]')
      },
      'ol > li::after': {
        height: theme('spacing[4.5]'),
        left: `-${theme('spacing[5.5]')}`,
        top: theme('spacing[1]'),
        width: theme('spacing[4.5]')
      },
      'ul > li::after': {
        height: theme('spacing[4.5]'),
        left: `-${theme('spacing[5.5]')}`,
        top: theme('spacing[1]'),
        width: theme('spacing[4.5]')
      },
      h1: {
        fontSize: theme('fontSize.5xl'),
        lineHeight: 0.875,
        marginBottom: theme('margin.4'),
        marginTop: theme('fontSize.5xl')
      },
      h2: {
        fontSize: theme('fontSize.2xl'),
        lineHeight: theme('lineHeight.leading.tight'),
        marginBottom: theme('margin.3'),
        marginTop: theme('fontSize.2xl')
      },
      h3: {
        fontSize: theme('fontSize.xl'),
        lineHeight: theme('lineHeight.leading.tight'),
        marginBottom: theme('margin.2'),
        marginTop: theme('fontSize.xl')
      },
      h4: {
        fontSize: theme('fontSize.lg'),
        lineHeight: theme('lineHeight.leading.snug'),
        marginBottom: theme('margin.1'),
        marginTop: theme('fontSize.lg')
      }
    }
  },
  lg: {
    css: {
      'ol > li::before': {
        fontSize: theme('fontSize.md'),
        height: theme('spacing[6]'),
        left: `-${theme('spacing[7]')}`,
        width: theme('spacing[5.5]')
      },
      'ul > li::before': {
        fontSize: theme('fontSize.xl'),
        height: theme('spacing[7.5]'),
        left: `-${theme('spacing[6.5]')}`,
        width: theme('spacing[5.5]')
      },
      'ol > li::after': {
        height: theme('spacing[5.5]'),
        left: `-${theme('spacing[6.5]')}`,
        top: theme('spacing[1.5]'),
        width: theme('spacing[5.5]')
      },
      'ul > li::after': {
        height: theme('spacing[5.5]'),
        left: `-${theme('spacing[6.5]')}`,
        top: theme('spacing[1.5]'),
        width: theme('spacing[5.5]')
      },
      h1: {
        fontSize: theme('fontSize.7xl'),
        lineHeight: 0.875,
        marginBottom: theme('margin.4'),
        marginTop: theme('fontSize.7xl')
      },
      h2: {
        fontSize: theme('fontSize.4xl'),
        lineHeight: theme('lineHeight.leading.tight'),
        marginBottom: theme('margin.3'),
        marginTop: theme('fontSize.4xl')
      },
      h3: {
        fontSize: theme('fontSize.3xl'),
        lineHeight: theme('lineHeight.leading.tight'),
        marginBottom: theme('margin.2'),
        marginTop: theme('fontSize.3xl')
      },
      h4: {
        fontSize: theme('fontSize.2xl'),
        lineHeight: theme('lineHeight.leading.snug'),
        marginBottom: theme('margin.1'),
        marginTop: theme('fontSize.2xl')
      }
    }
  },
  xl: {
    css: {
      'ol > li::before': {
        fontSize: theme('fontSize.lg'),
        height: theme('spacing[6]'),
        left: `-${theme('spacing[7.5]')}`,
        width: theme('spacing[6]')
      },
      'ul > li::before': {
        fontSize: theme('fontSize.2xl'),
        height: theme('spacing[8]'),
        left: `-${theme('spacing[7.5]')}`,
        width: theme('spacing[6]')
      },
      'ol > li::after': {
        height: theme('spacing[6]'),
        left: `-${theme('spacing[7]')}`,
        top: theme('spacing[1.5]'),
        width: theme('spacing[6]')
      },
      'ul > li::after': {
        height: theme('spacing[6]'),
        left: `-${theme('spacing[7]')}`,
        top: theme('spacing[1.5]'),
        width: theme('spacing[6]')
      },
      h1: {
        fontSize: theme('fontSize.8xl'),
        lineHeight: 0.875,
        marginBottom: theme('margin.4'),
        marginTop: theme('fontSize.8xl')
      },
      h2: {
        fontSize: theme('fontSize.5xl'),
        lineHeight: theme('lineHeight.leading.tight'),
        marginBottom: theme('margin.3'),
        marginTop: theme('fontSize.5xl')
      },
      h3: {
        fontSize: theme('fontSize.4xl'),
        lineHeight: theme('lineHeight.leading.tight'),
        marginBottom: theme('margin.2'),
        marginTop: theme('fontSize.4xl')
      },
      h4: {
        fontSize: theme('fontSize.3xl'),
        lineHeight: theme('lineHeight.leading.snug'),
        marginBottom: theme('margin.1'),
        marginTop: theme('fontSize.3xl')
      }
    }
  },
  '2xl': {
    css: {
      'ol > li::before': {
        fontSize: theme('fontSize.xl'),
        height: theme('spacing[6.5]'),
        left: `-${theme('spacing.8')}`,
        width: theme('spacing[6.5]')
      },
      'ul > li::before': {
        fontSize: theme('fontSize.3xl'),
        height: theme('spacing[8.5]'),
        left: `-${theme('spacing.8')}`,
        width: theme('spacing[6.5]')
      },
      'ol > li::after': {
        height: theme('spacing[6.5]'),
        left: `-${theme('spacing[7.5]')}`,
        top: theme('spacing[1.5]'),
        width: theme('spacing[6.5]')
      },
      'ul > li::after': {
        height: theme('spacing[6.5]'),
        left: `-${theme('spacing[7.5]')}`,
        top: theme('spacing[1.5]'),
        width: theme('spacing[6.5]')
      },
      h1: {
        fontSize: theme('fontSize.8xl'),
        lineHeight: 0.875,
        marginBottom: theme('margin.4'),
        marginTop: theme('fontSize.8xl')
      },
      h2: {
        fontSize: theme('fontSize.6xl'),
        lineHeight: theme('lineHeight.leading.tight'),
        marginBottom: theme('margin.3'),
        marginTop: theme('fontSize.6xl')
      },
      h3: {
        fontSize: theme('fontSize.5xl'),
        lineHeight: theme('lineHeight.leading.tight'),
        marginBottom: theme('margin.2'),
        marginTop: theme('fontSize.5xl')
      },
      h4: {
        fontSize: theme('fontSize.4xl'),
        lineHeight: theme('lineHeight.leading.snug'),
        marginBottom: theme('margin.1'),
        marginTop: theme('fontSize.4xl')
      }
    }
  }
})
