.view-field {
    // container-name: view-field;
    // container-type: inline-size;

    &__content {
        @apply grid gap-x-4 gap-y-2 pt-2 text-navy-500 border-t border-gray-200;

        // @container view-field (width > 640px) {
        //     @apply grid-cols-[minmax(0,320px)_minmax(0,1fr)];
        // }
    }

    &__label {
        @apply text-ellipsis overflow-hidden font-bold;
    }

    &__value {
        @apply text-ellipsis overflow-hidden;
    }
}
