.fi-modal > div > div {
    @apply inset-0;
}

.fi-modal-window {
    @apply max-h-[90dvh] overflow-y-auto max-w-4xl rounded #{!important};
}

.fi-modal-header {
    @apply sticky top-0 bg-white py-6;

    > div:nth-child(2):has(.fi-modal-icon) {
        // removes header-icon
        @apply hidden;
    }
}

.fi-modal-close-btn {
    .fi-icon-btn-icon {
        @apply w-8 h-8;
    }
}

.fi-modal-heading {
    @apply text-navy-500 text-2xl font-bold mb-2;
}

.fi-modal-footer {
    @apply sticky bottom-0 pb-6 bg-white mt-0;
}

.fi-modal-description {
    @apply text-base;
}
