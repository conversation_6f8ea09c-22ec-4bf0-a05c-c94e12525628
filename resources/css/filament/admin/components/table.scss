table.fi-ta-table {
    @apply border-x rounded w-full;

    &--custom {
        @apply border;

        th, td {
            @apply px-3 py-4 text-start align-top;

            &:first-child {
                @apply ps-6;
            }
        }
    }

    thead {
        tr {
            @apply bg-navy-100;
        }
    }

    tbody {
        @apply divide-navy-200;

        tr {
            @apply bg-white;

            &:hover {
                @apply bg-navy-100/50;
            }

            td:first-child a .fi-ta-text-item-label {
                @apply text-blue-500 font-bold underline underline-offset-2 decoration-blue-200 transition;

                &:hover {
                    @apply underline-offset-0;
                }
            }
        }
    }
}

.fi-ta-ctn {
    @apply rounded bg-transparent ring-0 shadow-none overflow-visible;

    .fi-ta-content {
        @apply divide-transparent rounded;
    }
}

.fi-ta-header-ctn {
    @apply bg-transparent;
}

.fi-ta-table:has(.fi-ta-actions-cell) thead th:last-child {
    @apply sticky right-0 bg-gray-50 shadow;
}

.fi-ta-actions-cell {

}

.fi-ta-cell {
    .fi-ta-text-item-label {
        @apply text-base;
    }

}

.fi-ta-header-toolbar {
    @apply px-0 #{!important};
    @apply pb-4 pt-0 mr-2;
}
