.fi-btn-color-primary {
    @apply rounded font-bold;
    @apply hover:bg-orange-400 #{!important};
}

.fi-btn-color-gray {
    @apply px-6 py-3 rounded font-bold;
}

.fi-ac-link-action {
    span {
        @apply font-bold underline;
    }

    &.fi-color-info span {
        @apply text-blue-500;
    }
}

.fi-btn {
    @apply rounded font-bold;
}

.fi-btn-outlined {
    @apply ring-2;

    &.fi-color-info {
        @apply text-blue-500 ring-blue-500;
    }
}

.fi-ac-btn-action.fi-ac-btn-action-chip {
    @apply text-base font-bold py-2 px-3 rounded-none ring-0 border-y-2 border-x-[1px];

    &:first-child {
        @apply rounded-s border-s-2;
    }

    &:last-child {
        @apply rounded-e border-e-2;
    }

    &:not(.fi-btn-outlined) {
        @apply border-custom-600;
    }

    &.fi-ac-btn-action--single-action {
        @apply rounded-full;
    }

    &.fi-btn-outlined {
        &.fi-color-success {
            @apply text-green-500 border-green-100;
        }

        &.fi-color-info {
            @apply text-blue-500 border-gray-100;

            &:not(.fi-btn-outlined) {
                @apply border-blue-500;
            }
        }

        &.fi-color-danger {
            @apply text-red-500 border-red-100;
        }
    }
}

.fi-ac-icon-btn-action {
    @apply border p-1 w-12 h-12 bg-white;

    .fi-icon-btn-icon {
        @apply fill-navy-400;
    }

    .fi-icon-btn-badge-ctn {
        @apply bg-orange-500 rounded-full w-4 h-4 leading-none pt-px;

        .fi-badge {
            @apply text-white bg-transparent pt-0;
        }
    }
}
