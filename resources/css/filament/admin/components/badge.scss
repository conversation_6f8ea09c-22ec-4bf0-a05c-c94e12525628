.fi-badge {
    @apply font-bold ring-0 bg-navy-100 text-navy-600 p-1 leading-none rounded shadow-none;

    &.fi-color-danger {
        @apply bg-red-100 text-red-500;
    }

    &.fi-color-info {
        @apply bg-blue-100/50 text-blue-500;
    }

    &.fi-color-success {
        @apply bg-aqua-100 text-aqua-700;
    }

    &.fi-color-warning {
        @apply bg-orange-100 text-orange-500;
    }

    .fi-badge-delete-button {
        @apply absolute inset-0 p-0;

        svg {
            @apply absolute top-1/2 -translate-y-1/2 right-2;
        }
    }

    &:has(.fi-badge-delete-button) {
        @apply relative cursor-pointer pe-4;
    }

    .truncate {
        all: unset !important;
    }
}
