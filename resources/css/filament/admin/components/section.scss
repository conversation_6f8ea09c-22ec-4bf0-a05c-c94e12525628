.fi-section {
    @apply ring-0;

    &:not(&--sticky) {
        @apply rounded border-0 h-full shadow;
    }

    &--sticky {
        @apply sticky top-4 h-auto bg-transparent;

        > .fi-section-content-ctn > .fi-section-content {
            @apply p-0;
        }
    }

    &--actions {
        @apply pt-4;
    }

    .fi-in-tabs {
        @apply ring-0 shadow-none rounded-none;

        .fi-in-tabs-tab {
            @apply px-0 pb-0;
        }

        .fi-fieldset {
            @apply px-0;
        }
    }
}

.fi-section-header {
    @apply pb-0
}

.fi-section-header-heading {
    @apply text-navy-500 text-xl font-bold;

    .fi-section--actions & {
        @apply text-4xl leading-none;
    }
}

.fi-section-content-ctn {
    @apply border-0;
}

.fi-fo-component-ctn {
    @apply gap-3;

    > div:has(.fi-section--row-2) {
        @apply order-9 2xl:order-none 2xl:row-span-2;
    }
}

