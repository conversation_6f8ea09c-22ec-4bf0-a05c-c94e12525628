.fi-sidebar-nav {
    @apply order-first p-0 px-2 bg-navy-600 overflow-visible;
}

.fi-sidebar-nav-groups {
    @apply gap-0;
}

.fi-sidebar-group {
    @apply relative w-[var(--sidebar-width)];
}

.fi-sidebar-group-button {
    @apply grid p-0;
}

.fi-sidebar-group-label {
    @apply text-white text-center;
}

.fi-sidebar-item-grouped-border {
    @apply hidden;
}

.fi-sidebar-group-items {
    @apply gap-0;

    .fi-sidebar-group:has(.fi-sidebar-group-button) & {
        @apply absolute left-full bg-white w-64 hidden;
        box-shadow: 0 4px 24px 0 #0000000D, 0 1px 8px 0 #0000000D;
    }

    .fi-sidebar-group:has(.fi-sidebar-group-button):hover & {
        @apply block;
    }
}

.fi-sidebar-group-label, .fi-sidebar-item-label {
    @apply font-bold;
}

.fi-sidebar-item-button, .fi-sidebar-group-button {
    @apply transition px-3 py-4 rounded-none;

    .fi-sidebar-group:not(:has(.fi-sidebar-group-button)) & {
        @apply grid gap-1 justify-center text-center;
    }

    .fi-sidebar-group:has(.fi-sidebar-group-button) .fi-sidebar-group-items & {
        @apply ps-5
    }

    .fi-badge {
        @apply hidden;
    }
}

.fi-sidebar-item-active .fi-sidebar-item-button,
.fi-sidebar-item .fi-sidebar-item-button:hover,
.fi-sidebar-group .fi-sidebar-group-button:hover {
    @apply bg-purple-800;
    box-shadow: 0 1px 0 0 #FFFFFF33, 0 1px 0 0 #FFFFFF33 inset;

    .fi-sidebar-group:has(.fi-sidebar-group-button) & {
        @apply bg-navy-200;
    }
}

.fi-sidebar-item-icon, .fi-sidebar-group-icon {
    @apply mx-auto stroke-white;

    .fi-sidebar-group:has(.fi-sidebar-group-button) .fi-sidebar-group-items & {
        @apply stroke-navy-500 mx-0;
    }
}

.fi-sidebar-item-label {
    @apply text-white font-bold whitespace-normal;

    .fi-sidebar-group:has(.fi-sidebar-group-button) & {
        @apply text-navy-500;
    }
}
