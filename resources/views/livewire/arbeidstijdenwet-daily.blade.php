<tr class="border-b">
    <td class="text-sm ps-3"><b><a href="{{ $applicant_url }}" target="_blank">{{ $applicant_name }}</a></b></td>
    <td class="text-sm ps-3">{{ $scoober_id }}</td>
    <td class="text-sm ps-3">{{ $total_hours }}</td>
    <td class="text-sm ps-3">{{ $date }}</td>
    <td>
        <button class="fi-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-btn-color-gray fi-color-gray fi-size-md fi-btn-size-md gap-1.5 px-3 py-2 text-sm inline-grid shadow-sm bg-white text-gray-950 hover:bg-gray-50 dark:bg-white/5 dark:text-white dark:hover:bg-white/10 ring-1 ring-gray-950/10 dark:ring-white/20 [input:checked+&]:bg-gray-400 [input:checked+&]:text-white [input:checked+&]:ring-0 [input:checked+&]:hover:bg-gray-300 dark:[input:checked+&]:bg-gray-600 dark:[input:checked+&]:hover:bg-gray-500 fi-ac-action mb-2 mt-2 fi-ac-btn-action" x-on:click="$dispatch('openModal',
            {
            component: 'arbeidstijden-wet-modal',
            arguments:
                {
                    applicant: {{ $applicant_id }},
                    date: '{{ $original_date }}'
                }
            })">Info</button>
    </td>
</tr>
