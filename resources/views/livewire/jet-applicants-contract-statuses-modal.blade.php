<div>
    <div class="fi-ta">
        <div class="fi-ta-content">
            <table class="fi-ta-table fi-ta-table--custom">
                <thead>
                <tr>
                    <th>Datum</th>
                    <th>Documenten</th>
                    <th>Status</th>
                </tr>
                </thead>

                <tbody>
                @foreach ($contracts as $contract)
                    <tr class="border-t border-gray-200">
                        <td class="text-navy-500 font-bold">
                            {{ \Carbon\Carbon::parse($contract['attributes']['date'])->format('H:i d-m-Y') }}
                        </td>

                        <td class="text-navy-500">
                            <ul class="list-inside list-disc">
                                @foreach($contract['attributes']['files'] as $file)
                                    <li>{{$file['file_name'] }}</li>
                                @endforeach
                            </ul>
                        </td>

                        <td>
                            @php
                                $code = $contract['attributes']['status_code'];
                                $color = 'warning';

                                if($code === 30) {
                                    $color = 'success';
                                } else if($code > 30) {
                                    $color = 'danger';
                                }
                            @endphp

                            <x-filament::badge color="{{ $color }}">
                                {{ $statuses[$code] }}
                            </x-filament::badge>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>
