<div
    x-cloak
    x-transition:enter="transform transition ease-in-out duration-500 sm:duration-700"
    x-transition:enter-start="translate-x-full"
    x-transition:enter-end="translate-x-0"
    x-transition:leave="transform transition ease-in-out duration-500 sm:duration-700"
    x-transition:leave-start="translate-x-0"
    x-transition:leave-end="translate-x-full"
    class="pointer-events-auto w-screen max-w-md"
>
    <div class="bg-white p-4 sm:px-6 sm:py-4 border-b border-gray-150">
         <h3 class="text-lg leading-6 font-medium text-gray-900">{{ $title }}</h3>
    </div>

    <div class="bg-white px-4 sm:p-6">
        <div class="space-y-6">
            <ul>
                <li>Scoober ID: {{ $applicant->scoober_id }}</li>
                <li>Easyflex ID: {{ $applicant->easyflex_id }}</li>
                <li>Datum: {{ $date }}</li>
            </ul>
        </div>
    </div>

    <table border="1" style="width: 100%; border-collapse: collapse;">
        <thead>
        <tr>
            <th style="padding: 8px; text-align: left;">Date/Weeknr</th>
            <th style="padding: 8px; text-align: left;">Import ID</th>
            <th style="padding: 8px; text-align: left;">Hours</th>
        </tr>
        </thead>
        <tbody>
        @foreach($total_hours as $hour_registration)
            <tr>
                <td style="padding: 8px;">{{ \Carbon\Carbon::createFromFormat('Y-m-d', $hour_registration->date)->format('d-m-Y') }}</td>
                <td style="padding: 8px;">{{ $hour_registration->import_id }}</td>
                <td style="padding: 8px;">{{ $hour_registration->hours }}</td>
            </tr>
        @endforeach
        </tbody>
    </table>

</div>
