<x-filament-panels::page>
    @livewire('create-export-button', ['record' => $record, 'weeknr' => $weeknr, 'workedHours' => json_encode($workedHours)])

    <div x-data="{ tab: 'tab1' }">
        <x-filament::tabs label="Content tabs">

            <x-filament::tabs.item @click="tab = 'tab1'" :alpine-active="'tab === \'tab1\''">
                18+
            </x-filament::tabs.item>

            <x-filament::tabs.item @click="tab = 'tab2'" :alpine-active="'tab === \'tab2\''">
                16/17
            </x-filament::tabs.item>

            <x-filament::tabs.item @click="tab = 'tab3'" :alpine-active="'tab === \'tab3\''">
                Workstudent
            </x-filament::tabs.item>

        </x-filament::tabs>

        <x-filament::section x-show="tab === 'tab1'" x-cloak>
            <h2 class="fi-header-heading text-3xl font-bold tracking-tight text-gray-950 dark:text-white sm:text-3xl">Daily</h2>
            <div>
                @if(!empty($regularData['daily']))
                    <x-filament.components.daily-table :data="$regularData['daily']" />
                @else
                    <p>Alles in orde</p>
                @endif
            </div>

            <h2 class="fi-header-heading text-3xl font-bold tracking-tight text-gray-950 dark:text-white sm:text-3xl">Weekly</h2>

            <div>
                @if(!empty($regularData['weekly']))
                    <x-filament.components.weekly-table :data="$regularData['weekly']" />
                @else
                    <p>Alles in orde</p>
                @endif
            </div>
        </x-filament::section>


        <x-filament::section x-show="tab === 'tab2'" x-cloak>
            <h2 class="fi-header-heading text-3xl font-bold tracking-tight text-gray-950 dark:text-white sm:text-3xl">Daily</h2>

            <div>
                @if(!empty($underageData['daily']))
                    <x-filament.components.daily-table :data="$underageData['daily']" />
                @else
                    <p>Alles in orde</p>
                @endif
            </div>

            <h2 class="fi-header-heading text-3xl font-bold tracking-tight text-gray-950 dark:text-white sm:text-3xl">Weekly</h2>

            <div>
                @if(!empty($underageData['weekly']))
                    <x-filament.components.weekly-table :data="$underageData['weekly']" />
                @else
                    <p>Alles in orde</p>
                @endif
            </div>
        </x-filament::section>

        <x-filament::section x-show="tab === 'tab3'" x-cloak>
            <h2 class="fi-header-heading text-3xl font-bold tracking-tight text-gray-950 dark:text-white sm:text-3xl">Weekly</h2>

            <div>
                @if(!empty($studentData['weekly']))
                    <x-filament.components.weekly-table :data="$studentData['weekly']" />
                @else
                    <p>Alles in orde</p>
                @endif
            </div>
        </x-filament::section>
    </div>

    <x-filament-actions::modals />
    @livewire('wire-elements-modal')

</x-filament-panels::page>
