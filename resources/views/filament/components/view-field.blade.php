<div class="view-field">
    <div class="view-field__content">
        <span class="view-field__label" title="{{ $getLabel() }}">
            {{ $getLabel() }}
        </span>

        @php
            $value = $getState();
        @endphp

        <span class="view-field__value" title="{{ is_array($value) ? $value['value'] : $value }}">
        @if($value === true)
                Ja
            @elseif($value === false)
                Nee
            @elseif(is_array($value))
                @if($value['type'] === 'badge')
                    <span class="inline-block h-full">
                        <x-filament::badge color="{{ $value['color'] }}" tooltip="{{ $value['tooltip'] ?? '' }}">
                            {{ $value['value'] }}
                        </x-filament::badge>
                    </span>
                @endif
            @else
                {!! nl2br($value) !!}
            @endif
        </span>
    </div>
</div>
