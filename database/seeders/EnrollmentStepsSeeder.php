<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

use App\Models\JetEnrollmentStep;

class EnrollmentStepsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $enrollmentSteps = [
            [
                'step_id' => 1,
                'step_name' => 'Submit Lead',
                'step_color' => 'info',
            ],
            [
                'step_id' => 2,
                'step_name' => 'Receive Applicant',
                'step_color' => 'info',
            ],
            [
                'step_id' => 3,
                'step_name' => 'Send Registration',
                'step_color' => 'info',
            ],
            [
                'step_id' => 4,
                'step_name' => 'Activate Account',
                'step_color' => 'info',
            ],
            [
                'step_id' => 5,
                'step_name' => 'Finalize Profile',
                'step_color' => 'info',
            ],
            [
                'step_id' => 6,
                'step_name' => 'Scan ID',
                'step_color' => 'info',
            ],
            [
                'step_id' => 7,
                'step_name' => 'Verify ID (DataChecker)',
                'step_color' => 'info',
            ],
            [
                'step_id' => 8,
                'step_name' => 'Scan Driver License',
                'step_color' => 'info',
            ],
            [
                'step_id' => 9,
                'step_name' => 'Verify Driver License (DataChecker)',
                'step_color' => 'info',
            ],
            [
                'step_id' => 10,
                'step_name' => 'Verify Scans',
                'step_color' => 'warning',
            ],
            [
                'step_id' => 11,
                'step_name' => 'Control Registration',
                'step_color' => 'warning',
            ],
            [
                'step_id' => 12,
                'step_name' => 'Sign Contract',
                'step_color' => 'info',
            ],
            [
                'step_id' => 13,
                'step_name' => 'Send Registration to JET',
                'step_color' => 'info',
            ],
            [
                'step_id' => 14,
                'step_name' => 'Finalize Easyflex Registration',
                'step_color' => 'warning',
            ],
            [
                'step_id' => 15,
                'step_name' => 'Fetch Scoober ID',
                'step_color' => 'info',
            ],
            [
                'step_id' => 16,
                'step_name' => 'Create Easyflex Job',
                'step_color' => 'success',
            ]
        ];
        JetEnrollmentStep::upsert($enrollmentSteps, uniqueBy: ['step_id']);
        JetEnrollmentStep::selectRaw("setval('enrollment_steps_new_step_id_seq'::regclass, max(step_id))")->get();
    }
}
