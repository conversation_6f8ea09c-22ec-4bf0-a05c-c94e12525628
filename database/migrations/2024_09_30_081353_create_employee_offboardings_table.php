<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('employee_offboarding', function (Blueprint $table) {
            $table->id();
            $table->foreignId('applicant_id')->references('applicant_id')->on('applicants');
            $table->string('reason');
            $table->date('offboarding_date');
            $table->json('plaatsing_ids');
            $table->integer('contract_id');
            $table->boolean('signalering')->default(false);
            $table->boolean('processed_reserveringen')->default(false);
            $table->boolean('processed_in_uwv')->default(false);
            $table->boolean('is_completed')->default(false);
            $table->foreignId('user_id')->references('id')->on('users');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('employee_offboarding');
    }
};
