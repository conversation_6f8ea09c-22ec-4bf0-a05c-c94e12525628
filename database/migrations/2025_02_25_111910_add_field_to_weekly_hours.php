<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('weekly_hours', function (Blueprint $table) {
            $table->integer('short_leave_minutes')->default(0);
            $table->integer('unpaid_leave_minutes')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('weekly_hours', function (Blueprint $table) {
            $table->dropColumn('short_leave_minutes');
            $table->dropColumn('unpaid_leave_minutes');
        });
    }
};
