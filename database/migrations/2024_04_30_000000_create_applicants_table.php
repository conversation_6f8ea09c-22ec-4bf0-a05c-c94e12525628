<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('applicants', function (Blueprint $table) {
            $table->bigIncrements('applicant_id');
            $table->bigInteger('wenote_id')->nullable();
            $table->bigInteger('easyflex_id')->nullable();
            $table->string('flexapp_id')->nullable();

            $table->string('lead_id');
            $table->string('pre_contract_id');
            $table->string('contact_id')->nullable();
            $table->string('scoober_id')->nullable();

            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->string('mobile_phone')->nullable();

            $table->string('emergency_contact_name')->nullable();
            $table->string('emergency_contact_phone')->nullable();

            $table->string('date_of_birth')->nullable();
            $table->string('gender')->nullable();
            $table->string('country_of_nationality')->nullable();

            $table->string('street')->nullable();
            $table->string('house_number')->nullable();
            $table->string('house_number_addition')->nullable();
            $table->string('postcode')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable();

            $table->string('preferred_language')->nullable();
            $table->string('preferred_communication_method')->nullable();
            $table->string('active_transportation_type')->nullable();
            $table->string('active_vehicle_ownership')->nullable();
            $table->string('delivery_area')->nullable();

            $table->boolean('previous_experience')->nullable();
            $table->boolean('is_student')->nullable();

            $table->string('residence_permit_type')->nullable();
            $table->string('residence_permit_expiration_date')->nullable();

            $table->string('driving_license_number')->nullable();
            $table->string('driving_license_expiration_date')->nullable();
            $table->timestamp('lead_created_date')->nullable();

            $table->date('pre_contract_start_date')->nullable();
            $table->decimal('pre_contract_minimum_hours', 8, 2)->nullable();
            $table->string('pre_contract_function')->default('Courier');
            $table->timestamp('pre_contract_created_date');

            $table->boolean('is_test')->nullable();

            $table->timestamps();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('applicants');
    }
};
