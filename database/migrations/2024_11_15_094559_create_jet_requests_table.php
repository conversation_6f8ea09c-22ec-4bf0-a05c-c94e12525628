<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('jet_courier_document_request_logs', function (Blueprint $table) {
            $table->id();
            $table->string('lead_id');
            $table->string('entity');
            $table->string('entity_id')
                ->nullable();
            $table->string('http_method')
                ->nullable();
            $table->integer('http_status_code');
            $table->json('payload')
                ->nullable();
            $table->string('error_code')
                ->nullable();
            $table->string('error_message')
                ->nullable();
            $table->json('error_fields')
                ->nullable()
                ->default('[]');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('jet_courier_document_request_logs');
    }
};
