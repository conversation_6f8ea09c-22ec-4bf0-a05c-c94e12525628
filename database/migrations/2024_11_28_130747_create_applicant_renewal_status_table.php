<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */

    public function up()
    {
        Schema::create('applicant_renewal_status', function (Blueprint $table) {
            $table->bigIncrements('applicant_renewal_status_id');
            $table->bigInteger('applicant_id')->unique();
            $table->timestamps();

            $table->text('notes')->nullable();

            $table->smallInteger('step_in_process')->nullable();
            $table->string('step_name')->nullable();

            $table->timestamp('step_0_started_at')->nullable();
            $table->timestamp('step_0_finished_at')->nullable();

            $table->timestamp('step_1_started_at')->nullable();
            $table->timestamp('step_1_finished_at')->nullable();

            $table->timestamp('step_2_started_at')->nullable();
            $table->timestamp('step_2_finished_at')->nullable();

            $table->timestamp('step_3_started_at')->nullable();
            $table->timestamp('step_3_finished_at')->nullable();

            $table->timestamp('step_4_started_at')->nullable();
            $table->timestamp('step_4_finished_at')->nullable();

            $table->timestamp('step_5_started_at')->nullable();
            $table->timestamp('step_5_finished_at')->nullable();

            $table->timestamp('step_6_started_at')->nullable();
            $table->timestamp('step_6_finished_at')->nullable();

            $table->timestamp('step_7_started_at')->nullable();
            $table->timestamp('step_7_finished_at')->nullable();

            $table->timestamp('step_8_started_at')->nullable();
            $table->timestamp('step_8_finished_at')->nullable();

            $table->timestamp('step_9_started_at')->nullable();
            $table->timestamp('step_9_finished_at')->nullable();

            $table->timestamp('step_10_started_at')->nullable();
            $table->timestamp('step_10_finished_at')->nullable();

            $table->timestamp('step_11_started_at')->nullable();
            $table->timestamp('step_11_finished_at')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('applicant_renewal_status');
    }
};
