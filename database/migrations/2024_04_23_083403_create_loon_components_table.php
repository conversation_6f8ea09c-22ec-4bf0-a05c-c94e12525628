<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loon_components', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->string('value')->nullable();
            $table->string('unit')->nullable();
            $table->tinyInteger('status');
            $table->unsignedInteger('loon_component_type_id');
            $table->foreign('loon_component_type_id') ->references('id')->on('loon_component_types');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loon_components');
    }
};
