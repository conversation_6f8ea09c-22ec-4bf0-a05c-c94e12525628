<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('residence_permit_renewals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('applicant_id')->references('applicant_id')->on('applicants');
            $table->foreignId('twv_information_id')->nullable()->constrained('twv_information');
            $table->string('datachecker_transaction_id')->nullable();
            $table->string('notes')->nullable();
            $table->jsonb('reminder_dates')->nullable();
            $table->boolean('renewal_completed')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('residence_permit_renewals');
    }
};
