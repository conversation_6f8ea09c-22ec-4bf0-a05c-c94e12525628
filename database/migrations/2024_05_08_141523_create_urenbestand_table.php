<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('urenbestand', function (Blueprint $table) {
            $table->id();
            $table->string('driver_id');
            $table->date('date');
            $table->decimal('hours', 5)->nullable();
            $table->decimal('amount', 19, 4)->nullable();
            $table->string('city');
            $table->string('weeknr');
            $table->unsignedInteger('loon_component_id');
            $table->foreign('loon_component_id') ->references('id')->on('loon_components');
            $table->unsignedBigInteger('import_id')->nullable();
            $table->foreign('import_id')->references('id')->on('imports')->onDelete('cascade');
            $table->string('reason')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('urenbestand');
    }
};
