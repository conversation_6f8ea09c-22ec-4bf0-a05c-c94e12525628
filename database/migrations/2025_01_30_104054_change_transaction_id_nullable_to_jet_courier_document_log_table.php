<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('jet_courier_document_request_logs', function (Blueprint $table) {
            $table->uuid('transaction_id')
                ->nullable(false)
                ->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('jet_courier_document_request_logs', function (Blueprint $table) {
            $table->uuid('transaction_id')
                ->nullable()
                ->change();
        });
    }
};
