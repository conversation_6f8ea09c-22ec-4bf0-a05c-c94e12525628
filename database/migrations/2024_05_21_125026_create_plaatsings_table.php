<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plaatsingen', function (Blueprint $table) {
            $table->id();
            $table->foreignId('applicant_id')->references('applicant_id')->on('applicants');
            $table->unsignedBigInteger('plaatsing_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plaatsingen');
    }
};
