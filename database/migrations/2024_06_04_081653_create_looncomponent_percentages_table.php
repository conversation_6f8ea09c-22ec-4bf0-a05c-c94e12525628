<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('looncomponent_percentages', function (Blueprint $table) {
            $table->id();
            $table->string('function');
            $table->unsignedBigInteger('age');
            $table->decimal('percentage', 5, 2);
            $table->decimal('wage_per_hour', 10, 2);
            $table->date('start_date');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('looncomponent_percentages');
    }
};
