<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contract_renewals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('applicant_id')->references('applicant_id')->on('applicants');
            $table->decimal('wage_per_hour', 8, 2);
            $table->integer('contract_hours');
            $table->boolean('is_wage_with_vacation_allowance');
            $table->boolean('is_wage_with_vacation_days');
            $table->string('delivery_area');
            $table->date('contract_start_date');
            $table->date('contract_end_date')->nullable(); // Nullable for open-ended contracts
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contract_renewals');
    }
};
