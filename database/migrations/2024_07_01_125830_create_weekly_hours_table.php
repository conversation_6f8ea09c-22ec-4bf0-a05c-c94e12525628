<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('weekly_hours', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('applicant_id');
            $table->foreign('applicant_id')->references('applicant_id')->on('applicants');
            $table->integer('week_number');
            $table->integer('year');
            $table->integer('worked_minutes')->default(0);
            $table->integer('ort_minutes')->default(0);
            $table->integer('sickness_minutes')->default(0);
            $table->integer('vacation_minutes')->default(0);
            $table->unique(['applicant_id', 'week_number']);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('weekly_hours');
    }
};
