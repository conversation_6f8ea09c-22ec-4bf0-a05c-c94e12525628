<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('applicants', function (Blueprint $table) {
            $table->string('lead_id')->nullable()->change();
            $table->string('pre_contract_id')->nullable()->change();

            $table->string('contract_id')->nullable();
            $table->boolean('pre_contract_is_indefinite')->nullable();
            $table->decimal('number_of_previous_contracts', 8, 2)->nullable();
            $table->decimal('months_of_work_experience', 8, 2)->nullable();
            $table->date('first_contract_start_date')->nullable();
            $table->boolean('is_import')->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('applicants', function (Blueprint $table) {
            $table->string('lead_id')->change();
            $table->string('pre_contract_id')->change();

            $table->dropColumn('contract_id');
            $table->dropColumn('pre_contract_is_indefinite');
            $table->dropColumn('number_of_previous_contracts');
            $table->dropColumn('months_of_work_experience');
            $table->dropColumn('first_contract_start_date');
            $table->dropColumn('is_import');
        });
    }
};
