<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('applicants_enrollment_status', function (Blueprint $table) {
            $table->bigIncrements('applicant_enrollment_status_id');
            $table->bigInteger('applicant_id')->unique();
            $table->timestamps();

            $table->string('agency_status');
            $table->string('agency_status_reason')->nullable();
            $table->timestamp('agency_status_updated_at')->nullable();

            $table->boolean('on_hold')->nullable();
            $table->string('on_hold_reason')->nullable();
            $table->timestamp('on_hold_started_at')->nullable();
            $table->timestamp('on_hold_finished_at')->nullable();

            $table->text('notes')->nullable();

            $table->smallInteger('step_in_process')->nullable();
            $table->string('step_name')->nullable();

            $table->timestamp('step_0_started_at')->nullable();
            $table->timestamp('step_0_finished_at')->nullable();

            $table->timestamp('step_1_started_at')->nullable();
            $table->timestamp('step_1_finished_at')->nullable();

            $table->timestamp('step_2_started_at')->nullable();
            $table->timestamp('step_2_finished_at')->nullable();

            $table->timestamp('step_3_started_at')->nullable();
            $table->timestamp('step_3_finished_at')->nullable();

            $table->timestamp('step_4_started_at')->nullable();
            $table->timestamp('step_4_finished_at')->nullable();

            $table->timestamp('step_5_started_at')->nullable();
            $table->timestamp('step_5_finished_at')->nullable();

            $table->timestamp('step_6_started_at')->nullable();
            $table->timestamp('step_6_finished_at')->nullable();

            $table->timestamp('step_7_started_at')->nullable();
            $table->timestamp('step_7_finished_at')->nullable();

            $table->timestamp('step_8_started_at')->nullable();
            $table->timestamp('step_8_finished_at')->nullable();

            $table->timestamp('step_9_started_at')->nullable();
            $table->timestamp('step_9_finished_at')->nullable();

            $table->timestamp('step_10_started_at')->nullable();
            $table->timestamp('step_10_finished_at')->nullable();
        });
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('applicants_enrollment_status');
    }
};
