<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

use Database\Seeders\EnrollmentStepsSeeder;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('enrollment_steps_new', function (Blueprint $table) {
            $table->smallIncrements('step_id');
            $table->string('step_name');
            $table->string('step_color');
            $table->timestamps();
        });

        $enrollmentStepsSeeder = new EnrollmentStepsSeeder;
        $enrollmentStepsSeeder->run();
    }


    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('enrollment_steps_new');
    }
};
