<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('twv_information', function (Blueprint $table) {
            $table->id();
            $table->foreignId('applicant_id')->references('applicant_id')->on('applicants');
            $table->timestamp('twv_requested_date')->nullable();
            $table->date('twv_approved_date')->nullable();
            $table->date('twv_start_date')->nullable();
            $table->date('twv_expiration_date')->nullable();
            $table->string('twv_issued_by')->nullable();
            $table->string('twv_number')->nullable();
            $table->string('twv_file_name')->nullable();
            $table->string('easyflex_document_number')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('twv_information');
    }
};
