{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework. ", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "ext-soap": "*", "bezhansalleh/filament-shield": "^3.2", "calebporzio/sushi": "^2.5", "filament/filament": "^3.2", "giggsey/libphonenumber-for-php": "^8.13", "laravel/framework": "^11.0", "laravel/octane": "^2.3", "laravel/tinker": "^2.9", "pxlrbt/filament-excel": "^2.3", "sentry/sentry-laravel": "^4.13", "spatie/laravel-activitylog": "^4.8", "spatie/laravel-data": "^4.6", "spatie/laravel-google-cloud-storage": "^2.3", "spatie/simple-excel": "^3.6", "wire-elements/modal": "^2.0", "z3d0x/filament-logger": "^0.7.2"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.15", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0.1", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}