diff --git a/.devcontainer.json b/.devcontainer.json
index 12d9a8c..4675447 100644
--- a/.devcontainer.json
+++ b/.devcontainer.json
@@ -42,7 +42,10 @@
 
     // Features to add to the dev container. More info: https://containers.dev/features.
 	"features": {
-		"ghcr.io/devcontainers/features/git": {}
+//        "ghcr.io/devcontainers/features/git:1": {
+//            "version": 2.39,
+//            "ppa": false
+//        }
 	},
 
 	// Configure tool-specific properties.
@@ -59,6 +62,30 @@
 			// "settings": {
 			// 	"dev.containers.mountWaylandSocket": false
 			// }
-		}
+		},
+        "jetbrains": {
+            "backend": "PhpStorm",
+            "settings": {
+                "com.intellij:app:EditorSettings.is_ensure_newline_at_eof": true,
+                "com.intellij:app:EditorSettings.remove_trailing_blank_lines": true,
+                "Git4Idea:app:Git-Application-Settings.use_credential_helper": true,
+                "Git4Idea:app:Git-Application-Settings.staging_area_enabled": true,
+                "Git4Idea:app:Git-Application-Settings.combined_stashes_and_shelves_enabled": true,
+                "Git4Idea:app:Git-Application-Settings.show_drop_commit_dialog": false,
+                "com.intellij:app:HttpConfigurable.use_proxy_pac": true,
+                "com.intellij:app:Vcs-Log-App-Settings.show_changes_from_parents": true,
+                "com.jetbrains.php:app:PhpCodeFoldingSettings.collapse_php_tags": true,
+                "com.jetbrains.php:app:PhpCodeFoldingSettings.collapse_here_docs": true,
+                "com.jetbrains.php:app:PhpCodeFoldingSettings.collapse_function_body": true,
+                "com.jetbrains.php:app:PhpCodeFoldingSettings.collapse_attribute": true,
+                "com.jetbrains.php:app:PhpCodeFoldingSettings.collapse_attribute_list": true
+            },
+            "plugins": [
+                "org.intellij.plugins.hcl",
+                "com.laravel_idea.plugin",
+                "com.kalessil.phpStorm.phpInspectionsEA",
+                "com.intellij.ml.llm"
+            ]
+        }
 	}
 }
